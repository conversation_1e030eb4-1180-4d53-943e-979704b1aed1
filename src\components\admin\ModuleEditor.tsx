import { useState, useEffect } from 'react';
import { Module, Lesson } from '@/types/academy';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Save,
  X,
  Plus,
  Trash,
  ChevronUp,
  ChevronDown,
  Edit,
  Video,
  FileText,
  Image,
  HelpCircle,
  Code,
  ExternalLink
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import LessonEditor from './LessonEditor';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface ModuleEditorProps {
  module: Module;
  onSave: (module: Module) => void;
  onCancel: () => void;
}

const ModuleEditor = ({ module, onSave, onCancel }: ModuleEditorProps) => {
  const [editableModule, setEditableModule] = useState<Module>({
    ...module,
    lessons: module.lessons || []
  });

  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);
  const [isLessonEditorOpen, setIsLessonEditorOpen] = useState(false);

  const handleChange = (field: string, value: any) => {
    setEditableModule(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(editableModule);
  };

  const handleAddLesson = () => {
    const newLesson: Lesson = {
      id: `lesson-${Date.now()}`,
      title: 'New Lesson',
      description: '',
      order: editableModule.lessons?.length || 0,
      isCompleted: false,
      contentBlocks: []
    };

    setSelectedLesson(newLesson);
    setIsLessonEditorOpen(true);
  };

  const handleEditLesson = (lesson: Lesson) => {
    setSelectedLesson({ ...lesson });
    setIsLessonEditorOpen(true);
  };

  const handleSaveLesson = (lesson: Lesson) => {
    setEditableModule(prev => {
      const updatedLessons = [...(prev.lessons || [])];
      const existingIndex = updatedLessons.findIndex(l => l.id === lesson.id);

      if (existingIndex >= 0) {
        updatedLessons[existingIndex] = lesson;
      } else {
        updatedLessons.push(lesson);
      }

      // Sort lessons by order
      updatedLessons.sort((a, b) => a.order - b.order);

      return {
        ...prev,
        lessons: updatedLessons
      };
    });

    setIsLessonEditorOpen(false);
  };

  const handleDeleteLesson = (lessonId: string) => {
    if (confirm('Are you sure you want to delete this lesson? This cannot be undone.')) {
      setEditableModule(prev => ({
        ...prev,
        lessons: prev.lessons?.filter(lesson => lesson.id !== lessonId) || []
      }));
    }
  };

  const handleMoveLesson = (lessonId: string, direction: 'up' | 'down') => {
    setEditableModule(prev => {
      const lessons = [...(prev.lessons || [])];
      const index = lessons.findIndex(l => l.id === lessonId);

      if (index < 0) return prev;

      const newIndex = direction === 'up' ? Math.max(0, index - 1) : Math.min(lessons.length - 1, index + 1);

      if (newIndex === index) return prev;

      // Swap order values
      const temp = lessons[index].order;
      lessons[index].order = lessons[newIndex].order;
      lessons[newIndex].order = temp;

      // Swap positions in array
      [lessons[index], lessons[newIndex]] = [lessons[newIndex], lessons[index]];

      return {
        ...prev,
        lessons
      };
    });
  };

  const calculateTotalDuration = () => {
    let totalMinutes = 0;
    editableModule.lessons?.forEach(lesson => {
      totalMinutes += lesson.duration || 0;
    });
    return totalMinutes;
  };

  useEffect(() => {
    // Update estimated duration based on lesson durations
    const totalDuration = calculateTotalDuration();
    setEditableModule(prev => ({
      ...prev,
      estimatedDuration: totalDuration
    }));
  }, [editableModule.lessons]);

  return (
    <div className="space-y-4">
      <Tabs defaultValue="basic">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic Details</TabsTrigger>
          <TabsTrigger value="lessons">Lessons & Content</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4 py-4">
          <form className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="module-title">Module Title</Label>
              <Input
                id="module-title"
                value={editableModule.title}
                onChange={(e) => handleChange('title', e.target.value)}
                placeholder="e.g., Master ChatGPT for Content Creation"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="module-description">Description</Label>
              <Textarea
                id="module-description"
                value={editableModule.description || ''}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Describe what students will learn in this module..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="module-xp">XP Reward</Label>
                <Input
                  id="module-xp"
                  type="number"
                  value={editableModule.xpReward}
                  onChange={(e) => handleChange('xpReward', parseInt(e.target.value))}
                  min={0}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="module-duration">Est. Duration (minutes)</Label>
                <Input
                  id="module-duration"
                  type="number"
                  value={editableModule.estimatedDuration || calculateTotalDuration()}
                  onChange={(e) => handleChange('estimatedDuration', parseInt(e.target.value))}
                  min={0}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="module-category">Category</Label>
              <Select
                value={editableModule.category || ''}
                onValueChange={(value) => handleChange('category', value)}
              >
                <SelectTrigger id="module-category">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ai-fundamentals">AI Fundamentals</SelectItem>
                  <SelectItem value="content-creation">Content Creation</SelectItem>
                  <SelectItem value="development">Development</SelectItem>
                  <SelectItem value="automation">Automation</SelectItem>
                  <SelectItem value="communication">Communication</SelectItem>
                  <SelectItem value="design">Design</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="module-image">Cover Image URL</Label>
              <Input
                id="module-image"
                value={editableModule.imageUrl || ''}
                onChange={(e) => handleChange('imageUrl', e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
              {editableModule.imageUrl && (
                <div className="mt-2 border rounded-lg overflow-hidden h-40 w-full">
                  <img
                    src={editableModule.imageUrl}
                    alt="Cover preview"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/images/placeholder.png';
                    }}
                  />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="module-video">Featured Video ID (YouTube)</Label>
              <Input
                id="module-video"
                value={editableModule.videoId || ''}
                onChange={(e) => handleChange('videoId', e.target.value)}
                placeholder="e.g., dQw4w9WgXcQ"
              />
              {editableModule.videoId && (
                <div className="mt-2 aspect-video w-full bg-sortmy-darker rounded-lg overflow-hidden border">
                  <iframe
                    src={`https://www.youtube.com/embed/${editableModule.videoId}`}
                    title="YouTube video"
                    className="w-full h-full"
                    allowFullScreen
                  ></iframe>
                </div>
              )}
            </div>
          </form>
        </TabsContent>

        <TabsContent value="lessons" className="space-y-4 py-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Module Lessons</h3>
            <Button onClick={handleAddLesson} variant="outline" className="bg-sortmy-blue/10 text-sortmy-blue hover:bg-sortmy-blue/20 border-sortmy-blue/20">
              <Plus className="mr-2 h-4 w-4" />
              Add Lesson
            </Button>
          </div>

          {editableModule.lessons?.length === 0 ? (
            <div className="bg-sortmy-darker/30 border border-dashed border-sortmy-blue/20 rounded-lg p-8 text-center">
              <div className="flex flex-col items-center gap-2">
                <FileText className="h-10 w-10 text-sortmy-blue/40" />
                <h3 className="text-lg font-medium">No Lessons Added Yet</h3>
                <p className="text-sm text-gray-400 max-w-md">
                  Start building your module by adding lessons. Each lesson can contain text, videos, images, quizzes, and code snippets.
                </p>
                <Button
                  onClick={handleAddLesson}
                  variant="outline"
                  className="mt-4 bg-sortmy-blue/10 text-sortmy-blue hover:bg-sortmy-blue/20 border-sortmy-blue/20"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Lesson
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {editableModule.lessons?.map((lesson, index) => (
                <Card key={lesson.id} className="bg-sortmy-darker/70 border-sortmy-blue/20">
                  <CardHeader className="p-4 pb-0">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-2">
                        <span className="flex items-center justify-center w-6 h-6 rounded-full bg-sortmy-blue/20 text-sortmy-blue text-xs font-medium">
                          {index + 1}
                        </span>
                        <CardTitle className="text-base">{lesson.title}</CardTitle>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-400"
                          onClick={() => handleMoveLesson(lesson.id, 'up')}
                          disabled={index === 0}
                        >
                          <ChevronUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-400"
                          onClick={() => handleMoveLesson(lesson.id, 'down')}
                          disabled={index === (editableModule.lessons?.length || 0) - 1}
                        >
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex -space-x-2">
                          {lesson.contentBlocks?.slice(0, 3).map((block, i) => (
                            <div
                              key={i}
                              className="w-6 h-6 rounded-full border border-sortmy-blue/20 flex items-center justify-center"
                              title={block.type}
                            >
                              {block.type === 'text' && <FileText className="h-3 w-3 text-blue-400" />}
                              {block.type === 'video' && <Video className="h-3 w-3 text-red-400" />}
                              {block.type === 'image' && <Image className="h-3 w-3 text-green-400" />}
                              {block.type === 'quiz' && <HelpCircle className="h-3 w-3 text-yellow-400" />}
                              {block.type === 'code' && <Code className="h-3 w-3 text-purple-400" />}
                              {block.type === 'embed' && <ExternalLink className="h-3 w-3 text-orange-400" />}
                            </div>
                          ))}
                          {lesson.contentBlocks && lesson.contentBlocks.length > 3 && (
                            <div className="w-6 h-6 rounded-full bg-sortmy-blue/10 border border-sortmy-blue/20 flex items-center justify-center">
                              <span className="text-xs text-sortmy-blue">+{lesson.contentBlocks.length - 3}</span>
                            </div>
                          )}
                        </div>
                        <span className="text-xs text-gray-400">
                          {lesson.duration || 0} min
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8"
                          onClick={() => handleEditLesson(lesson)}
                        >
                          <Edit className="h-3.5 w-3.5 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-red-500 hover:text-red-600 hover:bg-red-500/10"
                          onClick={() => handleDeleteLesson(lesson.id)}
                        >
                          <Trash className="h-3.5 w-3.5 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <Separator />

      <div className="flex justify-end space-x-2 pt-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button type="button" onClick={handleSubmit} className="bg-sortmy-blue hover:bg-sortmy-blue/90">
          <Save className="mr-2 h-4 w-4" />
          {editableModule.id ? 'Update Module' : 'Create Module'}
        </Button>
      </div>

      {/* Lesson Editor Dialog */}
      <Dialog open={isLessonEditorOpen} onOpenChange={setIsLessonEditorOpen}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedLesson?.id.startsWith('lesson-') ? 'Create New Lesson' : 'Edit Lesson'}</DialogTitle>
          </DialogHeader>
          {selectedLesson && (
            <LessonEditor
              lesson={selectedLesson}
              onSave={handleSaveLesson}
              onCancel={() => setIsLessonEditorOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ModuleEditor;
// This script loads the Firebase configuration into the service worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', async () => {
    try {
      // Default Firebase configuration
      const defaultConfig = {
        apiKey: "AIzaSyCSSBKFkrnBoK0b1Y3RmA97WdwcY9YLKcA",
        authDomain: "smai-og.firebaseapp.com",
        projectId: "smai-og",
        storageBucket: "smai-og.firebasestorage.app",
        messagingSenderId: "220186510992",
        appId: "1:220186510992:web:3d9e07c3df55d1f4ea7a15",
        measurementId: "G-4MR0WK595H"
      };

      // Check if we're in a preview environment
      const hostname = window.location.hostname;
      const isPreviewEnv = hostname.includes('preview.') || hostname.includes('localhost');

      let firebaseConfig;
      if (isPreviewEnv) {
        console.log('Running in preview environment, using default Firebase config');
        firebaseConfig = defaultConfig;
      } else {
        try {
          // For production, try to use environment variables
          firebaseConfig = {
            apiKey: window.__FIREBASE_CONFIG__?.apiKey || defaultConfig.apiKey,
            authDomain: window.__FIREBASE_CONFIG__?.authDomain || defaultConfig.authDomain,
            projectId: window.__FIREBASE_CONFIG__?.projectId || defaultConfig.projectId,
            storageBucket: window.__FIREBASE_CONFIG__?.storageBucket || defaultConfig.storageBucket,
            messagingSenderId: window.__FIREBASE_CONFIG__?.messagingSenderId || defaultConfig.messagingSenderId,
            appId: window.__FIREBASE_CONFIG__?.appId || defaultConfig.appId,
            measurementId: window.__FIREBASE_CONFIG__?.measurementId || defaultConfig.measurementId
          };
        } catch (error) {
          console.warn('Error accessing Firebase config:', error);
          console.log('Using default Firebase config as fallback');
          firebaseConfig = defaultConfig;
        }
      }

      // Unregister any existing service workers first
      const registrations = await navigator.serviceWorker.getRegistrations();
      await Promise.all(registrations.map(registration => registration.unregister()));

      // Register the new service worker
      const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');

      // Function to send config to service worker
      const sendConfigToServiceWorker = (serviceWorker) => {
        if (serviceWorker && serviceWorker.state !== 'redundant') {
          serviceWorker.postMessage({
            type: 'FIREBASE_CONFIG',
            config: firebaseConfig
          });
        }
      };

      // Send config to active and waiting service workers
      if (registration.active) {
        sendConfigToServiceWorker(registration.active);
      }
      if (registration.waiting) {
        sendConfigToServiceWorker(registration.waiting);
      }

      // Listen for new service workers
      registration.addEventListener('activate', (event) => {
        sendConfigToServiceWorker(event.target);
      });

      console.log('Service worker registered and configured successfully');
    } catch (error) {
      console.error('Service worker registration or configuration failed:', error);
    }
  });
}

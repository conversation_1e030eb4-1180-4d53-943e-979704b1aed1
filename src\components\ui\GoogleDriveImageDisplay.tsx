import { useState, useEffect } from 'react';
import { Image, ExternalLink, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getGoogleDriveFileId, getGoogleDriveImageUrls, isGoogleDriveUrl } from '@/utils/googleDriveUtils';
import { ImageItem } from './ImageItem';

interface GoogleDriveImageDisplayProps {
  src: string;
  alt: string;
  title?: string;
  className?: string;
}

export const GoogleDriveImageDisplay: React.FC<GoogleDriveImageDisplayProps> = ({
  src,
  alt,
  title,
  className
}) => {
  const [imageError, setImageError] = useState(false);
  const [attemptedUrls, setAttemptedUrls] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Reset error state when src changes
  useEffect(() => {
    setImageError(false);
    setAttemptedUrls([]);
    setIsLoading(true);
  }, [src]);

  // If it's not a Google Drive URL, use the regular ImageItem component
  if (!isGoogleDriveUrl(src)) {
    return (
      <ImageItem
        src={src}
        alt={alt}
        className={className}
      />
    );
  }

  const fileId = getGoogleDriveFileId(src);

  // If we can't extract a file ID, show error
  if (!fileId) {
    return (
      <div className={cn("h-full flex flex-col items-center justify-center bg-gray-800/50 p-4 text-center", className)}>
        <AlertCircle className="w-12 h-12 text-red-400 mb-2" />
        <p className="text-sm text-gray-300 mb-2">Invalid Google Drive URL</p>
        <p className="text-xs text-gray-400">Unable to extract file ID from the URL</p>
      </div>
    );
  }

  const urls = getGoogleDriveImageUrls(fileId);

  // Additional URL formats to try
  const allUrls = [
    urls.primary,
    urls.fallback,
    `https://drive.google.com/uc?export=view&id=${fileId}`,
    `https://drive.google.com/uc?id=${fileId}`,
  ];

  // If all URLs have failed, show the helpful fallback UI
  if (imageError && attemptedUrls.length >= allUrls.length) {
    return (
      <div className={cn("h-full flex flex-col items-center justify-center bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-4 text-center border border-gray-700/30 rounded-lg", className)}>
        <div className="bg-gray-700/50 rounded-full p-3 mb-3">
          <Image className="w-8 h-8 text-gray-400" />
        </div>
        <p className="text-sm text-gray-300 mb-2 font-medium">Image preview unavailable</p>
        <p className="text-xs text-gray-400 mb-4 max-w-xs">
          This Google Drive file may have restricted permissions. Click below to view it directly.
        </p>
        <div className="space-y-2">
          <a
            href={urls.view}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 hover:text-blue-300 px-3 py-2 rounded-md text-sm transition-colors border border-blue-500/30"
          >
            <ExternalLink className="w-4 h-4" />
            Open in Google Drive
          </a>
          {title && (
            <p className="text-xs text-gray-500 mt-2 italic">"{title}"</p>
          )}
        </div>
      </div>
    );
  }

  // Determine which URL to try
  const currentUrl = allUrls[attemptedUrls.length] || urls.primary;

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log(`GoogleDriveImageDisplay: Trying URL ${attemptedUrls.length + 1}/${allUrls.length}:`, currentUrl);
  }

  return (
    <div className={cn("h-full overflow-hidden relative", className)}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800/50 z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
        </div>
      )}
      <img
        src={currentUrl}
        alt={alt}
        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        onError={() => {
          const newAttemptedUrls = [...attemptedUrls, currentUrl];
          setAttemptedUrls(newAttemptedUrls);

          if (process.env.NODE_ENV === 'development') {
            console.log(`GoogleDriveImageDisplay: Failed to load URL ${newAttemptedUrls.length}/${allUrls.length}:`, currentUrl);
          }

          // If we haven't tried all URLs yet, don't set error
          if (newAttemptedUrls.length < allUrls.length) {
            // This will trigger a re-render with the next URL
            return;
          }

          // All URLs failed, show error UI
          if (process.env.NODE_ENV === 'development') {
            console.log('GoogleDriveImageDisplay: All URLs failed, showing error UI');
          }
          setImageError(true);
        }}
        onLoad={() => {
          // Image loaded successfully, reset error state
          if (process.env.NODE_ENV === 'development') {
            console.log('GoogleDriveImageDisplay: Successfully loaded:', currentUrl);
          }
          setImageError(false);
          setIsLoading(false);
        }}
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
      />
    </div>
  );
};

export default GoogleDriveImageDisplay;


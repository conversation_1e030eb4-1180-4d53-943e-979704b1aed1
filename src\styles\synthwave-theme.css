/* Synthwave Theme Design System */

:root {
  /* Base Colors - Using SortMyAI colors */
  --sortmy-blue: #0e96d5;
  --sortmy-dark: #121212;
  --sortmy-darker: #0a0a0a;
  --sortmy-gray: #2a2a2a;

  /* Synthwave accent colors for effects only */
  --synthwave-magenta: #ff00cc;
  --synthwave-cyan: #00ffff;
  --synthwave-purple: #2a003f;
  --synthwave-deep-black: #0d001a;

  /* Gradients */
  --aurora-gradient: linear-gradient(
    135deg,
    var(--sortmy-blue) 0%,
    var(--sortmy-dark) 40%,
    var(--sortmy-blue) 100%
  );

  --glass-gradient: linear-gradient(
    135deg,
    rgba(14, 150, 213, 0.1) 0%,
    rgba(42, 42, 42, 0.1) 100%
  );

  /* Shadows */
  --neon-shadow: 0 0 10px rgba(14, 150, 213, 0.5),
    0 0 20px rgba(14, 150, 213, 0.3);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --neumorphic-shadow: 8px 8px 16px rgba(0, 0, 0, 0.4),
    -8px -8px 16px rgba(42, 42, 42, 0.1);

  /* Animations */
  --pulse-animation: pulse 2s infinite;
  --float-animation: float 6s ease-in-out infinite;

  /* Blur */
  --glass-blur: blur(10px);
}

/* Animation Keyframes */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(14, 150, 213, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(14, 150, 213, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(14, 150, 213, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes glow {
  0% {
    filter: drop-shadow(0 0 2px var(--sortmy-blue));
  }
  50% {
    filter: drop-shadow(0 0 10px var(--sortmy-blue));
  }
  100% {
    filter: drop-shadow(0 0 2px var(--sortmy-blue));
  }
}

/* Utility Classes */

/* Backgrounds */
.bg-aurora {
  background: var(--aurora-gradient);
  animation: var(--float-animation);
  background-size: 400% 400%;
}

.bg-glass {
  background: rgba(10, 10, 10, 0.6);
  backdrop-filter: var(--glass-blur);
  border: 1px solid rgba(14, 150, 213, 0.2);
  box-shadow: var(--glass-shadow);
}

.bg-neumorphic {
  background: var(--sortmy-darker);
  box-shadow: var(--neumorphic-shadow);
  border-radius: 16px;
}

/* Buttons */
.btn-synthwave {
  background: var(--sortmy-darker);
  color: var(--sortmy-blue);
  border: 1px solid var(--sortmy-blue);
  border-radius: 8px;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.btn-synthwave:hover {
  box-shadow: var(--neon-shadow);
  transform: translateY(-2px);
  animation: var(--pulse-animation);
}

/* Cards */
.card-glass {
  background: rgba(10, 10, 10, 0.6);
  backdrop-filter: var(--glass-blur);
  border: 1px solid rgba(14, 150, 213, 0.2);
  border-radius: 12px;
  box-shadow: var(--glass-shadow);
  transition: all 0.3s ease;
}

.card-glass:hover {
  border-color: var(--sortmy-blue);
  box-shadow: 0 0 15px rgba(14, 150, 213, 0.5);
}

.card-neumorphic {
  background: var(--sortmy-darker);
  box-shadow: var(--neumorphic-shadow);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.card-neumorphic:hover {
  transform: translateY(-5px);
  box-shadow: 12px 12px 20px rgba(0, 0, 0, 0.5),
    -12px -12px 20px rgba(42, 42, 42, 0.2);
}

/* Text */
.text-glow {
  color: white;
  text-shadow: 0 0 5px var(--sortmy-blue), 0 0 10px var(--sortmy-blue);
}

.text-gradient {
  background: linear-gradient(90deg, var(--sortmy-blue), #4d94ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Loading Animations */
.skeleton-synthwave {
  background: linear-gradient(
    90deg,
    var(--sortmy-darker) 0%,
    var(--sortmy-gray) 50%,
    var(--sortmy-darker) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* XP Bar */
.xp-bar {
  height: 8px;
  background: var(--sortmy-darker);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.xp-bar-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--sortmy-blue), #4d94ff);
  border-radius: 4px;
  position: relative;
  transition: width 0.5s cubic-bezier(0.1, 0.9, 0.2, 1);
}

.xp-bar-progress::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 5px;
  background: white;
  opacity: 0.8;
  filter: blur(3px);
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: var(--neon-shadow);
}

.hover-glow {
  transition: filter 0.3s ease;
}

.hover-glow:hover {
  animation: glow 1.5s infinite;
}

.hover-pulse {
  transition: all 0.3s ease;
}

.hover-pulse:hover {
  animation: pulse 2s infinite;
}

/* Ambient Background */
.ambient-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.1;
  background: radial-gradient(
      circle at 20% 30%,
      var(--sortmy-blue),
      transparent 40%
    ),
    radial-gradient(circle at 80% 70%, var(--sortmy-blue), transparent 40%);
  filter: blur(60px);
  animation: float 10s ease-in-out infinite alternate;
}

/* Scanline Effect */
.bg-scanline {
  background-image: repeating-linear-gradient(
    to bottom,
    transparent,
    transparent 1px,
    rgba(255, 255, 255, 0.05) 1px,
    rgba(255, 255, 255, 0.05) 2px
  );
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Grid Pattern */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(14, 150, 213, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(14, 150, 213, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
  background-position: center center;
}

/* Scanline Animation */
@keyframes scanline {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

/* Subtle Pulse Animation for Notifications */
@keyframes pulse-subtle {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.scanline-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  pointer-events: none;
}

.scanline-effect::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  animation: scanline 6s linear infinite;
}

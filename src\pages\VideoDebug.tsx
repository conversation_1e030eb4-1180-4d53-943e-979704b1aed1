import { MuxPlayer } from '@/components/video/MuxPlayer';
import { MuxDebugPlayer } from '@/components/video/MuxDebugPlayer';
import { extractMuxPlaybackId, isMuxUrl, getMuxPlaybackUrl } from '@/lib/mux';

const VideoDebug = () => {
    const testUrl = 'https://stream.mux.com/6bqrqUIzLqHuxBFv7PL8LIugi02kM9eYocJckhiyYvOI.m3u8';
    const playbackId = extractMuxPlaybackId(testUrl);

    return (
        <div className="min-h-screen bg-gray-900 text-white p-6">
            <div className="max-w-6xl mx-auto space-y-8">
                <h1 className="text-3xl font-bold">Video Debug Page</h1>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Test with MuxPlayer */}
                    <div className="space-y-4">
                        <h2 className="text-xl font-semibold">MuxPlayer Component</h2>
                        <div className="aspect-video bg-black rounded">
                            <MuxPlayer
                                src={testUrl}
                                title="Test Video"
                                controls={true}
                                muted={false}
                                autoPlay={false}
                                className="w-full h-full"
                                onLoadedData={() => console.log('MuxPlayer: Video loaded')}
                                onPlay={() => console.log('MuxPlayer: Playing')}
                                onPause={() => console.log('MuxPlayer: Paused')}
                                onError={(error) => console.error('MuxPlayer: Error', error)}
                            />
                        </div>
                    </div>

                    {/* Test with Debug Player */}
                    <div className="space-y-4">
                        <h2 className="text-xl font-semibold">Debug Player</h2>
                        <MuxDebugPlayer
                            url={testUrl}
                            title="Debug Test Video"
                        />
                    </div>
                </div>

                {/* Test with playback ID */}
                <div className="space-y-4">
                    <h2 className="text-xl font-semibold">MuxPlayer with Playback ID</h2>
                    <div className="aspect-video bg-black rounded max-w-2xl">
                        <MuxPlayer
                            playbackId={playbackId || undefined}
                            title="Test Video (Playback ID)"
                            controls={true}
                            muted={false}
                            autoPlay={false}
                            className="w-full h-full"
                            onLoadedData={() => console.log('MuxPlayer (ID): Video loaded')}
                            onPlay={() => console.log('MuxPlayer (ID): Playing')}
                            onPause={() => console.log('MuxPlayer (ID): Paused')}
                            onError={(error) => console.error('MuxPlayer (ID): Error', error)}
                        />
                    </div>
                </div>

                {/* Native HTML5 Video Test */}
                <div className="space-y-4">
                    <h2 className="text-xl font-semibold">Native HTML5 Video</h2>
                    <div className="aspect-video bg-black rounded max-w-2xl">
                        <video
                            src={testUrl}
                            controls
                            playsInline
                            preload="metadata"
                            crossOrigin="anonymous"
                            className="w-full h-full"
                            onLoadedData={() => console.log('Native video: Loaded')}
                            onError={(e) => console.error('Native video: Error', e)}
                        >
                            <source src={testUrl} type="application/vnd.apple.mpegurl" />
                            Your browser doesn't support HLS video.
                        </video>
                    </div>
                </div>

                {/* Debug Information */}
                <div className="bg-gray-800 p-6 rounded-lg">
                    <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
                    <div className="space-y-2 text-sm font-mono">
                        <p><strong>Test URL:</strong> {testUrl}</p>
                        <p><strong>Is Mux URL:</strong> {isMuxUrl(testUrl) ? 'Yes' : 'No'}</p>
                        <p><strong>Extracted Playback ID:</strong> {playbackId || 'None'}</p>
                        <p><strong>Generated URL from ID:</strong> {playbackId ? getMuxPlaybackUrl(playbackId) : 'N/A'}</p>
                        <p><strong>Browser:</strong> {navigator.userAgent}</p>
                        <p><strong>HLS.js Support:</strong> {typeof window !== 'undefined' && 'Hls' in window ? 'Available' : 'Not Available'}</p>
                    </div>
                </div>

                {/* Instructions */}
                <div className="bg-blue-900/20 border border-blue-500/30 p-6 rounded-lg">
                    <h2 className="text-xl font-semibold mb-4">Testing Instructions</h2>
                    <ol className="list-decimal list-inside space-y-2">
                        <li>Open browser developer tools (F12)</li>
                        <li>Check the Console tab for debug messages</li>
                        <li>Try playing each video player above</li>
                        <li>Look for any error messages or network issues</li>
                        <li>Check the Network tab for failed requests</li>
                    </ol>
                </div>
            </div>
        </div>
    );
};

export default VideoDebug;

import React from 'react';
import { Message } from '@/types/message';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { handleMessageAction } from '@/services/messageService';
import { useToast } from '@/hooks/use-toast';

interface MessageBubbleProps {
  message: Message;
  isCurrentUser: boolean;
  senderAvatar?: string;
  senderName?: string;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isCurrentUser,
  senderAvatar,
  senderName
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const handleProfileClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (senderName && senderName !== 'U') {
      navigate(`/portfolio/${senderName}`);
    }
  };

  const handleButtonClick = async (action: string) => {
    try {
      await handleMessageAction(
        message.conversationId,
        message.receiverId, // Current user (who's clicking)
        message.senderId,   // Bot/system
        action
      );
    } catch (error) {
      console.error('Error handling button click:', error);
      toast({
        title: 'Error',
        description: 'Failed to process your selection. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const formattedTime = formatDistanceToNow(new Date(message.timestamp), { addSuffix: true });

  return (
    <div className={cn(
      "flex items-start gap-2 mb-4",
      isCurrentUser ? "flex-row-reverse" : "flex-row"
    )}>
      <Avatar className="h-8 w-8 flex-shrink-0 cursor-pointer" onClick={handleProfileClick}>
        <AvatarImage src={senderAvatar} />
        <AvatarFallback className="bg-sortmy-blue/20 text-sortmy-blue text-xs">
          {senderName ? getInitials(senderName) : 'U'}
        </AvatarFallback>
      </Avatar>

      <div className={cn(
        "max-w-[70%] rounded-lg p-3 shadow-sm",
        message.isInternshipResponse
          ? "bg-blue-600/20 border border-blue-500/40 text-white" // Special styling for internship responses
          : isCurrentUser
            ? "bg-sortmy-blue text-white rounded-tr-none"
            : "bg-sortmy-dark/50 border border-sortmy-blue/20 rounded-tl-none"
      )}>
        {/* For internship responses, we want to format the content differently */}
        {message.isInternshipResponse ? (
          <div>
            <div className="text-blue-400 font-semibold mb-2">Internship Program Information</div>
            <div className="text-white">
              {message.content.replace('[AUTOMATED INTERNSHIP RESPONSE]', '').trim()}
            </div>
          </div>
        ) : (
          message.content
        )}

        {message.isAutomatedResponse && message.buttons && (
          <div className="mt-3 flex gap-2">
            {message.buttons.map((button, index) => (
              <Button
                key={index}
                variant={button.action.includes('accept') || button.action === 'proceed' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleButtonClick(button.action)}
                className={cn(
                  button.action.includes('accept') || button.action === 'proceed'
                    ? "bg-green-500/10 hover:bg-green-500/20 border-green-500/30 text-green-500"
                    : "bg-red-500/10 hover:bg-red-500/20 border-red-500/30 text-red-500"
                )}
              >
                {button.label}
              </Button>
            ))}
          </div>
        )}

        {message.attachmentUrl && message.attachmentType === 'image' && (
          <div className="mt-2">
            <img
              src={message.attachmentUrl}
              alt="Attachment"
              className="rounded-md max-w-full max-h-60 object-contain"
            />
          </div>
        )}

        {message.attachmentUrl && message.attachmentType === 'file' && (
          <div className="mt-2">
            <a
              href={message.attachmentUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm flex items-center gap-1 text-sortmy-blue underline"
            >
              Attachment
            </a>
          </div>
        )}

        <div className={cn(
          "text-xs mt-1",
          isCurrentUser ? "text-blue-100" : "text-gray-400"
        )}>
          {formattedTime}
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;


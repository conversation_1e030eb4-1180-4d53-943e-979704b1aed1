import { storage } from '@/lib/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

export class CourseMediaService {
    static async uploadThumbnail(courseId: string, file: File): Promise<string> {
        const path = `course-media/${courseId}/thumbnail`;
        const storageRef = ref(storage, path);
        await uploadBytes(storageRef, file);
        return getDownloadURL(storageRef);
    }

    static async uploadLessonMedia(
        courseId: string,
        lessonId: string,
        file: File,
        type: 'image' | 'video'
    ): Promise<string> {
        const path = `course-lessons/${courseId}/${lessonId}/${type}`;
        const storageRef = ref(storage, path);
        await uploadBytes(storageRef, file);
        return getDownloadURL(storageRef);
    }

    static async uploadQuizImage(
        courseId: string,
        lessonId: string,
        questionId: string,
        file: File
    ): Promise<string> {
        const path = `course-lessons/${courseId}/${lessonId}/quiz/${questionId}`;
        const storageRef = ref(storage, path);
        await uploadBytes(storageRef, file);
        return getDownloadURL(storageRef);
    }
}

{"version": 3, "file": "web-Dfmdk10I.js", "sources": ["../../node_modules/@capacitor/app/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class AppWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.handleVisibilityChange = () => {\n            const data = {\n                isActive: document.hidden !== true,\n            };\n            this.notifyListeners('appStateChange', data);\n            if (document.hidden) {\n                this.notifyListeners('pause', null);\n            }\n            else {\n                this.notifyListeners('resume', null);\n            }\n        };\n        document.addEventListener('visibilitychange', this.handleVisibilityChange, false);\n    }\n    exitApp() {\n        throw this.unimplemented('Not implemented on web.');\n    }\n    async getInfo() {\n        throw this.unimplemented('Not implemented on web.');\n    }\n    async getLaunchUrl() {\n        return { url: '' };\n    }\n    async getState() {\n        return { isActive: document.hidden !== true };\n    }\n    async minimizeApp() {\n        throw this.unimplemented('Not implemented on web.');\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["AppWeb", "WebPlugin", "data"], "mappings": "uCACO,MAAMA,UAAeC,CAAU,CAClC,aAAc,CACV,MAAO,EACP,KAAK,uBAAyB,IAAM,CAChC,MAAMC,EAAO,CACT,SAAU,SAAS,SAAW,EACjC,EACD,KAAK,gBAAgB,iBAAkBA,CAAI,EACvC,SAAS,OACT,KAAK,gBAAgB,QAAS,IAAI,EAGlC,KAAK,gBAAgB,SAAU,IAAI,CAE1C,EACD,SAAS,iBAAiB,mBAAoB,KAAK,uBAAwB,EAAK,CACxF,CACI,SAAU,CACN,MAAM,KAAK,cAAc,yBAAyB,CAC1D,CACI,MAAM,SAAU,CACZ,MAAM,KAAK,cAAc,yBAAyB,CAC1D,CACI,MAAM,cAAe,CACjB,MAAO,CAAE,IAAK,EAAI,CAC1B,CACI,MAAM,UAAW,CACb,MAAO,CAAE,SAAU,SAAS,SAAW,EAAM,CACrD,CACI,MAAM,aAAc,CAChB,MAAM,KAAK,cAAc,yBAAyB,CAC1D,CACA", "x_google_ignoreList": [0]}
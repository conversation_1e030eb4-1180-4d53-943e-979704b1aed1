# Academy System Documentation

## Overview
The Academy system in SortMind Zenith provides a structured learning platform for AI tools and methodologies. It features course management, module organization, and progress tracking.

## Core Components

### 1. Course View (`src/pages/Academy/CourseView.tsx`)
Main course display component:
```typescript
function CourseView() {
  // Handles course content display
  // Manages module progression
  // Tracks completion status
  // Handles user interactions
}
```

Features:
- Module progression tracking
- Interactive content display
- Progress synchronization
- Achievement system integration

### 2. Course Editor (`src/pages/admin/CourseEditor.tsx`)
Admin interface for course management:
```typescript
function CourseEditor() {
  // Course content creation
  // Module organization
  // Media integration
  // Publishing controls
}
```

Features:
- Rich text editing
- Media embedding
- Module sequencing
- Version control

### 3. Course Manager (`src/pages/admin/CourseManager.tsx`)
Course administration dashboard:
```typescript
function CourseManager() {
  // Course listing
  // Batch operations
  // Analytics tracking
  // User progress monitoring
}
```

## Module System

### 1. Enhanced Module View (`src/pages/EnhancedModuleView.tsx`)
Module interaction component:
```typescript
interface Module {
  id: string;
  title: string;
  description: string;
  lessons: Lesson[];
  order: number;
  prerequisites: string[];
  completion_criteria: CompletionCriteria;
}

function EnhancedModuleView({ moduleId, onBack }) {
  // Module content rendering
  // Progress tracking
  // Interactive elements
  // Assessment handling
}
```

### 2. Module Editor (`src/components/admin/ModuleEditor.tsx`)
Module creation and editing interface:
```typescript
function ModuleEditor() {
  // Content structuring
  // Lesson organization
  // Resource management
  // Assessment creation
}
```

## Lesson Components

### 1. Lesson View (`src/components/academy/LessonView.tsx`)
Individual lesson display:
```typescript
interface Lesson {
  id: string;
  title: string;
  content: LessonContent;
  type: LessonType;
  duration: number;
  resources: Resource[];
}

function LessonView({ lesson, onComplete }) {
  // Content rendering
  // Progress tracking
  // Resource handling
  // Completion management
}
```

### 2. Lesson Editor (`src/components/admin/LessonEditor.tsx`)
Lesson creation interface:
```typescript
function LessonEditor() {
  // Content editing
  // Media integration
  // Resource attachment
  // Assessment configuration
}
```

## Service Layer

### Course Service (`src/services/courseService.ts`)
Course management operations:
```typescript
export const courseService = {
  // Course Operations
  createCourse: async (courseData: CourseData) => string,
  updateCourse: async (id: string, data: Partial<CourseData>) => void,
  deleteCourse: async (id: string) => void,
  publishCourse: async (id: string) => void,
  
  // Module Operations
  addModule: async (courseId: string, moduleData: ModuleData) => string,
  updateModule: async (courseId: string, moduleId: string, data: Partial<ModuleData>) => void,
  reorderModules: async (courseId: string, moduleOrder: string[]) => void,
  
  // Lesson Operations
  addLesson: async (moduleId: string, lessonData: LessonData) => string,
  updateLesson: async (moduleId: string, lessonId: string, data: Partial<LessonData>) => void,
  
  // Progress Tracking
  trackProgress: async (userId: string, courseId: string, progress: Progress) => void,
  getProgress: async (userId: string, courseId: string) => Progress,
}
```

### Progress Service (`src/services/progressService.ts`)
User progress tracking:
```typescript
export const progressService = {
  updateProgress: async (userId: string, courseId: string, moduleId: string, progress: number) => void,
  getModuleProgress: async (userId: string, moduleId: string) => ModuleProgress,
  getCourseProgress: async (userId: string, courseId: string) => CourseProgress,
  syncProgress: async (userId: string) => void,
}
```

## Types and Interfaces

### Course Types (`src/types/academy.ts`)
```typescript
export interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  modules: Module[];
  createdBy: string;
  published: boolean;
  prerequisites: string[];
  level: CourseLevel;
  duration: number;
  updatedAt: Timestamp;
}

export interface Module {
  id: string;
  title: string;
  description: string;
  lessons: Lesson[];
  order: number;
  duration: number;
  completion_criteria: CompletionCriteria;
}

export interface Lesson {
  id: string;
  title: string;
  content: LessonContent;
  type: LessonType;
  duration: number;
  resources: Resource[];
  quiz?: Quiz;
}

export interface Progress {
  userId: string;
  courseId: string;
  moduleProgress: Record<string, ModuleProgress>;
  completedLessons: string[];
  startedAt: Timestamp;
  lastAccessed: Timestamp;
  completed: boolean;
}
```

## Custom Hooks

### `src/hooks/useCourse.ts`
Course management hook:
```typescript
export function useCourse(courseId: string) {
  // Course data management
  // Progress tracking
  // Module navigation
  // State synchronization
}

export function useCourseList() {
  // Course listing
  // Filtering
  // Sorting
  // Search functionality
}

export function useUserCourses() {
  // Enrolled courses
  // Progress tracking
  // Recommendations
  // Achievement tracking
}
```

## UI Components

### Progress Components (`src/components/academy/progress/`)
```typescript
// Progress Bar
export function CourseProgressBar({ courseId, userId }) {
  // Visual progress tracking
  // Milestone indicators
  // Achievement display
}

// Progress Card
export function ModuleProgressCard({ moduleId, progress }) {
  // Module completion status
  // Time tracking
  // Achievement badges
}
```

### Interactive Components (`src/components/academy/interactive/`)
```typescript
// Quiz Component
export function QuizComponent({ quiz, onComplete }) {
  // Question rendering
  // Answer validation
  // Score tracking
  // Progress updates
}

// Practice Exercise
export function PracticeExercise({ exercise, onSubmit }) {
  // Exercise setup
  // Code execution
  // Result validation
  // Feedback display
}
```

## Feature Matrix

### Free Tier
- Access to basic courses
- Progress tracking
- Basic assessments
- Community support

### Premium Features (SortMyAI+)
- Advanced courses
- Interactive exercises
- Personal mentorship
- Certification paths
- Priority support
- Custom learning paths

## Error Handling

### Course Errors
```typescript
export class CourseError extends AppError {
  constructor(message: string) {
    super(message, 'COURSE_ERROR');
  }
}

export class ProgressError extends AppError {
  constructor(message: string) {
    super(message, 'PROGRESS_ERROR');
  }
}
```

## Security Implementation

### Access Control
```typescript
// Firestore Rules
match /courses/{courseId} {
  allow read: if true;
  allow write: if request.auth.uid != null && 
    (resource.data.createdBy == request.auth.uid || 
     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
}

match /progress/{progressId} {
  allow read, write: if request.auth.uid == resource.data.userId;
}
```

## Analytics Integration

### Course Analytics
```typescript
export const courseAnalytics = {
  trackCompletion: async (courseId: string, userId: string) => void,
  trackEngagement: async (courseId: string, metrics: EngagementMetrics) => void,
  generateReport: async (courseId: string) => AnalyticsReport,
}
```

## Maintenance Tasks

### Regular Tasks
- Content updates
- Progress synchronization
- Analytics processing
- Resource optimization
- Cache management

### Scheduled Tasks
- Course recommendations
- Progress notifications
- Achievement updates
- Engagement reminders

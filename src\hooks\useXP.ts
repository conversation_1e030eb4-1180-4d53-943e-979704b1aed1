import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { XPService } from '@/services/xpService';
import { ActivityType } from '@/utils/xpSystem';
import { useToast } from '@/hooks/use-toast';

interface XPResult {
  xpEarned: number;
  newLevel: number;
  leveledUp: boolean;
  streakBonus?: number;
  newStreak?: number;
}

interface XPProgress {
  totalXP: number;
  level: number;
  currentLevelXP: number;
  xpForNextLevel: number;
  progressPercentage: number;
  streakDays: number;
}

export const useXP = () => {
  const { user, refreshUser } = useAuth();
  const { toast } = useToast();
  const [isAwarding, setIsAwarding] = useState(false);

  // Award XP for any activity
  const awardXP = useCallback(async (
    activityType: ActivityType,
    description: string,
    metadata?: Record<string, any>
  ): Promise<XPResult | null> => {
    if (!user?.uid) {
      console.warn('No user found for XP award');
      return null;
    }

    setIsAwarding(true);
    try {
      const result = await XPService.awardXP(user.uid, activityType, description, metadata);

      // Refresh user data to get updated XP
      await refreshUser();

      // Show success toast
      toast({
        title: "XP Earned! 🎉",
        description: `+${result.xpEarned} XP - ${description}`,
      });

      // Show level up toast if applicable
      if (result.leveledUp) {
        toast({
          title: "Level Up! 🚀",
          description: `Congratulations! You've reached Level ${result.newLevel}!`,
        });
      }

      return result;
    } catch (error) {
      console.error('Error awarding XP:', error);
      toast({
        title: "Error",
        description: "Failed to award XP. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsAwarding(false);
    }
  }, [user?.uid, refreshUser, toast]);

  // Handle daily login
  const handleDailyLogin = useCallback(async (): Promise<XPResult | null> => {
    if (!user?.uid) return null;

    setIsAwarding(true);
    try {
      const result = await XPService.handleDailyLogin(user.uid);

      if (result.xpEarned > 0) {
        // Refresh user data
        await refreshUser();

        // Show login XP toast
        toast({
          title: "Daily Login Bonus! 🌟",
          description: `+${result.xpEarned} XP (${result.newStreak} day streak)`,
        });

        // Show streak bonus if applicable
        if (result.streakBonus > 0) {
          toast({
            title: "Streak Bonus! 🔥",
            description: `+${result.streakBonus} XP for your ${result.newStreak} day streak!`,
          });
        }

        // Show level up toast if applicable
        if (result.leveledUp) {
          toast({
            title: "Level Up! 🚀",
            description: `Congratulations! You've reached Level ${result.newLevel}!`,
          });
        }
      }

      return result;
    } catch (error) {
      console.error('Error handling daily login:', error);
      return null;
    } finally {
      setIsAwarding(false);
    }
  }, [user?.uid, refreshUser, toast]);

  // Get user XP progress
  const getXPProgress = useCallback(async (): Promise<XPProgress | null> => {
    if (!user?.uid) return null;

    try {
      return await XPService.getUserXPProgress(user.uid);
    } catch (error) {
      console.error('Error getting XP progress:', error);
      return null;
    }
  }, [user?.uid]);

  // Convenience methods for common activities
  const activities = {
    addToolToLibrary: useCallback((toolName: string) =>
      awardXP('ADD_TOOL_TO_LIBRARY', `Added ${toolName} to library`), [awardXP]),

    createTool: useCallback((toolName: string) =>
      awardXP('CREATE_TOOL', `Created tool: ${toolName}`), [awardXP]),

    createToolkit: useCallback((toolkitName: string) =>
      awardXP('CREATE_TOOLKIT', `Created toolkit: ${toolkitName}`), [awardXP]),

    createFirstPortfolio: useCallback(() =>
      awardXP('CREATE_FIRST_PORTFOLIO', 'Created first portfolio item'), [awardXP]),

    createPortfolioItem: useCallback((itemTitle: string) =>
      awardXP('CREATE_PORTFOLIO_ITEM', `Created portfolio: ${itemTitle}`), [awardXP]),

    completeLesson: useCallback((lessonTitle: string) =>
      awardXP('COMPLETE_LESSON', `Completed lesson: ${lessonTitle}`), [awardXP]),

    createPost: useCallback((postTitle: string) =>
      awardXP('CREATE_POST', `Created post: ${postTitle}`), [awardXP]),

    shareTool: useCallback((toolName: string) =>
      awardXP('SHARE_TOOL', `Shared tool: ${toolName}`), [awardXP]),

    likePost: useCallback((postTitle: string) =>
      awardXP('LIKE_POST', `Liked post: ${postTitle}`), [awardXP]),

    commentOnPost: useCallback((postTitle: string) =>
      awardXP('COMMENT_ON_POST', `Commented on: ${postTitle}`), [awardXP]),
  };

  return {
    // Core functions
    awardXP,
    handleDailyLogin,
    getXPProgress,

    // Activity shortcuts
    activities,

    // State
    isAwarding,

    // User XP data (from auth context)
    userXP: user?.xp || 0,
    userLevel: user?.level || 1,
    userStreak: user?.streak_days || 0,
  };
};

// Hook specifically for XP progress display
export const useXPProgress = () => {
  const { user } = useAuth();
  const [progress, setProgress] = useState<XPProgress | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const refreshProgress = useCallback(async () => {
    if (!user?.uid) return;

    setIsLoading(true);
    try {
      const progressData = await XPService.getUserXPProgress(user.uid);
      setProgress(progressData);
    } catch (error) {
      console.error('Error refreshing XP progress:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid]);

  return {
    progress,
    isLoading,
    refreshProgress,
  };
};

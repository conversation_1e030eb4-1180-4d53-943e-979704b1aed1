import { useState, useEffect } from 'react';
import { Lesson } from '@/types/academy';
import GlassCard from '@/components/ui/GlassCard';
import { Button } from '@/components/ui/button';
import { CheckCircle, ChevronDown, ChevronUp } from 'lucide-react';
import ContentBlockView from './ContentBlockView';
import QuizBlock from './QuizBlock';

interface LessonViewProps {
  lesson: Lesson;
  isCompleted: boolean;
  onComplete: () => void;
}

const LessonView = ({ lesson, isCompleted, onComplete }: LessonViewProps) => {
  const [expandedBlocks, setExpandedBlocks] = useState<{ [key: string]: boolean }>({});
  const [quizAttempts, setQuizAttempts] = useState<{ [key: string]: boolean }>({});
  const [canComplete, setCanComplete] = useState(isCompleted);

  // Initialize all blocks as expanded
  useEffect(() => {
    const initialExpandedState: { [key: string]: boolean } = {};
    lesson.contentBlocks?.forEach(block => {
      initialExpandedState[block.id] = true;
    });
    setExpandedBlocks(initialExpandedState);
  }, [lesson.contentBlocks]);

  // Check if all quizzes are completed
  useEffect(() => {
    if (isCompleted) {
      setCanComplete(true);
      return;
    }

    const quizBlocks = lesson.contentBlocks?.filter(block => block.type === 'quiz') || [];

    // If no quizzes, allow completion after viewing
    if (quizBlocks.length === 0) {
      setCanComplete(true);
      return;
    }

    // Check if all quizzes have been attempted
    const allQuizzesAttempted = quizBlocks.every(block => quizAttempts[block.id]);
    setCanComplete(allQuizzesAttempted);
  }, [quizAttempts, isCompleted, lesson.contentBlocks]);

  const toggleBlockExpansion = (blockId: string) => {
    setExpandedBlocks(prev => ({
      ...prev,
      [blockId]: !prev[blockId]
    }));
  };

  const handleQuizAttempt = (blockId: string, isCorrect: boolean) => {
    setQuizAttempts(prev => ({
      ...prev,
      [blockId]: isCorrect
    }));
  };

  return (
    <div className="space-y-6">
      <GlassCard className="p-6">
        <h2 className="text-xl font-bold mb-2">{lesson.title}</h2>
        {lesson.description && (
          <p className="text-gray-400 mb-4">{lesson.description}</p>
        )}

        {!lesson.contentBlocks || lesson.contentBlocks.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-gray-400">No content available for this lesson.</p>
          </div>
        ) : (
          <div className="space-y-6">
            {lesson.contentBlocks
              .sort((a, b) => a.order - b.order)
              .map((block) => (
                <div key={block.id} className="border border-sortmy-blue/20 rounded-lg overflow-hidden">
                  <div
                    className="bg-sortmy-blue/10 p-3 flex justify-between items-center cursor-pointer"
                    onClick={() => toggleBlockExpansion(block.id)}
                  >
                    <h3 className="text-md font-medium">
                      {block.type === 'text' && 'Text'}
                      {block.type === 'image' && 'Image'}
                      {block.type === 'video' && 'Video'}
                      {block.type === 'quiz' && 'Quiz Question'}
                      {block.type === 'code' && 'Code Example'}
                      {block.type === 'embed' && 'Embedded Content'}
                    </h3>
                    <button className="text-gray-400 hover:text-white">
                      {expandedBlocks[block.id] ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </button>
                  </div>

                  {expandedBlocks[block.id] && (
                    <div className="p-4">
                      {block.type === 'quiz' ? (
                        <QuizBlock
                          block={block}
                          onAttempt={(isCorrect) => handleQuizAttempt(block.id, isCorrect)}
                          isAttempted={!!quizAttempts[block.id]}
                        />
                      ) : block.type === "video" && block.content ? (
                        <div className="aspect-video w-full rounded overflow-hidden my-4">
                          {(block.content.includes("youtube.com") || block.content.includes("youtu.be")) ? (
                            <div className="p-4 text-center bg-sortmy-blue/10 rounded">
                              <p className="mb-2 text-white">
                                This video is hosted on YouTube and may be restricted from embedding.
                              </p>
                              <a
                                href={block.content}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-block bg-sortmy-blue px-4 py-2 text-white rounded hover:bg-sortmy-blue/90 transition"
                              >
                                Watch on YouTube
                              </a>
                            </div>
                          ) : (
                            <video src={block.content} controls className="w-full max-h-[480px] rounded" />
                          )}
                          {block.caption && (
                            <p className="text-sm mt-2 text-gray-300">{block.caption}</p>
                          )}
                        </div>
                      ) : (
                        <ContentBlockView block={block} />
                      )}
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}

        {!isCompleted && (
          <div className="mt-8 flex justify-center">
            <Button
              onClick={onComplete}
              disabled={!canComplete}
              className={canComplete ? "bg-sortmy-blue hover:bg-sortmy-blue/90" : "bg-gray-700"}
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              {canComplete ? "Mark as Completed" : "Complete All Quizzes"}
            </Button>
          </div>
        )}

        {isCompleted && (
          <div className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
            <div className="flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <p className="text-green-400">You've completed this lesson!</p>
            </div>
          </div>
        )}
      </GlassCard>
    </div>
  );
};

export default LessonView;
import { useQuery } from '@tanstack/react-query';
import { collection, query, where, getDocs, doc, updateDoc, arrayUnion, deleteDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { Bell } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

interface ToolkitNotification {
    id: string;
    userId: string;
    type: string;
    toolkitId?: string;
    createdAt?: string;
    read?: boolean;
}

export function Notifications() {
    const { user } = useAuth();
    const { toast } = useToast();
    const [open, setOpen] = useState(false);
    const [loadingAction, setLoadingAction] = useState<string | null>(null);

    const { data: notifications = [], refetch } = useQuery<ToolkitNotification[]>({
        queryKey: ['notifications', user?.uid],
        queryFn: async () => {
            if (!user?.uid) return [];
            const notificationsRef = collection(db, 'notifications');
            const q = query(notificationsRef, where('userId', '==', user.uid));
            const snap = await getDocs(q);
            return snap.docs.map(doc => ({ id: doc.id, ...doc.data() })) as ToolkitNotification[];
        },
        enabled: !!user?.uid,
        staleTime: 1000 * 60 * 5,
    });

    // Accept: add user to toolkit.shared_with, then delete notification
    const handleAccept = async (notification: ToolkitNotification) => {
        if (!notification.toolkitId || !user?.uid) return;
        setLoadingAction(notification.id);
        try {
            const toolkitRef = doc(db, 'toolkits', notification.toolkitId);
            const updatePayload = {
                shared_with: arrayUnion(user.uid),
                updated_at: new Date().toISOString(),
            };
            // Add detailed logging
            console.log('handleAccept: notification', notification);
            console.log('handleAccept: toolkitId', notification.toolkitId);
            console.log('handleAccept: user.uid', user.uid);
            console.log('handleAccept: updatePayload', updatePayload);

            await updateDoc(toolkitRef, updatePayload);
            await deleteDoc(doc(db, 'notifications', notification.id));
            refetch();
        } catch (err) {
            // Log the error object in detail
            console.error('Error accepting toolkit share:', err);
            toast({
                title: 'Error',
                description: 'Could not accept toolkit share. Please try again or contact support.',
                variant: 'destructive',
            });
        } finally {
            setLoadingAction(null);
        }
    };

    // Decline: just delete notification
    const handleDecline = async (notification: ToolkitNotification) => {
        setLoadingAction(notification.id);
        try {
            await deleteDoc(doc(db, 'notifications', notification.id));
            refetch();
        } catch (err) {
            // Optionally show error toast
        } finally {
            setLoadingAction(null);
        }
    };

    return (
        <div className="relative">
            <button onClick={() => setOpen(o => !o)} className="relative">
                <Bell className="w-6 h-6 text-white" />
                {notifications.length > 0 && (
                    <span className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full px-1">
                        {notifications.length}
                    </span>
                )}
            </button>
            {open && (
                <div className="absolute right-0 bottom-full mb-2 w-80 bg-sortmy-darker border border-sortmy-blue/20 rounded shadow-lg z-50">
                    <div className="p-4">
                        <h4 className="font-bold text-white mb-2">Notifications</h4>
                        {notifications.length === 0 && (
                            <div className="text-gray-400 text-sm">No notifications</div>
                        )}
                        {notifications.map((n) => (
                            <div key={n.id} className="mb-2">
                                {n.type === 'toolkit_share' && (
                                    <div>
                                        <span className="text-blue-400 font-semibold">Toolkit shared with you:</span>
                                        <Link
                                            to={`/dashboard/toolkits/${n.toolkitId}`}
                                            className="ml-2 text-sortmy-blue underline"
                                            onClick={() => setOpen(false)}
                                        >
                                            View Toolkit
                                        </Link>
                                        <div className="flex gap-2 mt-2">
                                            <button
                                                className="px-2 py-1 bg-green-600 text-white rounded text-xs disabled:opacity-60"
                                                onClick={() => handleAccept(n)}
                                                disabled={loadingAction === n.id}
                                            >
                                                {loadingAction === n.id ? 'Accepting...' : 'Accept'}
                                            </button>
                                            <button
                                                className="px-2 py-1 bg-red-600 text-white rounded text-xs disabled:opacity-60"
                                                onClick={() => handleDecline(n)}
                                                disabled={loadingAction === n.id}
                                            >
                                                {loadingAction === n.id ? 'Declining...' : 'Decline'}
                                            </button>
                                        </div>
                                    </div>
                                )}
                                {/* Add more notification types as needed */}
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}

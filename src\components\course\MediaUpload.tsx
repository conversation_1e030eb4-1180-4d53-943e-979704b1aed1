import React, { useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { storage } from '@/lib/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface MediaUploadProps {
    label: string;
    type: 'image' | 'video';
    value?: string;
    allowYouTube?: boolean;
    onChange: (url: string) => void;
    className?: string;
    courseId?: string;
    lessonId?: string;
}

export function MediaUpload({
    label,
    type,
    value,
    allowYouTube,
    onChange,
    className,
    courseId,
    lessonId
}: MediaUploadProps) {
    const [embedUrl, setEmbedUrl] = React.useState('');
    const [isUploading, setIsUploading] = React.useState(false);
    const [error, setError] = React.useState<string | null>(null);

    useEffect(() => {
        if (value && value.includes('youtube.com/embed')) {
            // Extract video ID from embed URL
            const videoId = value.split('/').pop();
            if (videoId) {
                setEmbedUrl(`https://www.youtube.com/watch?v=${videoId}`);
            }
        }
    }, [value]);

    const onDrop = useCallback(async (acceptedFiles: File[]) => {
        if (acceptedFiles.length === 0) return;

        const file = acceptedFiles[0];
        setIsUploading(true);
        setError(null);

        try {
            if (!courseId) {
                throw new Error('courseId is required for file uploads');
            }

            // Determine the storage path based on the type and context
            let path = `course-media/${courseId}`;
            if (lessonId) {
                path = `course-lessons/${courseId}/${lessonId}/${type}`;
            }

            const storageRef = ref(storage, `${path}/${file.name}`);
            await uploadBytes(storageRef, file);
            const url = await getDownloadURL(storageRef);
            onChange(url);
        } catch (error) {
            console.error('Upload error:', error);
            setError(error instanceof Error ? error.message : 'Failed to upload file');
        } finally {
            setIsUploading(false);
        }
    }, [courseId, lessonId, type, onChange]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'image/*': type === 'image' ? [] : [],
            'video/*': type === 'video' ? [] : [],
        },
        maxFiles: 1,
    });

    const handleEmbedSubmit = () => {
        if (!embedUrl) return;
        setError(null);

        // Extract video ID from YouTube URL
        const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = embedUrl.match(youtubeRegex);

        if (match && match[1]) {
            const videoId = match[1];
            const embedCode = `https://www.youtube.com/embed/${videoId}`;
            onChange(embedCode);
            setEmbedUrl('');
        } else {
            setError('Invalid YouTube URL');
        }
    };

    return (
        <div className={cn('space-y-2', className)}>
            <Label>{label}</Label>

            {value && (
                <div className="mb-4">
                    {type === 'image' ? (
                        <img src={value} alt={label} className="max-w-sm rounded-lg shadow-lg" />
                    ) : value.includes('youtube.com/embed') ? (
                        <div className="aspect-video max-w-2xl">
                            <iframe
                                src={value}
                                className="w-full h-full rounded-lg shadow-lg"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowFullScreen
                            />
                        </div>
                    ) : (
                        <video src={value} controls className="max-w-2xl rounded-lg shadow-lg">
                            Your browser does not support the video tag.
                        </video>
                    )}
                    <Button
                        variant="destructive"
                        className="mt-2"
                        onClick={() => onChange('')}
                    >
                        Remove {type === 'image' ? 'Image' : 'Video'}
                    </Button>
                </div>
            )}

            {!value && (
                <>
                    {type === 'video' && allowYouTube && (
                        <div className="space-y-2 mb-4">
                            <Label>YouTube Video URL</Label>
                            <div className="flex gap-2">
                                <Input
                                    value={embedUrl}
                                    onChange={(e) => setEmbedUrl(e.target.value)}
                                    placeholder="https://www.youtube.com/watch?v=..."
                                />
                                <Button onClick={handleEmbedSubmit}>
                                    Add Video
                                </Button>
                            </div>
                        </div>
                    )}

                    <div
                        {...getRootProps()}
                        className={cn(
                            'border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors',
                            isDragActive ? 'border-primary bg-primary/5' : 'border-gray-600',
                            'hover:border-primary hover:bg-primary/5'
                        )}
                    >
                        <input {...getInputProps()} />
                        <div className="text-center space-y-2">
                            {isUploading ? (
                                <p>Uploading...</p>
                            ) : isDragActive ? (
                                <p>Drop the file here</p>
                            ) : (
                                <>
                                    <p>Drag & drop a {type} here, or click to select</p>
                                    <p className="text-sm text-muted-foreground">
                                        {type === 'image'
                                            ? 'Supports: JPG, PNG, GIF, WebP (max 5MB)'
                                            : 'Supports: MP4, WebM (max 50MB)'}
                                    </p>
                                </>
                            )}
                        </div>
                    </div>
                </>
            )}

            {error && (
                <p className="text-sm text-destructive mt-2">{error}</p>
            )}
        </div>
    );
}

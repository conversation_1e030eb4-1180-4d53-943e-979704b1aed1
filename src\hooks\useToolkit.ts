import { useState, useCallback } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tool, Toolkit } from '@/types/tools';
import { User, AITool } from '@/types';

interface UseToolkitReturn {
    toolkit: Toolkit | null;
    tools: Tool[];
    creator: User | null;
    loading: boolean;
    error: Error | null;
    fetchToolkit: (id: string) => Promise<void>;
}

export function useToolkit(): UseToolkitReturn {
    const [toolkit, setToolkit] = useState<Toolkit | null>(null);
    const [tools, setTools] = useState<Tool[]>([]);
    const [creator, setCreator] = useState<User | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    const fetchToolkit = useCallback(async (id: string) => {
        console.log('useToolkit: Starting to fetch toolkit with id:', id);
        setLoading(true);
        setError(null);
        try {
            const toolkitDoc = await getDoc(doc(db, 'toolkits', id));
            if (!toolkitDoc.exists()) {
                throw new Error('Toolkit not found');
            }

            const data = toolkitDoc.data() as Toolkit;
            const toolkitWithId = { ...data, id: toolkitDoc.id };
            console.log('useToolkit: Toolkit data loaded:', toolkitWithId);
            setToolkit(toolkitWithId);            // Fetch tools data - check both collections
            const toolPromises = (data.tools || []).map(async (toolId: string) => {
                // First try aiTools collection
                let toolDoc = await getDoc(doc(db, 'aiTools', toolId));
                if (toolDoc.exists()) {
                    const aiToolData = toolDoc.data() as AITool;
                    // Transform AITool to Tool interface
                    return {
                        id: toolDoc.id,
                        name: aiToolData.name,
                        description: aiToolData.description,
                        logo_url: aiToolData.logoUrl,
                        logoUrl: aiToolData.logoUrl,
                        website: aiToolData.website,
                        tags: Array.isArray(aiToolData.tags) ? aiToolData.tags :
                            typeof aiToolData.tags === 'string' ? aiToolData.tags.split(',').map((tag: string) => tag.trim()) : [],
                        category: aiToolData.category,
                        created_at: aiToolData.createdAt || new Date().toISOString(),
                        updated_at: aiToolData.updatedAt,
                        user_id: aiToolData.createdBy,
                        pricing: aiToolData.pricing,
                        useCase: aiToolData.useCase,
                        excelsAt: aiToolData.excelsAt,
                        source: 'library' as const,
                        image_settings: aiToolData.imageSettings
                    } as Tool;
                }

                // Then try tools collection
                toolDoc = await getDoc(doc(db, 'tools', toolId));
                if (toolDoc.exists()) {
                    return { id: toolDoc.id, ...toolDoc.data() } as Tool;
                }

                return null;
            });

            const resolvedTools = await Promise.all(toolPromises);
            const validTools = resolvedTools.filter((t): t is Tool => t !== null);
            setTools(validTools);

            // Fetch creator data if available
            if (data.created_by) {
                try {
                    const userDoc = await getDoc(doc(db, 'users', data.created_by));
                    if (userDoc.exists()) {
                        const creatorData = { id: userDoc.id, ...userDoc.data() } as User;
                        setCreator(creatorData);
                    }
                } catch (creatorError) {
                    console.error('Error fetching creator:', creatorError);
                    // Continue without creator data
                }
            }
        } catch (err) {
            setError(err instanceof Error ? err : new Error('Failed to fetch toolkit'));
        } finally {
            console.log('useToolkit: Finished fetching, setting loading to false');
            setLoading(false);
        }
    }, []);

    return {
        toolkit,
        tools,
        creator,
        loading,
        error,
        fetchToolkit
    };
}

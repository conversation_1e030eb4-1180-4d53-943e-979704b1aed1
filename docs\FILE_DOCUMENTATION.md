# SortMind Zenith - File Level Documentation

## Core Files

### `src/App.tsx`
Main application file that handles:
- Routing configuration
- Application providers setup
- Authentication state management
- Automatic cleanup initialization
- Capacitor integration

Key functions:
```typescript
function App() {
  // Handles initialization of capacitor features
  // Sets up cleanup services
  // Manages auth state
  // Defines application routes
}
```

### `src/main.tsx`
Application entry point that:
- Initializes React application
- Sets up core providers:
  - FirebaseConnectionProvider
  - AuthProvider 
  - MessageNotificationProvider
  - ToastProvider
- Configures browser router
- Initializes Capacitor

## Authentication System

### `src/contexts/AuthContext.tsx`
Authentication context provider that:
- Manages user authentication state
- Handles sign-in/sign-out operations
- Provides user data across the application

Key functions:
```typescript
export function AuthProvider({ children }) {
  // Manages authentication state
  // Handles auth operations
  // Provides auth context to children
}

export function useAuth() {
  // Custom hook for accessing auth context
}
```

## Portfolio System

### `src/components/dashboard/Portfolio.tsx`
Main portfolio component that:
- Manages portfolio items display
- Handles CRUD operations
- Integrates with Firebase storage
- Manages media uploads

Key functions:
```typescript
function Portfolio() {
  // Fetches portfolio items
  // Handles item deletion
  // Manages media storage
  // Provides filtering and sorting
}
```

### `src/components/portfolio/EnhancedPortfolioForm.tsx`
Enhanced form component for portfolio management:
- Handles form validation
- Manages media uploads
- Implements auto-save functionality
- Provides draft management

Key functions:
```typescript
function EnhancedPortfolioForm() {
  // Form validation and submission
  // Media upload handling
  // Draft management
  // Auto-save functionality
}
```

### `src/services/portfolioStorageService.ts`
Service for portfolio storage operations:
- Manages file uploads
- Handles media optimization
- Implements cleanup routines
- Provides draft management

## Tool Management System

### `src/components/dashboard/CombinedToolTracker.tsx`
Main tool tracking component that:
- Manages personal and library tools
- Provides tool categorization
- Implements usage tracking
- Handles tool suggestions

Key functions:
```typescript
function CombinedToolTracker() {
  // Tool data management
  // Usage tracking
  // Category filtering
  // Search functionality
}
```

### `src/components/tools/ToolDetail.tsx`
Detailed tool view component:
- Displays tool information
- Manages tool interactions
- Handles usage statistics
- Provides integration options

Key functions:
```typescript
function ToolDetail() {
  // Tool data fetching
  // Usage statistics
  // Integration management
  // User interactions
}
```

## Toolkit System

### `src/components/toolkits/ToolkitDetail.tsx`
Toolkit detail component that:
- Displays toolkit information
- Manages tool collections
- Handles sharing functionality
- Tracks usage statistics

Key functions:
```typescript
function ToolkitDetail() {
  // Toolkit data management
  // Tool collection handling
  // Usage tracking
  // Sharing functionality
}
```

### `src/components/toolkits/ManageToolkit.tsx`
Toolkit management component:
- Handles toolkit creation/editing
- Manages tool associations
- Implements sharing options
- Provides version control

Key functions:
```typescript
function ManageToolkit() {
  // Toolkit CRUD operations
  // Tool association management
  // Sharing configuration
  // Version tracking
}
```

## Service Layer

### `src/services/toolService.ts`
Tool management service:
```typescript
export const toolService = {
  addTool: async (toolData) => {/* ... */},
  updateTool: async (id, data) => {/* ... */},
  deleteTool: async (id) => {/* ... */},
  getToolUsage: async (id) => {/* ... */}
}
```

### `src/services/toolkitService.ts`
Toolkit management service:
```typescript
export const toolkitService = {
  createToolkit: async (data) => {/* ... */},
  addToolToKit: async (kitId, toolId) => {/* ... */},
  removeToolFromKit: async (kitId, toolId) => {/* ... */},
  getToolkitStats: async (kitId) => {/* ... */}
}
```

## Context Providers

### `src/contexts/PortfolioContext.tsx`
Portfolio state management:
```typescript
export const PortfolioProvider = () => {
  // Manages portfolio items state
  // Handles portfolio operations
  // Provides portfolio context
}
```

### `src/contexts/BackgroundContext.tsx`
UI background management:
```typescript
export const BackgroundProvider = () => {
  // Manages background appearance
  // Handles theme changes
  // Provides background context
}
```

### `src/contexts/FirebaseConnectionContext.tsx`
Firebase connection management:
```typescript
export const FirebaseConnectionProvider = () => {
  // Manages Firebase connectivity
  // Handles offline/online states
  // Provides connection context
}
```

## Custom Hooks

### `src/hooks/useToolkit.ts`
Toolkit management hook:
```typescript
export function useToolkit() {
  // Manages toolkit state
  // Handles toolkit operations
  // Provides toolkit data
}
```

### `src/hooks/useAuth.ts`
Authentication hook:
```typescript
export function useAuth() {
  // Provides authentication context
  // Handles auth operations
  // Manages user state
}
```


import { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

interface FormData {
    name: string;
    description: string;
    logo_url: string;
    website_url: string;
    tags: string[];
    category: string;
}

const AddToolForm = () => {
    const location = useLocation();
    const { user } = useAuth();
    const { toast } = useToast();
    const uploadedImageUrl = location.state?.imageUrl;

    const [formData, setFormData] = useState<FormData>({
        name: '',
        description: '',
        logo_url: uploadedImageUrl || '',
        website_url: '',
        tags: [],
        category: ''
    });

    const handleImageUpload = async (file: File) => {
        try {
            const imageId = `${Date.now()}-${Math.random().toString(36).substring(2)}`;
            const storageRef = ref(storage, `user-tool-logos/${user?.uid}/${imageId}-${file.name}`);

            const metadata = {
                contentType: file.type,
                customMetadata: {
                    userId: user?.uid || '',
                    uploadedAt: new Date().toISOString()
                }
            };

            await uploadBytes(storageRef, file, metadata);
            const downloadUrl = await getDownloadURL(storageRef);

            setFormData(prev => ({
                ...prev,
                logo_url: downloadUrl
            }));

            toast({
                title: 'Success',
                description: 'Image uploaded successfully',
            });
        } catch (error) {
            console.error('Error uploading image:', error);
            toast({
                title: 'Error',
                description: 'Failed to upload image. Please try again.',
                variant: 'destructive',
            });
        }
    };

    return (
        <form>
            {/* ...existing form fields... */}

            {/* Show image preview if available */}
            {formData.logo_url && (
                <div className="mb-4">
                    <Label>Logo Preview</Label>
                    <div className="relative w-32 h-32 rounded-xl overflow-hidden">
                        <img
                            src={formData.logo_url}
                            alt="Tool logo"
                            className="w-full h-full object-contain"
                        />
                    </div>
                </div>
            )}

            {/* Allow changing the image */}
            <div className="mb-4">
                <Label>Logo URL</Label>
                <div className="flex gap-2">
                    <Input
                        value={formData.logo_url}
                        onChange={(e) => setFormData({ ...formData, logo_url: e.target.value })}
                        placeholder="Enter logo URL or use upload button"
                    />
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('logo-upload')?.click()}
                    >
                        Upload
                    </Button>
                    <input
                        id="logo-upload"
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                                handleImageUpload(file);
                            }
                        }}
                    />
                </div>
            </div>

            {/* ...rest of form... */}
        </form>
    );
};

export default AddToolForm;


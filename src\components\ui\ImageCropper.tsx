import { useState, useRef, useEffect } from 'react';
import { Button } from './button';
import { Slider } from './slider';
import { Label } from './label';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { ImageSettings, ImagePositionType } from '@/types';
import { toast } from 'sonner';

interface Position {
    x: number;
    y: number;
}

interface ImageCropperProps {
    imageUrl: string;
    onCropComplete: (downloadUrl: string, settings: ImageSettings) => void;
    initialSettings?: ImageSettings;
    onCancel: () => void;
    toolId?: string;
}

// Helper function to convert numeric position to ImagePositionType
const getPositionType = (x: number, y: number, containerSize: number): ImagePositionType => {
    const xPercent = (x / containerSize) * 100;
    const yPercent = (y / containerSize) * 100;

    if (Math.abs(xPercent) < 10 && Math.abs(yPercent) < 10) return 'center';
    if (xPercent < -30) {
        return yPercent < -30 ? 'top-left' : yPercent > 30 ? 'bottom-left' : 'left';
    }
    if (xPercent > 30) {
        return yPercent < -30 ? 'top-right' : yPercent > 30 ? 'bottom-right' : 'right';
    }
    return yPercent < -30 ? 'top' : yPercent > 30 ? 'bottom' : 'center';
};

export const ImageCropper: React.FC<ImageCropperProps> = ({
    imageUrl,
    onCropComplete,
    initialSettings,
    onCancel,
    toolId,
}): JSX.Element => {
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [scale, setScale] = useState(initialSettings?.scale || 1);
    const [rotate, setRotate] = useState(initialSettings?.rotate || 0);
    const [padding, setPadding] = useState(initialSettings?.padding || 0);
    const [position, setPosition] = useState<Position>({
        x: initialSettings?.crop?.x ?? 0,
        y: initialSettings?.crop?.y ?? 0
    });
    const imgRef = useRef<HTMLImageElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [loadedImage, setLoadedImage] = useState<string | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 });

    const loadImageWithFallbacks = async (url: string): Promise<string> => {
        try {
            if (url.startsWith('data:')) return url;

            if (url.startsWith('blob:')) {
                const response = await fetch(url);
                if (!response.ok) throw new Error('Failed to load blob URL');
                const blob = await response.blob();
                return URL.createObjectURL(blob);
            }

            if (url.includes('firebasestorage.googleapis.com') || url.includes('storage.googleapis.com')) {
                const storage = getStorage();
                const urlPath = url.split('/o/')[1]?.split('?')[0];
                if (!urlPath) throw new Error('Invalid Firebase Storage URL');

                const decodedPath = decodeURIComponent(urlPath);
                const imageRef = ref(storage, decodedPath);
                const firebaseUrl = await getDownloadURL(imageRef);

                const response = await fetch(firebaseUrl);
                if (!response.ok) throw new Error('Failed to load Firebase Storage image');
                const blob = await response.blob();
                return URL.createObjectURL(blob);
            }

            const proxyUrl = `https://images.weserv.nl/?url=${encodeURIComponent(url)}`;
            const response = await fetch(proxyUrl);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const blob = await response.blob();
            return URL.createObjectURL(blob);
        } catch (error) {
            console.error('Error loading image:', error);
            throw error;
        }
    };

    const handleMouseDown = (e: React.MouseEvent) => {
        if (!containerRef.current) return;
        setIsDragging(true);
        setDragStart({
            x: e.clientX - position.x,
            y: e.clientY - position.y
        });
        e.preventDefault();
    };

    const handleMouseMove = (e: React.MouseEvent) => {
        if (!isDragging || !containerRef.current || !imgRef.current) return;

        const containerRect = containerRef.current.getBoundingClientRect();
        const containerSize = containerRect.width;
        const scaledImgSize = containerSize * scale;

        // Allow movement based on scale factor
        let moveLimit;
        if (scale <= 1) {
            const movementRange = containerSize * 0.1;
            const scaleFactor = (scale - 0.1) / 0.9;
            moveLimit = movementRange * scaleFactor;
        } else {
            moveLimit = (scaledImgSize - containerSize) / 2;
        }

        const newX = e.clientX - dragStart.x;
        const newY = e.clientY - dragStart.y;

        const clampedX = Math.max(-moveLimit, Math.min(moveLimit, newX));
        const clampedY = Math.max(-moveLimit, Math.min(moveLimit, newY));

        setPosition({ x: clampedX, y: clampedY });
        e.preventDefault();
    };

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    const captureImage = async (): Promise<Blob> => {
        if (!containerRef.current || !imgRef.current) {
            throw new Error('Missing references');
        }

        const canvas = document.createElement('canvas');
        const containerSize = containerRef.current.clientWidth;
        canvas.width = containerSize;
        canvas.height = containerSize;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('No 2d context');
        }

        const img = imgRef.current;

        ctx.drawImage(
            img,
            -position.x / scale,
            -position.y / scale,
            containerSize / scale,
            containerSize / scale,
            0,
            0,
            containerSize,
            containerSize
        );

        return new Promise((resolve, reject) => {
            canvas.toBlob(
                (blob) => {
                    if (blob) resolve(blob);
                    else reject(new Error('Failed to create blob'));
                },
                'image/png',
                1
            );
        });
    };

    useEffect(() => {
        let mounted = true;
        let tempUrl: string | null = null;

        const loadImage = async () => {
            if (!mounted) return;

            setIsLoading(true);
            setError(null);

            try {
                const loadedImageUrl = await loadImageWithFallbacks(imageUrl);
                if (!mounted) return;

                tempUrl = loadedImageUrl;
                setLoadedImage(loadedImageUrl);
            } catch (err) {
                if (!mounted) return;
                console.error('Error loading image:', err);
                setError('Failed to load image. Please check the image URL and try again.');
            } finally {
                if (mounted) {
                    setIsLoading(false);
                }
            }
        };

        loadImage();

        return () => {
            mounted = false;
            if (tempUrl?.startsWith('blob:')) {
                URL.revokeObjectURL(tempUrl);
            }
        };
    }, [imageUrl]);

    const uploadToFirebase = async (blob: Blob): Promise<string> => {
        const storage = getStorage();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}`;
        const storagePath = toolId
            ? `tool-logos/${toolId}/${fileName}.png`
            : `uploads/${fileName}.png`;

        const storageRef = ref(storage, storagePath);
        const metadata = {
            contentType: 'image/png',
            cacheControl: 'public,max-age=31536000',
        };

        try {
            await uploadBytes(storageRef, blob, metadata);
            return await getDownloadURL(storageRef);
        } catch (error) {
            console.error('Error uploading to Firebase:', error);
            throw error;
        }
    };

    const handleSave = async () => {
        if (!imgRef.current || !containerRef.current) {
            toast.error('Image not ready');
            return;
        }

        try {
            setIsLoading(true);
            setError(null);

            const blob = await captureImage();
            const downloadUrl = await uploadToFirebase(blob);
            const containerSize = containerRef.current.clientWidth;

            const positionType = getPositionType(position.x, position.y, containerSize);

            const settings: ImageSettings = {
                size: 'contain',
                position: positionType,
                padding,
                scale,
                rotate,
                crop: {
                    unit: '%',
                    x: position.x,
                    y: position.y,
                    width: 100,
                    height: 100
                }
            };

            onCropComplete(downloadUrl, settings);
        } catch (err) {
            console.error('Error saving image:', err);
            setError('Failed to save image. Please try again.');
            toast.error('Failed to save image');
        } finally {
            setIsLoading(false);
        }
    }; return (
        <div className="flex flex-col gap-4 p-4">
            {error && <div className="text-red-500">{error}</div>}

            <div
                ref={containerRef}
                className="relative w-full aspect-square overflow-hidden bg-gray-100 rounded-lg cursor-move"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
            >
                {loadedImage && (
                    <img
                        ref={imgRef}
                        src={loadedImage}
                        className="absolute transform-gpu" style={{
                            transform: `translate(${position.x}px, ${position.y}px) scale(${scale}) rotate(${rotate}deg)`,
                            transformOrigin: 'center',
                            maxWidth: 'none',
                            padding: `${padding}px`
                        }}
                        crossOrigin="anonymous"
                        onLoad={() => setIsLoading(false)}
                        onError={() => {
                            setError('Failed to load image');
                            setIsLoading(false);
                        }}
                        draggable={false}
                        alt="Tool logo"
                    />
                )}
            </div>

            <div className="flex flex-col gap-2">
                <Label>Scale</Label>
                <Slider
                    value={[scale]}
                    onValueChange={(value) => setScale(value[0])}
                    min={0.1}
                    max={3}
                    step={0.1}
                />
            </div>

            <div className="flex flex-col gap-2">
                <Label>Rotate</Label>
                <Slider
                    value={[rotate]}
                    onValueChange={(value) => setRotate(value[0])}
                    min={-180}
                    max={180}
                    step={1}
                />
            </div>

            <div className="flex flex-col gap-2">
                <Label>Padding</Label>
                <Slider
                    value={[padding]}
                    onValueChange={(value) => setPadding(value[0])}
                    min={0}
                    max={50}
                    step={1}
                />
            </div>

            <div className="flex gap-2">
                <Button
                    variant="outline"
                    onClick={onCancel}
                    disabled={isLoading}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleSave}
                    disabled={isLoading || !loadedImage}
                >
                    {isLoading ? 'Saving...' : 'Save'}
                </Button>
            </div>
        </div>
    );
};

export default ImageCropper;

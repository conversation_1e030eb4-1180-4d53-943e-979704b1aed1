{"version": 3, "file": "sw.js", "sources": ["../AppData/Local/Temp/46a4249df39d6e7267b2b964ca3f75fe/sw.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-routing/registerRoute.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {CacheFirst as workbox_strategies_CacheFirst} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-strategies/CacheFirst.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from 'C:/Users/<USER>/sortmind-zenith/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"assets/firebase-messaging-sw-BoHq4HdZ.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/main-8XFua-u6.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/main-nBR2mc0g.css\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/web-_VCTtvUu.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/web-D2G9elIF.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/web-Dfmdk10I.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/web-qcnismWP.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"firebase-messaging-sw-loader.js\",\n    \"revision\": \"fa725ea355a6c2a941a491db4b9c8c3f\"\n  },\n  {\n    \"url\": \"firebase-messaging-sw.js\",\n    \"revision\": \"262d46c138d8eaabd7147c34b59ba8ed\"\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"d44e297cdc5f75280da4ef29613f1686\"\n  },\n  {\n    \"url\": \"lovable-preview-fix.js\",\n    \"revision\": \"74bce8dca617158ab312799366d5a14b\"\n  },\n  {\n    \"url\": \"registerSW.js\",\n    \"revision\": \"1872c500de691dce40960bb85481de07\"\n  },\n  {\n    \"url\": \"manifest.webmanifest\",\n    \"revision\": \"19e913e61d2539e89f3702333a44a4c0\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"index.html\")));\n\n\nworkbox_routing_registerRoute(/^https:\\/\\/www\\.gstatic\\.com\\/.*/i, new workbox_strategies_CacheFirst({ \"cacheName\":\"google-static\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 50, maxAgeSeconds: 2592000 })] }), 'GET');\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_precaching_cleanupOutdatedCaches", "workbox", "registerRoute", "workbox_routing_NavigationRoute", "workbox_precaching_createHandlerBoundToURL", "workbox_routing_registerRoute", "workbox_strategies_CacheFirst", "cacheName", "plugins", "workbox_expiration_ExpirationPlugin", "maxEntries", "maxAgeSeconds"], "mappings": "0nBA0BAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,2CACPC,SAAY,MAEd,CACED,IAAO,0BACPC,SAAY,MAEd,CACED,IAAO,2BACPC,SAAY,MAEd,CACED,IAAO,yBACPC,SAAY,MAEd,CACED,IAAO,yBACPC,SAAY,MAEd,CACED,IAAO,yBACPC,SAAY,MAEd,CACED,IAAO,yBACPC,SAAY,MAEd,CACED,IAAO,kCACPC,SAAY,oCAEd,CACED,IAAO,2BACPC,SAAY,oCAEd,CACED,IAAO,aACPC,SAAY,oCAEd,CACED,IAAO,yBACPC,SAAY,oCAEd,CACED,IAAO,gBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,qCAEb,CAAE,GACLC,EAAAA,wBAC6BC,EAAAC,cAAC,IAAIC,EAAAA,gBAAgCC,EAAAA,wBAA2C,gBAG7GC,EAAAA,cAA8B,oCAAqC,IAAIC,aAA8B,CAAEC,UAAY,gBAAiBC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,GAAIC,cAAe,YAAgB"}
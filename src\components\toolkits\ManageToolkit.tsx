import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { db, storage } from '@/lib/firebase';
import { doc, getDoc, updateDoc, collection, getDocs, query, where } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { X, Plus, <PERSON><PERSON>ef<PERSON>, Save, Settings, Upload, Loader2 } from 'lucide-react';
import GlassCard from '@/components/ui/GlassCard';
import NeonButton from '@/components/ui/NeonButton';
import ClickEffect from '@/components/ui/ClickEffect';
import { ToolSelector } from './ToolSelector';
import { Tool, Toolkit } from '@/types/tools';

const formSchema = z.object({
  name: z.string().min(2, { message: "Toolkit name must be at least 2 characters" }),
  description: z.string().min(10, { message: "Description must be at least 10 characters" }),
  logo_url: z.string().url({ message: "Please enter a valid logo URL" }).optional(),
  tags: z.array(z.string()).min(1, { message: "Add at least one tag" }),
  tools: z.array(z.string()).min(1, { message: "Select at least one tool" }),
});

const ManageToolkit = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [toolkit, setToolkit] = useState<Toolkit | null>(null);
  const [availableTools, setAvailableTools] = useState<Tool[]>([]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      logo_url: "",
      tags: [],
      tools: [],
    },
  });

  // Load toolkit data and available tools
  useEffect(() => {
    const loadData = async () => {
      if (!id || !user) return;

      try {
        setIsLoading(true);

        // Load toolkit
        const toolkitDoc = await getDoc(doc(db, 'toolkits', id));
        if (!toolkitDoc.exists()) {
          toast({
            title: "Toolkit not found",
            description: "The toolkit you're trying to edit doesn't exist.",
            variant: "destructive",
          });
          navigate('/dashboard/tools');
          return;
        }

        const toolkitData = { id: toolkitDoc.id, ...toolkitDoc.data() } as Toolkit;

        // Check if user owns this toolkit
        if (toolkitData.created_by !== user.uid) {
          toast({
            title: "Access denied",
            description: "You don't have permission to edit this toolkit.",
            variant: "destructive",
          });
          navigate('/dashboard/tools');
          return;
        }

        setToolkit(toolkitData);

        // Set form values
        form.setValue('name', toolkitData.name);
        form.setValue('description', toolkitData.description || '');
        form.setValue('logo_url', toolkitData.logo_url || '');
        form.setValue('tags', toolkitData.tags || []);
        form.setValue('tools', toolkitData.tools || []);

        // Load available tools (user tools + library tools)
        const [userToolsSnapshot, libraryToolsSnapshot] = await Promise.all([
          getDocs(query(collection(db, 'tools'), where('user_id', '==', user.uid))),
          getDocs(collection(db, 'aiTools'))
        ]);

        const userTools = userToolsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Tool[];

        const libraryTools = libraryToolsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          source: 'library'
        })) as Tool[];

        setAvailableTools([...userTools, ...libraryTools]);

      } catch (error) {
        console.error('Error loading toolkit:', error);
        toast({
          title: "Error",
          description: "Failed to load toolkit data.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [id, user, form, navigate, toast]);

  const addTag = () => {
    if (newTag.trim() && !form.getValues().tags.includes(newTag.trim())) {
      form.setValue('tags', [...form.getValues().tags, newTag.trim()]);
      setNewTag('');
      form.clearErrors('tags');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const updatedTags = form.getValues().tags.filter(tag => tag !== tagToRemove);
    form.setValue('tags', updatedTags);
  };

  const handleImageUpload = async (file: File) => {
    if (!user || !id) return;

    try {
      setIsUploading(true);
      const storageRef = ref(storage, `toolkit-logos/${user.uid}/${id}/${file.name}`);
      const result = await uploadBytes(storageRef, file);
      const url = await getDownloadURL(result.ref);
      form.setValue('logo_url', url);

      toast({
        title: "Image uploaded",
        description: "Logo has been uploaded successfully.",
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Upload failed",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!user || !id) return;

    try {
      setIsSubmitting(true);

      // Update toolkit in Firestore
      await updateDoc(doc(db, 'toolkits', id), {
        name: values.name,
        description: values.description,
        logo_url: values.logo_url || '',
        tags: values.tags,
        tools: values.tools,
        updated_at: new Date().toISOString(),
      });

      toast({
        title: "Toolkit updated",
        description: "Your toolkit has been updated successfully.",
      });

      navigate(`/dashboard/toolkits/${id}`);
    } catch (error: any) {
      console.error('Error updating toolkit:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update toolkit. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2 text-white">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading toolkit...</span>
        </div>
      </div>
    );
  }

  if (!toolkit) {
    return (
      <div className="text-center text-white">
        <p>Toolkit not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <NeonButton variant="cyan" className="mb-4" onClick={() => navigate(`/dashboard/toolkits/${id}`)}>
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Toolkit
      </NeonButton>

      <GlassCard variant="bordered" className="border-sortmy-blue/20">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2 text-sortmy-blue" />
            <span className="bg-gradient-to-r from-sortmy-blue to-[#4d94ff] text-transparent bg-clip-text">
              Manage Toolkit
            </span>
          </CardTitle>
          <CardDescription>
            Update your toolkit details, tools, and settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Toolkit Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. Content Creation Toolkit" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="logo_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Logo URL</FormLabel>
                        <FormControl>
                          <div className="space-y-2">
                            <Input placeholder="https://example.com/logo.png" {...field} />
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-gray-400">or</span>
                              <label className="cursor-pointer">
                                <input
                                  type="file"
                                  accept="image/*"
                                  className="hidden"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) handleImageUpload(file);
                                  }}
                                  disabled={isUploading}
                                />
                                <ClickEffect effect="ripple" color="blue">
                                  <NeonButton
                                    type="button"
                                    variant="cyan"
                                    size="sm"
                                    disabled={isUploading}
                                  >
                                    {isUploading ? (
                                      <>
                                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                        Uploading...
                                      </>
                                    ) : (
                                      <>
                                        <Upload className="w-4 h-4 mr-1" />
                                        Upload
                                      </>
                                    )}
                                  </NeonButton>
                                </ClickEffect>
                              </label>
                            </div>
                          </div>
                        </FormControl>
                        <FormDescription>
                          Enter the URL of the toolkit's logo image or upload one
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe what this toolkit is for and how it helps..."
                            className="min-h-32 resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Tags Section */}
              <FormField
                control={form.control}
                name="tags"
                render={() => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <div className="space-y-4">
                      <div className="flex flex-wrap gap-2">
                        {form.getValues().tags.map((tag, i) => (
                          <div
                            key={i}
                            className="bg-sortmy-blue/20 text-white px-3 py-1 rounded-full flex items-center gap-1 transition-colors hover:bg-sortmy-blue/30"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="w-4 h-4 rounded-full bg-sortmy-gray/50 flex items-center justify-center hover:bg-sortmy-blue/50 transition-colors"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </div>
                        ))}
                      </div>

                      <div className="flex gap-2">
                        <Input
                          placeholder="Add a tag..."
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addTag();
                            }
                          }}
                        />
                        <ClickEffect effect="ripple" color="blue">
                          <NeonButton
                            type="button"
                            variant="cyan"
                            onClick={addTag}
                          >
                            <Plus className="w-4 h-4 mr-1" /> Add
                          </NeonButton>
                        </ClickEffect>
                      </div>
                    </div>
                    <FormDescription>
                      Add tags like "productivity", "design", "content creation", etc.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tools Selection */}
              <FormField
                control={form.control}
                name="tools"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select Tools</FormLabel>
                    <FormControl>
                      <ToolSelector
                        availableTools={availableTools}
                        selectedTools={field.value}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormDescription>
                      Choose the tools that belong to this toolkit
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4">
                <ClickEffect effect="ripple" color="gray">
                  <NeonButton
                    type="button"
                    variant="outline"
                    onClick={() => navigate(`/dashboard/toolkits/${id}`)}
                  >
                    Cancel
                  </NeonButton>
                </ClickEffect>

                <ClickEffect effect="particles" color="blue">
                  <NeonButton
                    type="submit"
                    disabled={isSubmitting}
                    variant="gradient"
                    className="w-full md:w-auto"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isSubmitting ? 'Updating...' : 'Update Toolkit'}
                  </NeonButton>
                </ClickEffect>
              </div>
            </form>
          </Form>
        </CardContent>
      </GlassCard>
    </div>
  );
};

export default ManageToolkit;


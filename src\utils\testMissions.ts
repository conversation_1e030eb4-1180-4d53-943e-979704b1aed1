// Test utility to verify mission system is working
import { MissionService, MissionTracker } from '@/services/missionService';

export const testMissionSystem = async (userId: string) => {
  console.log('🧪 Testing Mission System for user:', userId);

  try {
    // Test 1: Get user missions
    console.log('📋 Test 1: Getting user missions...');
    const missions = await MissionService.getUserMissions(userId);
    console.log('✅ Found missions:', missions.length);
    missions.forEach(mission => {
      console.log(`  - ${mission.title}: ${mission.progress}/${mission.maxProgress} (${mission.isCompleted ? 'Complete' : 'In Progress'})`);
    });

    // Test 2: Track adding a tool to library
    console.log('\n🔧 Test 2: Tracking tool addition...');
    await MissionTracker.addToolToLibrary(userId);
    console.log('✅ Tool addition tracked');

    // Test 3: Track portfolio creation
    console.log('\n📁 Test 3: Tracking portfolio creation...');
    await MissionTracker.createPost(userId);
    console.log('✅ Portfolio creation tracked');

    // Test 4: Track daily login
    console.log('\n🌅 Test 4: Tracking daily login...');
    await MissionTracker.dailyLogin(userId);
    console.log('✅ Daily login tracked');

    // Test 5: Get updated missions
    console.log('\n📋 Test 5: Getting updated missions...');
    const updatedMissions = await MissionService.getUserMissions(userId);
    console.log('✅ Updated missions retrieved');
    updatedMissions.forEach(mission => {
      console.log(`  - ${mission.title}: ${mission.progress}/${mission.maxProgress} (${mission.isCompleted ? 'Complete' : 'In Progress'})`);
    });

    console.log('\n🎉 Mission system test completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Mission system test failed:', error);
    return false;
  }
};

// Test localStorage fallback
export const testLocalStorageFallback = (userId: string) => {
  console.log('🧪 Testing localStorage fallback for user:', userId);

  // Simulate mission progress in localStorage
  const testMissions = [
    { id: 'daily_login', progress: 1, maxProgress: 1 },
    { id: 'tool_collector', progress: 2, maxProgress: 3 },
    { id: 'portfolio_creator', progress: 1, maxProgress: 1 }
  ];

  testMissions.forEach(mission => {
    const localKey = `mission_${userId}_${mission.id}`;
    const localData = {
      userId,
      missionId: mission.id,
      progress: mission.progress,
      isCompleted: mission.progress >= mission.maxProgress,
      isClaimed: false,
      updatedAt: new Date().toISOString()
    };
    localStorage.setItem(localKey, JSON.stringify(localData));
    console.log(`✅ Stored mission ${mission.id} in localStorage`);
  });

  console.log('🎉 localStorage fallback test completed!');
};

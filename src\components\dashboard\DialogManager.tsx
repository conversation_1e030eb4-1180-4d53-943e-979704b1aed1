import { Tool } from '@/types/tools';
import { Loader2 } from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface DialogManagerProps {
    // Delete Dialog Props
    showDeleteDialog: boolean;
    setShowDeleteDialog: (show: boolean) => void;
    toolToDelete: any;
    isDeleting: boolean;
    confirmDelete: () => void;
    // Edit Dialog Props
    isEditing: boolean;
    setIsEditing: (editing: boolean) => void;
    editingTool: Tool | null;
    setEditingTool: (tool: Tool | null) => void;
    handleSaveEdit: (tool: Tool) => void;
    isUploading: boolean;
}

export const DialogManager = ({
    showDeleteDialog,
    setShowDeleteDialog,
    toolToDelete,
    isDeleting,
    confirmDelete,
    isEditing,
    setIsEditing,
    editingTool,
    setEditingTool,
    handleSaveEdit,
    isUploading,
}: DialogManagerProps) => {
    return (
        <>
            {/* Delete Confirmation Dialog */}
            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md">
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Tool?</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete "{toolToDelete?.name}"? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <button
                            className="px-4 py-2 rounded-md bg-transparent border border-[#01AAE9]/20 text-white hover:bg-[#01AAE9]/10 transition-colors"
                            disabled={isDeleting}
                            onClick={() => setShowDeleteDialog(false)}
                        >
                            Cancel
                        </button>
                        <button
                            className="px-4 py-2 rounded-md bg-red-500/80 text-white hover:bg-red-500 transition-colors"
                            onClick={confirmDelete}
                            disabled={isDeleting}
                        >
                            {isDeleting ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 inline animate-spin" />
                                    Deleting...
                                </>
                            ) : (
                                'Delete'
                            )}
                        </button>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Edit Tool Dialog */}
            <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
                <DialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Edit Tool</DialogTitle>
                        <DialogDescription>
                            Update the tool details below.
                        </DialogDescription>
                    </DialogHeader>

                    {editingTool && (
                        <div className="space-y-6 py-4">
                            <div className="grid grid-cols-1 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Name</Label>
                                    <Input
                                        id="name"
                                        value={editingTool.name}
                                        onChange={(e) => setEditingTool({ ...editingTool, name: e.target.value })}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={editingTool.description || ''}
                                        onChange={(e) => setEditingTool({ ...editingTool, description: e.target.value })}
                                        className="min-h-[100px]"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="tags">Tags</Label>
                                <Input
                                    id="tags"
                                    value={Array.isArray(editingTool.tags) ? editingTool.tags.join(', ') : editingTool.tags || ''}
                                    onChange={(e) => {
                                        const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                                        setEditingTool({ ...editingTool, tags });
                                    }}
                                    placeholder="Enter tags separated by commas"
                                />
                            </div>
                        </div>
                    )}

                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsEditing(false)}>
                            Cancel
                        </Button>
                        <Button
                            onClick={() => editingTool && handleSaveEdit(editingTool)}
                            disabled={!editingTool || isUploading}
                        >
                            {isUploading ? (
                                <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Uploading...
                                </>
                            ) : (
                                'Save Changes'
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};


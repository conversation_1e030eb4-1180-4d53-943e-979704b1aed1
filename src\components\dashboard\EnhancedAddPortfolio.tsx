import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { collection, addDoc, doc, updateDoc, arrayUnion, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { EnhancedPortfolioForm } from '@/components/portfolio/EnhancedPortfolioForm';
// Removed unused portfolioStorageService import
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, FileText, Video, Image as ImageIcon } from 'lucide-react';
import { MissionTracker } from '@/services/missionService';

interface EnhancedPortfolioFormData {
  title: string;
  description: string;
  tools_used: string[];
  tagged_toolkits?: string[];
  is_public: boolean;
  status: 'published' | 'draft' | 'archived';
  content_type: 'post' | 'reel' | 'both';
  project_url?: string;
  media_urls: string[];
  media_metadata: any[];
}

export default function EnhancedAddPortfolio() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (data: EnhancedPortfolioFormData) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to add a portfolio item.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Determine media type from uploaded media
      const hasVideo = data.media_metadata.some(meta => meta?.playbackId);
      const mediaType = hasVideo ? 'video' : 'image';

      // Create unique ID for the portfolio item
      const timestamp = Date.now();
      const portfolioId = `portfolio-${user.id}-${timestamp}`;

      // Prepare portfolio data with enhanced metadata - filter out undefined values
      const portfolioData: any = {
        id: portfolioId,
        userId: user?.uid || user?.id, // Use uid for consistency with queries
        title: data.title,
        description: data.description,
        media_url: data.media_urls[0] || '', // Primary media URL
        media_urls: data.media_urls,
        media_type: mediaType,
        content_type: data.content_type,
        tools_used: data.tools_used,
        tagged_toolkits: data.tagged_toolkits || [],
        categories: [],
        likes: 0,
        comments: 0,
        views: 0,
        project_url: data.project_url || '',
        is_public: data.is_public,
        status: data.status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Only add thumbnail_url if it has a value
      if (mediaType === 'image' && data.media_urls[0]) {
        portfolioData.thumbnail_url = data.media_urls[0];
      }

      // Create storage metadata without undefined values
      const storageMetadata: any = {
        file_paths: data.media_metadata.map(meta => meta?.fileName || ''),
        file_sizes: data.media_metadata.map(meta => meta?.size || 0),
        upload_timestamps: data.media_metadata.map(() => new Date().toISOString())
      };

      // Only add video-specific fields if they exist
      const livepeerAssetId = data.media_metadata.find(meta => meta?.playbackId)?.playbackId;
      if (livepeerAssetId) {
        storageMetadata.livepeer_asset_id = livepeerAssetId;
      }

      const livepeerPlaybackUrl = data.media_metadata.find(meta => meta?.playbackUrl)?.playbackUrl;
      if (livepeerPlaybackUrl) {
        storageMetadata.livepeer_playback_url = livepeerPlaybackUrl;
      }

      portfolioData.storage_metadata = storageMetadata;

      // Add to portfolio collection using the new system
      const portfolioRef = collection(db, 'portfolio');
      const newPortfolioRef = await addDoc(portfolioRef, portfolioData);

      // Update user's portfolio array (for backward compatibility)
      const userRef = doc(db, 'users', user?.uid || user?.id);
      await updateDoc(userRef, {
        portfolios: arrayUnion(newPortfolioRef.id)
      });

      // Track mission progress for portfolio creation
      try {
        await MissionTracker.createPortfolioItem(user?.uid || user?.id);

        // Check if this is the user's first portfolio item for the milestone mission
        const userDoc = await getDoc(userRef);
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const portfolioCount = userData.portfolios?.length || 0;
          if (portfolioCount === 1) {
            // This is their first portfolio item
            await MissionTracker.createFirstPortfolio(user?.uid || user?.id);
          }
        }
      } catch (missionError) {
        console.warn('Error tracking portfolio mission:', missionError);
        // Don't fail the portfolio creation if mission tracking fails
      }

      toast({
        title: 'Success',
        description: 'Portfolio item added successfully!'
      });

      navigate('/portfolio');
    } catch (error) {
      console.error('Error adding portfolio:', error);
      toast({
        title: 'Error',
        description: 'Failed to add portfolio item. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/portfolio');
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-sortmy-dark p-6">
        <div className="max-w-2xl mx-auto">
          <Card className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">Authentication Required</h2>
            <p className="text-gray-600 mb-4">You must be logged in to add a portfolio item.</p>
            <Button onClick={() => navigate('/login')}>
              Go to Login
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-sortmy-dark p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate('/portfolio')}
            className="text-white hover:text-blue-400"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Portfolio
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-white">Add New Portfolio Item</h1>
            <p className="text-gray-400">Create and share your latest work</p>
          </div>
        </div>

        {/* Features Overview */}
        <Card className="p-6 mb-6 bg-gray-800 border-gray-600">
          <h2 className="text-lg font-semibold mb-4 text-white">Enhanced Portfolio Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-blue-600/20 rounded-lg">
                <ImageIcon className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <h3 className="font-medium text-white">Firebase Storage</h3>
                <p className="text-sm text-gray-300">Optimized image uploads with automatic compression</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="p-2 bg-purple-600/20 rounded-lg">
                <Video className="w-5 h-5 text-purple-400" />
              </div>
              <div>
                <h3 className="font-medium text-white">Livepeer Videos</h3>
                <p className="text-sm text-gray-300">Professional video streaming with global CDN</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="p-2 bg-green-600/20 rounded-lg">
                <FileText className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <h3 className="font-medium text-white">Smart Drafts</h3>
                <p className="text-sm text-gray-300">Auto-save with intelligent cleanup</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Main Form */}
        <Card className="p-6 bg-gray-800 border-gray-600">
          <EnhancedPortfolioForm
            user={user}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isSubmitting}
            initialData={{
              is_public: true,
              status: 'published',
              content_type: 'post'
            }}
          />
        </Card>

        {/* Help Section */}
        <Card className="p-6 mt-6 bg-gray-800 border-gray-600">
          <h3 className="font-semibold mb-3 text-white">Tips for Better Portfolio Items</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-300">
            <div>
              <h4 className="font-medium text-white mb-2">Images</h4>
              <ul className="space-y-1">
                <li>• Use high-resolution images (1920x1080 or higher)</li>
                <li>• Images are automatically optimized for web</li>
                <li>• Supported formats: JPEG, PNG, WebP, GIF</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-white mb-2">Videos</h4>
              <ul className="space-y-1">
                <li>• Videos are processed for optimal streaming</li>
                <li>• Automatic thumbnail generation</li>
                <li>• Global CDN delivery for fast loading</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-white mb-2">Drafts</h4>
              <ul className="space-y-1">
                <li>• Your work is auto-saved every 30 seconds</li>
                <li>• Drafts are cleaned up after 7 days</li>
                <li>• Resume work anytime from the Drafts tab</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-white mb-2">Organization</h4>
              <ul className="space-y-1">
                <li>• Select tools from your personal library</li>
                <li>• Use descriptive titles and detailed descriptions</li>
                <li>• Choose appropriate content types for visibility</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}


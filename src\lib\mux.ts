// Mux Video API utilities
export interface MuxAsset {
    id: string;
    status: 'preparing' | 'ready' | 'errored';
    playback_ids: Array<{
        id: string;
        policy: 'public' | 'signed';
    }>;
    duration?: number;
    aspect_ratio?: string;
    created_at: string;
    max_stored_resolution?: string;
    max_stored_frame_rate?: number;
}

export interface MuxUploadResponse {
    data: {
        id: string;
        timeout: number;
        status: 'waiting' | 'asset_created' | 'errored' | 'cancelled' | 'timed_out';
        new_asset_settings: {
            playback_policy: string[];
        };
        url: string;
    };
}

// Get Mux API credentials from environment
const getMuxCredentials = () => {
    const tokenId = import.meta.env.VITE_MUX_TOKEN_ID;
    const tokenSecret = import.meta.env.VITE_MUX_TOKEN_SECRET;

    if (!tokenId || !tokenSecret) {
        console.warn('Mux credentials not found in environment variables');
        return null;
    }

    return { tokenId, tokenSecret };
};

// Create authorization header for Mux API
const getMuxAuthHeader = () => {
    const credentials = getMuxCredentials();
    if (!credentials) return null;

    const auth = btoa(`${credentials.tokenId}:${credentials.tokenSecret}`);
    return `Basic ${auth}`;
};

// Generate Mux playback URL from playback ID
export const getMuxPlaybackUrl = (playbackId: string): string => {
    return `https://stream.mux.com/${playbackId}.m3u8`;
};

// Generate Mux thumbnail URL
export const getMuxThumbnailUrl = (playbackId: string, options?: {
    width?: number;
    height?: number;
    fit_mode?: 'preserve' | 'crop' | 'pad';
    time?: number;
}): string => {
    const params = new URLSearchParams();

    if (options?.width) params.set('width', options.width.toString());
    if (options?.height) params.set('height', options.height.toString());
    if (options?.fit_mode) params.set('fit_mode', options.fit_mode);
    if (options?.time) params.set('time', options.time.toString());

    const queryString = params.toString();
    return `https://image.mux.com/${playbackId}/thumbnail.jpg${queryString ? `?${queryString}` : ''}`;
};

// Check if a URL is a Mux URL
export const isMuxUrl = (url: string): boolean => {
    if (!url) return false;
    return url.includes('stream.mux.com') || url.includes('image.mux.com');
};

// Extract playback ID from Mux URL
export const extractMuxPlaybackId = (url: string): string | null => {
    if (!url) return null;

    // Match Mux stream URLs with .m3u8 extension
    // This will match URLs like: https://stream.mux.com/PnndXcwARagR00m4gHeLa2AbZRmywzJG73kA9ki00xgOo.m3u8
    const streamMatch = url.match(/stream\.mux\.com\/([a-zA-Z0-9]+)\.m3u8/);
    if (streamMatch) return streamMatch[1];

    // Match Mux stream URLs without extension
    const streamMatchNoExt = url.match(/stream\.mux\.com\/([a-zA-Z0-9]+)/);
    if (streamMatchNoExt) return streamMatchNoExt[1];

    // Match Mux image URLs: https://image.mux.com/{playback_id}/thumbnail.jpg
    const imageMatch = url.match(/image\.mux\.com\/([^\/]+)/);
    if (imageMatch) return imageMatch[1];

    return null;
};

// Create a direct upload for video files
export const createMuxDirectUpload = async (options?: {
    cors_origin?: string;
    new_asset_settings?: {
        playback_policy?: string[];
        encoding_tier?: 'baseline' | 'smart';
        video_quality?: 'basic' | 'plus';
    };
}): Promise<MuxUploadResponse | null> => {
    const authHeader = getMuxAuthHeader();
    if (!authHeader) {
        console.error('Mux credentials not available');
        return null;
    }

    try {
        const requestBody = {
            cors_origin: options?.cors_origin || window.location.origin,
            new_asset_settings: {
                playback_policy: ['public'],
                encoding_tier: 'baseline', // Use baseline instead of smart for better compatibility
                ...options?.new_asset_settings,
            },
        };

        console.log('Creating Mux upload with:', requestBody);

        const response = await fetch('https://api.mux.com/video/v1/uploads', {
            method: 'POST',
            headers: {
                'Authorization': authHeader,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Mux API error response:', errorText);
            throw new Error(`Mux API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error creating Mux direct upload:', error);
        return null;
    }
};

// Get asset information
export const getMuxAsset = async (assetId: string): Promise<MuxAsset | null> => {
    const authHeader = getMuxAuthHeader();
    if (!authHeader) {
        console.error('Mux credentials not available');
        return null;
    }

    try {
        const response = await fetch(`https://api.mux.com/video/v1/assets/${assetId}`, {
            headers: {
                'Authorization': authHeader,
            },
        });

        if (!response.ok) {
            throw new Error(`Mux API error: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        return result.data;
    } catch (error) {
        console.error('Error fetching Mux asset:', error);
        return null;
    }
};

// Upload file to Mux using direct upload
export const uploadToMux = async (
    file: File,
    onProgress?: (progress: number) => void
): Promise<{ assetId: string; playbackId: string } | null> => {
    try {
        // Step 1: Create direct upload
        const uploadResponse = await createMuxDirectUpload();
        if (!uploadResponse) {
            throw new Error('Failed to create Mux upload URL');
        }

        const { url: uploadUrl, id: uploadId } = uploadResponse.data;

        // Step 2: Upload file
        const formData = new FormData();
        formData.append('file', file);

        const xhr = new XMLHttpRequest();

        return new Promise((resolve, reject) => {
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable && onProgress) {
                    const progress = (event.loaded / event.total) * 100;
                    onProgress(progress);
                }
            });

            xhr.addEventListener('load', async () => {
                if (xhr.status === 200 || xhr.status === 201) {
                    // Step 3: Poll for asset creation
                    try {
                        const assetId = await pollForAssetCreation(uploadId);
                        const asset = await getMuxAsset(assetId);

                        if (asset && asset.playback_ids.length > 0) {
                            resolve({
                                assetId: asset.id,
                                playbackId: asset.playback_ids[0].id,
                            });
                        } else {
                            reject(new Error('Asset created but no playback ID available'));
                        }
                    } catch (error) {
                        reject(error);
                    }
                } else {
                    reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Upload failed'));
            });

            xhr.open('PUT', uploadUrl);
            xhr.send(file);
        });
    } catch (error) {
        console.error('Error uploading to Mux:', error);
        return null;
    }
};

// Poll for asset creation after upload
const pollForAssetCreation = async (uploadId: string): Promise<string> => {
    const authHeader = getMuxAuthHeader();
    if (!authHeader) {
        throw new Error('Mux credentials not available');
    }

    const maxAttempts = 30; // 5 minutes with 10-second intervals
    let attempts = 0;

    while (attempts < maxAttempts) {
        try {
            const response = await fetch(`https://api.mux.com/video/v1/uploads/${uploadId}`, {
                headers: {
                    'Authorization': authHeader,
                },
            });

            if (response.ok) {
                const result = await response.json();
                const upload = result.data;

                if (upload.status === 'asset_created' && upload.asset_id) {
                    return upload.asset_id;
                } else if (upload.status === 'errored' || upload.status === 'cancelled' || upload.status === 'timed_out') {
                    throw new Error(`Upload ${upload.status}`);
                }
            }

            // Wait 10 seconds before next attempt
            await new Promise(resolve => setTimeout(resolve, 10000));
            attempts++;
        } catch (error) {
            console.error('Error polling for asset creation:', error);
            attempts++;
        }
    }

    throw new Error('Timeout waiting for asset creation');
};
import { ContentBlock } from '@/types/academy';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { useEffect, useState } from 'react';

interface ContentBlockViewProps {
  block: ContentBlock;
}

const ContentBlockView = ({ block }: ContentBlockViewProps) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  const renderContent = () => {
    if (!isMounted) return null;

    switch (block.type) {
      case 'text':
        return (
          <div className="prose prose-invert prose-blue max-w-none">
            <div dangerouslySetInnerHTML={{ __html: block.content }} />
            {block.caption && (
              <p className="text-sm text-gray-400 mt-2">{block.caption}</p>
            )}
          </div>
        );

      case 'image':
        return (
          <div className="space-y-2">
            <img
              src={block.content}
              alt={block.caption || 'Image'}
              className="max-w-full rounded-lg"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/images/placeholder.png';
              }}
            />
            {block.caption && (
              <p className="text-sm text-gray-400 text-center">{block.caption}</p>
            )}
          </div>
        );

      case 'video':
        return (
          <div className="space-y-2">
            <AspectRatio ratio={16 / 9}>
              <video
                src={block.content}
                controls
                className="w-full h-full rounded-lg"
                poster="/images/video-placeholder.png"
              >
                Your browser does not support the video tag.
              </video>
            </AspectRatio>
            {block.caption && (
              <p className="text-sm text-gray-400 text-center">{block.caption}</p>
            )}
          </div>
        );

      case 'code':
        return (
          <div className="space-y-2">
            <div className="bg-sortmy-darker rounded-lg p-4 overflow-x-auto">
              <pre className="font-mono text-sm">
                <code>{block.content}</code>
              </pre>
            </div>
            {block.caption && (
              <p className="text-sm text-gray-400">{block.caption}</p>
            )}
          </div>
        );

      case 'embed':
        if (block.embedType === 'youtube' && block.content) {
          return (
            <div className="space-y-2">
              <AspectRatio ratio={16 / 9}>
                <iframe
                  src={`https://www.youtube.com/embed/${block.content}?rel=0`}
                  title={block.caption || 'YouTube video'}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                  className="w-full h-full rounded-lg"
                ></iframe>
              </AspectRatio>
              {block.caption && (
                <p className="text-sm text-gray-400 text-center">{block.caption}</p>
              )}
            </div>
          );
        }

        if (block.embedType === 'vimeo' && block.content) {
          return (
            <div className="space-y-2">
              <AspectRatio ratio={16 / 9}>
                <iframe
                  src={`https://player.vimeo.com/video/${block.content}`}
                  title={block.caption || 'Vimeo video'}
                  allow="autoplay; fullscreen; picture-in-picture"
                  allowFullScreen
                  className="w-full h-full rounded-lg"
                ></iframe>
              </AspectRatio>
              {block.caption && (
                <p className="text-sm text-gray-400 text-center">{block.caption}</p>
              )}
            </div>
          );
        }

        // Default for other embed types
        return (
          <div className="space-y-2">
            <div className="bg-sortmy-darker border border-sortmy-blue/20 rounded-lg p-4">
              <div dangerouslySetInnerHTML={{ __html: block.content }} />
              {block.caption && (
                <p className="text-sm text-gray-400 mt-2">{block.caption}</p>
              )}
            </div>
          </div>
        );

      default:
        return (
          <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <p className="text-red-400">Unknown content type: {block.type}</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      {renderContent()}
    </div>
  );
};

export default ContentBlockView;
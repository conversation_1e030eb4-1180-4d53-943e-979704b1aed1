import { useState, useEffect, useRef } from 'react';
import { Eye, ExternalLink, Trash2, Plus, Bookmark as BookmarkIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import type { ImageSettings } from '@/types/tools';
import { getStorage, ref, getDownloadURL } from 'firebase/storage';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface BaseToolFields {
  id: string;
  name: string;
  description?: string;
  logoUrl?: string;
  logo_url?: string;
  useCase?: string;
}

// Helper function to get image URL from tool
const getImageUrl = (tool: BaseToolFields): string | undefined => {
  return tool.logoUrl || tool.logo_url || undefined;
};

export interface ToolIconProps {
  tool: {
    id: string;
    name: string;
    description?: string;
    logoUrl?: string;
    logo_url?: string;
    useCase?: string;
    createdBy?: string;
    createdAt?: string;
    updatedAt?: string;
    tags?: string[] | string;
    pricing?: string;
    website?: string;
    imageSettings?: ImageSettings;
  };
  isUserTool?: boolean;
  isLibraryTool?: boolean;
  onDelete?: () => void;  // Update these to not accept parameters
  onAdd?: () => void;
  onEdit?: () => void;
  isAdmin?: boolean;
  isSaving?: boolean;
}

export const ToolIcon: React.FC<ToolIconProps> = ({
  tool,
  isUserTool,
  isLibraryTool,
  onDelete,
  onAdd,
  onEdit,
  isAdmin,
  isSaving
}) => {
  const navigate = useNavigate();
  const [showDetails, setShowDetails] = useState(false);
  const [position, setPosition] = useState<'top' | 'bottom'>('bottom');
  const containerRef = useRef<HTMLDivElement>(null);
  // Add state for image loading
  const [imageUrl, setImageUrl] = useState<string>('');
  const [imageError, setImageError] = useState(false);

  // Calculate whether to show details above or below based on viewport position
  useEffect(() => {
    const calculatePosition = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const spaceBelow = viewportHeight - rect.bottom;
        // If space below is less than 200px, show details above
        setPosition(spaceBelow < 200 ? 'top' : 'bottom');
      }
    };

    if (showDetails) {
      calculatePosition();
    }
  }, [showDetails]);

  // Helper to get website URL with protocol
  const getWebsiteUrl = () => {
    const url = tool.website || (tool as any).websiteLink || (tool as any).link;
    if (!url) return undefined;
    return url.startsWith('http') ? url : `https://${url}`;
  };

  // Load and validate image URL
  useEffect(() => {
    const loadImage = async () => {
      const url = getImageUrl(tool);
      if (!url) {
        setImageError(true);
        setImageUrl('');
        return;
      }

      try {
        // First try loading the image directly
        if (url.startsWith('data:') || url.startsWith('blob:')) {
          setImageUrl(url);
          setImageError(false);
          return;
        }

        if (url.includes('firebasestorage.googleapis.com')) {
          const storage = getStorage();
          const urlPath = url.split('/o/')[1]?.split('?')[0];
          if (!urlPath) throw new Error('Invalid Firebase Storage URL');

          const decodedPath = decodeURIComponent(urlPath);
          const imageRef = ref(storage, decodedPath);
          const downloadUrl = await getDownloadURL(imageRef);
          setImageUrl(downloadUrl);
          setImageError(false);
          return;
        }

        // Try loading the image directly first
        try {
          // Only allow images from trusted domains (CSP safe)
          const allowedDomains = [
            'firebasestorage.googleapis.com',
            'storage.googleapis.com',
            'cdn.gpteng.co',
            'images.unsplash.com',
            'lh3.googleusercontent.com',
            'avatars.githubusercontent.com',
            'raw.githubusercontent.com',
            'githubusercontent.com',
            'cdn.jsdelivr.net',
            'unpkg.com',
            'localhost',
            '127.0.0.1',
          ];
          const urlObj = new URL(url);
          const isAllowed = allowedDomains.some(domain => urlObj.hostname.endsWith(domain));
          if (isAllowed) {
            const response = await fetch(url, { mode: 'cors' });
            if (response.ok) {
              setImageUrl(url);
              setImageError(false);
              return;
            }
          } else {
            throw new Error('Blocked by CSP: domain not allowed');
          }
        } catch (directError) {
          console.log('Direct image load failed or blocked by CSP, skipping proxy...', directError);
        }
        // If not allowed or failed, fallback to default icon
        setImageError(true);
        setImageUrl('');
      } catch (error) {
        console.error('Error loading image:', error);
        // On error, try to use the original URL as fallback
        try {
          const img = new Image();
          img.onload = () => {
            setImageUrl(url);
            setImageError(false);
          };
          img.onerror = () => {
            setImageError(true);
            setImageUrl('');
          };
          img.src = url;
        } catch (fallbackError) {
          setImageError(true);
          setImageUrl('');
        }
      }
    };

    loadImage();

    return () => {
      // Cleanup blob URLs
      if (imageUrl?.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [tool]);

  // Get image settings if available (for AITool type)
  const imageSettings = tool.imageSettings;
  const objectFit = imageSettings?.size === 'contain' || imageSettings?.size === 'cover'
    ? imageSettings.size
    : imageSettings?.size ? undefined : 'contain';

  // Update tag handling
  const handleTags = (tags?: string | string[]): string[] => {
    if (!tags) return [];
    if (Array.isArray(tags)) return tags;
    return tags.split(',').map(t => t.trim()).filter(Boolean);
  };

  // Use the handleTags function
  const displayTags = handleTags(tool.tags);

  // Handler for viewing tool details
  const handleViewDetails = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/dashboard/tools/${tool.id}`);
  };

  // Handler for visiting website
  const handleVisitWebsite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const url = getWebsiteUrl();
    if (url) window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      ref={containerRef}
      className="relative group cursor-pointer"
      onMouseEnter={() => setShowDetails(true)}
      onMouseLeave={() => setShowDetails(false)}
    >
      {/* Main icon clickable area opens website in new tab */}
      <a
        href={getWebsiteUrl()}
        target="_blank"
        rel="noopener noreferrer"
        tabIndex={-1}
        className="flex flex-col items-center w-20 focus:outline-none"
        onClick={e => {
          if (!getWebsiteUrl()) e.preventDefault();
        }}
      >
        {/* App Icon */}
        <div
          className="w-16 h-16 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 mb-2 shadow-lg cursor-pointer relative hover:scale-110 hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 ease-out"
        >
          {!imageError && imageUrl ? (
            <img
              src={imageUrl}
              alt={`${tool.name} logo`}
              className="w-full h-full"
              crossOrigin="anonymous"
              loading="lazy"
              style={{
                objectFit,
                width: imageSettings?.size?.endsWith('%') ? imageSettings.size : '100%',
                height: imageSettings?.size?.endsWith('%') ? imageSettings.size : '100%',
                padding: imageSettings?.padding != null ? `${imageSettings.padding}px` : '0',
                objectPosition: imageSettings?.position || 'center'
              }}
              onError={(e) => {
                console.error('Image load error:', {
                  url: imageUrl,
                  error: e
                });
                setImageError(true);
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-2xl font-bold text-white">{tool.name ? tool.name.charAt(0) : '?'}</span>
            </div>
          )}
        </div>

        {/* App Name */}
        <span
          className="text-sm font-medium text-white truncate max-w-[80px] cursor-pointer hover:text-blue-300 transition-all duration-200 hover:scale-105"
        >
          {tool.name}
        </span>
      </a>

      {/* Hover details - Use fixed positioning */}
      {showDetails && (
        <div
          className={cn(
            "absolute z-50 w-72 bg-sortmy-darker border border-[#01AAE9]/20 rounded-lg p-4 shadow-lg",
            position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2',
            "left-1/2 transform -translate-x-1/2"
          )}
          style={{
            maxHeight: '300px',
            overflowY: 'auto'
          }}
        >
          <div className="relative">
            {/* Arrow pointer - now points to the left */}
            <div className="absolute top-1/2 -left-3 -translate-y-1/2 w-0 h-0 border-t-8 border-b-8 border-r-8 border-transparent border-r-blue-500/30"></div>

            {/* Content */}
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <h3 className="font-semibold text-white">{tool.name}</h3>
                {tool.pricing && (
                  <Badge
                    variant="outline"
                    className={`text-xs ${tool.pricing === 'Free'
                      ? 'bg-green-500/20 text-green-400 border-green-500/20'
                      : tool.pricing === 'Freemium'
                        ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                        : 'bg-purple-500/20 text-purple-400 border-purple-500/30'
                      }`}
                  >
                    {tool.pricing}
                  </Badge>
                )}
              </div>

              {/* Description */}
              {(tool.description || tool.useCase) && (
                <p className="text-sm text-gray-300 line-clamp-2">
                  {tool.description || tool.useCase}
                </p>
              )}

              {/* Tags */}
              {tool.tags && tool.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {displayTags.slice(0, 3).map((tag: string, index: number) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs bg-[#01AAE9]/10 border-[#01AAE9]/20"
                    >
                      {tag}
                    </Badge>
                  ))}
                  {displayTags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{displayTags.length - 3}
                    </Badge>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col gap-2.5">
                {/* View Details Button */}
                <button
                  className="w-full px-4 py-2.5 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white flex items-center justify-center gap-2 font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg hover:shadow-indigo-500/25"
                  onClick={handleViewDetails}
                >
                  <Eye size={16} />
                  View Details
                </button>

                {/* Visit Website Button */}
                <button
                  className="w-full px-4 py-2.5 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white flex items-center justify-center gap-2 font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25"
                  onClick={handleVisitWebsite}
                  disabled={!getWebsiteUrl()}
                >
                  <ExternalLink size={16} />
                  Visit Website
                </button>

                {/* Admin Edit Button */}
                {isAdmin && onEdit && (
                  <button
                    onClick={onEdit}  // Remove the tool argument
                    className="w-full px-4 py-2.5 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white flex items-center justify-center gap-2 font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg hover:shadow-green-500/25"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                    </svg>
                    Edit Tool
                  </button>
                )}

                {/* Delete Button - for user tools or admin */}
                {(isUserTool || isAdmin) && (
                  <button
                    onClick={onDelete}  // Remove the tool argument
                    className="w-full px-4 py-2.5 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white flex items-center justify-center gap-2 font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg hover:shadow-red-500/25"
                  >
                    <Trash2 size={16} />
                    Delete
                  </button>
                )}

                {/* Add Button */}
                {!isUserTool && (
                  <button
                    onClick={onAdd}  // Remove the tool argument
                    disabled={isSaving}
                    className={`w-full px-4 py-2.5 rounded-lg flex items-center justify-center gap-2 font-medium transition-all duration-200 ${isSaving
                      ? 'bg-gradient-to-r from-gray-500/50 to-gray-600/50 cursor-not-allowed'
                      : isLibraryTool
                        ? 'bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 hover:scale-105 hover:shadow-lg hover:shadow-emerald-500/25'
                        : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25'
                      } text-white`}
                  >
                    {isSaving ? (
                      <>
                        <div className="w-4 h-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                        <span>Adding...</span>
                      </>
                    ) : (
                      <>
                        {isLibraryTool ? <BookmarkIcon size={16} /> : <Plus size={16} />}
                        <span>{isLibraryTool ? 'Add to My Tools' : 'Add Tool'}</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};


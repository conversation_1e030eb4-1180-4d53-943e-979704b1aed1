import { useState } from 'react';
import { cn } from '@/lib/utils';

interface ClickEffectProps extends React.HTMLAttributes<HTMLDivElement> {
    effect?: 'ripple' | 'pulse';
    color?: string;
    children: React.ReactNode;
}

export const ClickEffect: React.FC<ClickEffectProps> = ({
    effect = 'ripple',
    color = 'blue',
    children,
    className,
    ...props
}) => {
    const [isClicked, setIsClicked] = useState(false);

    const handleClick = () => {
        setIsClicked(true);
        setTimeout(() => setIsClicked(false), 300);
    };

    return (
        <div
            className={cn(
                'relative overflow-hidden transition-transform',
                isClicked && 'scale-95',
                effect === 'ripple' && `hover:bg-${color}-100/10`,
                effect === 'pulse' && `hover:shadow-${color}-500/20`,
                className
            )}
            onClick={handleClick}
            {...props}
        >
            {children}
            {isClicked && effect === 'ripple' && (
                <div
                    className={cn(
                        'absolute inset-0 animate-ripple rounded-full bg-current opacity-25',
                        `bg-${color}-500/20`
                    )}
                />
            )}
        </div>
    );
};


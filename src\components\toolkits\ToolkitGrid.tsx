import { Link } from 'react-router-dom';
import { Tool, Toolkit } from '@/types/tools';
import { ToolkitPreview } from './ToolkitPreview';
import { Share } from 'lucide-react';
import { ToolkitShareModal } from './ToolkitShareModal';
import { useState } from 'react';

interface ToolkitGridProps {
    toolkits: Toolkit[];
    tools: Tool[];
    onEdit?: (toolkit: Toolkit) => void;
    onDelete?: (toolkit: Toolkit) => void;
    currentUserId?: string;
    isLoading?: boolean; // <-- Add this line
}

export function ToolkitGrid({ toolkits, tools, onEdit, onDelete, currentUserId, isLoading }: ToolkitGridProps) {
    const [showShareModal, setShowShareModal] = useState(false);
    const [selectedToolkit, setSelectedToolkit] = useState<Toolkit | null>(null);

    if (isLoading) {
        // Show a loading skeleton grid
        return (
            <div className="p-4 bg-sortmy-darker/40 border border-[#01AAE9]/20 rounded-xl">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Array.from({ length: 6 }).map((_, index) => (
                        <div key={index} className="rounded-xl bg-[#01AAE9]/10 h-40 animate-pulse" />
                    ))}
                </div>
            </div>
        );
    }

    if (!toolkits?.length) {
        return (
            <div className="text-center p-6 bg-sortmy-darker border border-sortmy-blue/20 rounded-lg">
                <p className="text-gray-400">No toolkits found</p>
            </div>
        );
    }

    const handleShare = (toolkit: Toolkit, e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setSelectedToolkit(toolkit);
        setShowShareModal(true);
    };

    return (
        <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {toolkits.map((toolkit) => (
                    <div key={toolkit.id} className="relative group">
                        {/* Share button, visible on hover */}
                        <button
                            className="absolute top-2 left-2 z-20 bg-gray-800/80 hover:bg-purple-600/90 text-white p-2 rounded-full shadow transition-opacity opacity-0 group-hover:opacity-100 focus:opacity-100"
                            title="Share Toolkit"
                            onClick={(e) => handleShare(toolkit, e)}
                        >
                            <Share className="w-4 h-4" />
                        </button>
                        <Link
                            to={`/dashboard/toolkits/${toolkit.id}`}
                            className="block"
                        >
                            <div className="relative">
                                {/* Show badge if shared_with includes current user */}
                                {Array.isArray(toolkit.shared_with) && currentUserId && toolkit.shared_with.includes(currentUserId) && (
                                    <span className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded z-10">
                                        Shared with you
                                    </span>
                                )}
                                <ToolkitPreview
                                    toolkit={toolkit}
                                    tools={tools}
                                    onEdit={() => onEdit?.(toolkit)}
                                    onDelete={() => onDelete?.(toolkit)}
                                    isOwner={true}
                                />
                            </div>
                        </Link>
                    </div>
                ))}
            </div>
            {/* Share Modal */}
            {selectedToolkit && (
                <ToolkitShareModal
                    isOpen={showShareModal}
                    onClose={() => setShowShareModal(false)}
                    toolkit={selectedToolkit}
                />
            )}
        </>
    );
}


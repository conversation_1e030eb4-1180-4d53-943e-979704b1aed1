# XP System Documentation

## Overview

The XP (Experience Points) system is a comprehensive gamification feature that rewards users for various activities throughout the platform. Users earn XP for actions like adding tools, creating portfolios, completing lessons, and maintaining daily login streaks.

## XP Rewards Structure

### Activity-Based Rewards

| Activity | XP Reward | Description |
|----------|-----------|-------------|
| Add Tool to Library | 5 XP | When user adds an AI tool to their personal library |
| Create Tool | 10 XP | When user creates a new tool entry |
| Create Toolkit | 10 XP | When user creates a new toolkit |
| Share Tool | 10 XP | When user shares a tool with others |
| Create Portfolio Item | 25 XP | When user creates a regular portfolio item |
| Create First Portfolio | 50 XP | Bonus for user's very first portfolio item |
| Complete Lesson | 25 XP | When user completes an academy lesson |
| Complete Course | 100 XP | When user completes an entire course |
| Create Post | 25 XP | When user creates a social post |
| Like Post | 2 XP | When user likes another user's post |
| Comment on Post | 5 XP | When user comments on a post |
| Daily Login | 25 XP | Daily login bonus |

### Streak System

The streak system provides progressive rewards for consecutive daily logins:

- **Daily Login**: 25 XP base reward
- **Weekly Streak Bonus**: 100 XP base per week
- **Progressive Multiplier**: Each week adds more bonus XP
  - Week 1: +100 XP
  - Week 2: +200 XP (100 × 2)
  - Week 3: +300 XP (100 × 3)
  - And so on...

### Level Progression

The level system uses an exponential progression:
- **Base XP per level**: 100 XP
- **Formula**: `baseXP * Math.pow(1.5, level - 1)`
- **Examples**:
  - Level 1 → 2: 100 XP
  - Level 2 → 3: 150 XP
  - Level 3 → 4: 225 XP
  - Level 4 → 5: 337 XP

## Implementation Guide

### 1. Using the XP Hook

```typescript
import { useXP } from '@/hooks/useXP';

const MyComponent = () => {
  const { activities, userXP, userLevel, isAwarding } = useXP();

  const handleToolCreation = async () => {
    // Your tool creation logic here
    
    // Award XP
    await activities.createTool('Tool Name');
  };

  return (
    <div>
      <p>Current XP: {userXP}</p>
      <p>Level: {userLevel}</p>
      <button onClick={handleToolCreation} disabled={isAwarding}>
        Create Tool
      </button>
    </div>
  );
};
```

### 2. Available Activity Methods

```typescript
const { activities } = useXP();

// Tool-related activities
await activities.addToolToLibrary('Tool Name');
await activities.createTool('Tool Name');
await activities.shareTool('Tool Name');

// Portfolio activities
await activities.createFirstPortfolio();
await activities.createPortfolioItem('Portfolio Title');

// Academy activities
await activities.completeLesson('Lesson Title');

// Social activities
await activities.createPost('Post Title');
await activities.likePost('Post Title');
await activities.commentOnPost('Post Title');

// Toolkit activities
await activities.createToolkit('Toolkit Name');
```

### 3. Manual XP Awarding

```typescript
import { useXP } from '@/hooks/useXP';

const { awardXP } = useXP();

// Award custom XP
await awardXP('CREATE_TOOL', 'Created custom AI tool', { toolId: '123' });
```

### 4. Daily Login Handling

Daily login XP is automatically handled in the AuthContext when users sign in. No additional implementation needed.

### 5. XP Progress Display

```typescript
import EnhancedXPProgress from '@/components/gamification/EnhancedXPProgress';
import { calculateXPForNextLevel } from '@/utils/xpSystem';

const XPDisplay = ({ user }) => (
  <EnhancedXPProgress 
    xp={user.xp} 
    level={user.level} 
    xpForNextLevel={calculateXPForNextLevel(user.level)}
    showSparks={true}
    animate={true}
  />
);
```

## Service Integration

### Automatic XP Integration

The following services automatically award XP:

1. **toolService.ts** - Awards XP when adding tools to library
2. **portfolioStorageService.ts** - Awards XP for portfolio creation
3. **toolkitService.ts** - Awards XP for toolkit creation
4. **AuthContext.tsx** - Handles daily login XP

### Adding XP to New Services

To integrate XP rewards into a new service:

```typescript
import { XPActions } from '@/services/xpService';

export const myNewService = async (userId: string, data: any) => {
  // Your service logic here
  
  // Award XP
  try {
    await XPActions.createTool(userId, data.name);
  } catch (error) {
    console.error('Error awarding XP:', error);
    // Don't throw error to avoid breaking main flow
  }
};
```

## Database Structure

### User Document Updates

Users now have additional XP-related fields:
```typescript
{
  xp: number;           // Total XP earned
  level: number;        // Current level
  streak_days: number;  // Current login streak
  last_login: string;   // Last login timestamp
  badges: string[];     // Array of earned badge IDs
}
```

### XP Activity Logging

XP activities are logged in the `xp_activities` collection:
```typescript
{
  userId: string;
  activityType: string;
  xpEarned: number;
  description: string;
  metadata: object;
  timestamp: Timestamp;
}
```

## Best Practices

1. **Error Handling**: Always wrap XP awarding in try-catch blocks to prevent breaking main functionality
2. **User Feedback**: Use toast notifications to inform users about XP gains
3. **Performance**: XP calculations are optimized and cached where possible
4. **Consistency**: Use the provided activity methods rather than manual XP awarding
5. **Testing**: Test XP flows in development to ensure proper rewards

## Troubleshooting

### Common Issues

1. **XP not awarded**: Check if user is authenticated and service integration is correct
2. **Incorrect calculations**: Verify level progression formulas and XP amounts
3. **Missing notifications**: Ensure toast provider is properly configured
4. **Streak resets**: Check timezone handling and date calculations

### Debug Tools

Use browser console to check XP service calls:
```javascript
// Check user XP progress
console.log(await XPService.getUserXPProgress(userId));

// Test XP calculation
console.log(calculateLevel(1000)); // Should return level for 1000 XP
```

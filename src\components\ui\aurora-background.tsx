import React from 'react';

interface AuroraBackgroundProps {
    children: React.ReactNode;
    className?: string;
    intensity?: number;
}

const AuroraBackground: React.FC<AuroraBackgroundProps> = ({
    children,
    className = '',
    intensity = 0.5
}) => {
    return (
        <div className={`relative overflow-hidden ${className}`}>
            <div
                className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900"
                style={{ opacity: intensity }}
            />
            <div className="relative">{children}</div>
        </div>
    );
};

export default AuroraBackground;

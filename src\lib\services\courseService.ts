import { db } from '@/lib/firebase';
import {
    doc,
    collection,
    getDoc,
    getDocs,
    query,
    where,
    orderBy,
    addDoc,
    updateDoc,
    writeBatch,
    DocumentData,
} from 'firebase/firestore';
import type { Course, UserProgress } from '@/types/course';
import { AppError } from '@/lib/errors';

export class CourseService {
    static async createCourse(course: Omit<Course, 'id'>): Promise<string> {
        try {
            const coursesRef = collection(db, 'courses');
            const courseDoc = await addDoc(coursesRef, {
                ...course,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            });
            return courseDoc.id;
        } catch (error) {
            console.error('Error creating course:', error);
            throw error;
        }
    }

    static async getCourse(courseId: string): Promise<Course | null> {
        try {
            const courseDoc = await getDoc(doc(db, 'courses', courseId));
            if (!courseDoc.exists()) return null;
            return { ...courseDoc.data(), id: courseDoc.id } as Course;
        } catch (error) {
            console.error('Error getting course:', error);
            throw error;
        }
    }

    static async listCourses(): Promise<Course[]> {
        try {
            const coursesRef = collection(db, 'courses');
            const q = query(coursesRef, orderBy('createdAt', 'desc'));
            const querySnapshot = await getDocs(q);
            return querySnapshot.docs.map(
                doc => ({ ...doc.data(), id: doc.id } as Course)
            );
        } catch (error) {
            console.error('Error listing courses:', error);
            throw error;
        }
    }

    static async updateCourse(
        courseId: string,
        courseData: Partial<Course>
    ): Promise<void> {
        try {
            const courseRef = doc(db, 'courses', courseId);
            const updateData = {
                ...courseData,
                updatedAt: new Date().toISOString(),
            } as DocumentData;
            await updateDoc(courseRef, updateData);
        } catch (error) {
            console.error('Error updating course:', error);
            throw error;
        }
    }

    static async deleteCourse(courseId: string): Promise<void> {
        try {
            // Start a batch write
            const batch = writeBatch(db);

            // Delete all user progress for this course
            const progressRef = collection(db, 'userProgress');
            const progressQuery = query(progressRef, where('courseId', '==', courseId));
            const progressDocs = await getDocs(progressQuery);
            progressDocs.forEach(doc => {
                batch.delete(doc.ref);
            });

            // Delete the course
            batch.delete(doc(db, 'courses', courseId));

            // Commit the batch
            await batch.commit();
        } catch (error) {
            console.error('Error deleting course:', error);
            throw error;
        }
    }

    static async getUserProgress(
        userId: string,
        courseId: string
    ): Promise<UserProgress | null> {
        try {
            const progressDoc = await getDoc(
                doc(db, 'userProgress', `${userId}_${courseId}`)
            );
            if (!progressDoc.exists()) return null;
            return progressDoc.data() as UserProgress;
        } catch (error) {
            console.error('Error getting user progress:', error);
            throw error;
        }
    }

    static async updateUserProgress(
        userId: string,
        courseId: string,
        progress: Partial<UserProgress>
    ): Promise<UserProgress> {
        try {
            const progressRef = doc(db, 'userProgress', `${userId}_${courseId}`);
            const updateData = {
                ...progress,
                updatedAt: new Date().toISOString(),
            } as DocumentData;
            await updateDoc(progressRef, updateData);

            // Get and return the updated progress
            const updatedDocRef = await getDoc(progressRef);
            const data = updatedDocRef.data();
            if (!data) {
                throw new AppError('Progress document not found after update');
            }
            return {
                userId: data.userId,
                courseId: data.courseId,
                status: data.status || 'not-started',
                completedLessons: data.completedLessons || {},
                completedModules: data.completedModules || {},
                quizScores: data.quizScores || {},
                earnedXp: data.earnedXp || 0,
                currentLesson: data.currentLesson || { moduleId: '', lessonId: '' },
                lastAccessedAt: data.lastAccessedAt || new Date().toISOString(),
                updatedAt: data.updatedAt || new Date().toISOString(),
                quizAttempts: data.quizAttempts || [] // This is allowed by the type now
            };
        } catch (error) {
            console.error('Error updating user progress:', error);
            throw error;
        }
    }

    static async initUserProgress(
        userId: string,
        courseId: string
    ): Promise<void> {
        try {
            const progressRef = doc(db, 'userProgress', `${userId}_${courseId}`); const progressData: DocumentData = {
                userId,
                courseId,
                completedLessons: {},
                completedModules: {},
                quizScores: {},
                earnedXp: 0,
                lastAccessedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            await updateDoc(progressRef, progressData);
        } catch (error) {
            console.error('Error initializing user progress:', error);
            throw error;
        }
    }

    static async listUserCourses(userId: string): Promise<{
        course: Course;
        progress: UserProgress;
    }[]> {
        try {
            // Get all user progress documents
            const progressRef = collection(db, 'userProgress');
            const progressQuery = query(progressRef, where('userId', '==', userId));
            const progressDocs = await getDocs(progressQuery);

            // Get all courses for which the user has progress
            const coursesWithProgress: { course: Course; progress: UserProgress }[] = [];

            for (const progressDoc of progressDocs.docs) {
                const progress = progressDoc.data() as UserProgress;
                const course = await this.getCourse(progress.courseId);
                if (course) {
                    coursesWithProgress.push({ course, progress });
                }
            }

            return coursesWithProgress;
        } catch (error) {
            console.error('Error listing user courses:', error);
            throw error;
        }
    }
}

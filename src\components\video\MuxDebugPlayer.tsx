import { useEffect, useRef, useState } from 'react';
import Hls from 'hls.js';

interface MuxDebugPlayerProps {
    url: string;
    title?: string;
}

export function MuxDebugPlayer({ url, title = "Debug Video" }: MuxDebugPlayerProps) {
    const videoRef = useRef<HTMLVideoElement>(null);
    const hlsRef = useRef<Hls | null>(null);
    const [logs, setLogs] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    const addLog = (message: string) => {
        const timestamp = new Date().toLocaleTimeString();
        setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
        console.log(`[MuxDebugPlayer] ${message}`);
    };

    useEffect(() => {
        const video = videoRef.current;
        if (!video || !url) return;

        addLog(`Starting debug for URL: ${url}`);
        setIsLoading(true);
        setHasError(false);

        // Clean up previous HLS instance
        if (hlsRef.current) {
            hlsRef.current.destroy();
            hlsRef.current = null;
        }

        const setupVideo = () => {
            if (Hls.isSupported()) {
                addLog('HLS.js is supported, initializing...');
                
                const hls = new Hls({
                    enableWorker: false,
                    lowLatencyMode: false,
                    debug: true, // Enable debug for detailed logs
                });

                hlsRef.current = hls;

                // Add all HLS event listeners for debugging
                hls.on(Hls.Events.MEDIA_ATTACHING, () => {
                    addLog('HLS: Media attaching');
                });

                hls.on(Hls.Events.MEDIA_ATTACHED, () => {
                    addLog('HLS: Media attached');
                });

                hls.on(Hls.Events.MANIFEST_LOADING, () => {
                    addLog('HLS: Manifest loading');
                });

                hls.on(Hls.Events.MANIFEST_LOADED, () => {
                    addLog('HLS: Manifest loaded');
                });

                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    addLog('HLS: Manifest parsed successfully');
                    setIsLoading(false);
                });

                hls.on(Hls.Events.LEVEL_LOADING, () => {
                    addLog('HLS: Level loading');
                });

                hls.on(Hls.Events.LEVEL_LOADED, () => {
                    addLog('HLS: Level loaded');
                });

                hls.on(Hls.Events.FRAG_LOADING, () => {
                    addLog('HLS: Fragment loading');
                });

                hls.on(Hls.Events.FRAG_LOADED, () => {
                    addLog('HLS: Fragment loaded');
                });

                hls.on(Hls.Events.ERROR, (_, data) => {
                    addLog(`HLS ERROR: ${data.type} - ${data.details} - ${data.fatal ? 'FATAL' : 'NON-FATAL'}`);
                    if (data.response) {
                        addLog(`HTTP Response: ${data.response.code} ${data.response.text}`);
                    }
                    if (data.fatal) {
                        setHasError(true);
                        setIsLoading(false);
                    }
                });

                addLog('Loading HLS source...');
                hls.loadSource(url);
                hls.attachMedia(video);

            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                addLog('Using native HLS support (Safari)');
                video.src = url;

                const handleLoadedData = () => {
                    addLog('Native HLS: Video loaded successfully');
                    setIsLoading(false);
                };

                const handleError = (event: Event) => {
                    addLog(`Native HLS error: ${event.type}`);
                    setHasError(true);
                    setIsLoading(false);
                };

                video.addEventListener('loadeddata', handleLoadedData);
                video.addEventListener('error', handleError);

                return () => {
                    video.removeEventListener('loadeddata', handleLoadedData);
                    video.removeEventListener('error', handleError);
                };
            } else {
                addLog('HLS not supported in this browser');
                setHasError(true);
                setIsLoading(false);
            }
        };

        setupVideo();

        return () => {
            if (hlsRef.current) {
                hlsRef.current.destroy();
                hlsRef.current = null;
            }
        };
    }, [url]);

    return (
        <div className="space-y-4 p-4 bg-gray-900 text-white rounded-lg">
            <h3 className="text-lg font-semibold">{title}</h3>
            
            <div className="aspect-video bg-black rounded">
                <video
                    ref={videoRef}
                    className="w-full h-full"
                    controls
                    playsInline
                    preload="metadata"
                    crossOrigin="anonymous"
                >
                    <source src={url} type="application/vnd.apple.mpegurl" />
                    <p>Your browser doesn't support HLS video.</p>
                </video>
            </div>

            {isLoading && (
                <div className="text-yellow-400">Loading video...</div>
            )}

            {hasError && (
                <div className="text-red-400">Error loading video!</div>
            )}

            <div className="space-y-2">
                <h4 className="font-medium">Debug Logs:</h4>
                <div className="bg-black p-3 rounded text-xs font-mono max-h-40 overflow-y-auto">
                    {logs.map((log, index) => (
                        <div key={index} className="text-green-400">
                            {log}
                        </div>
                    ))}
                </div>
            </div>

            <div className="text-sm text-gray-400">
                <p><strong>URL:</strong> {url}</p>
                <p><strong>HLS.js supported:</strong> {Hls.isSupported() ? 'Yes' : 'No'}</p>
                <p><strong>Native HLS:</strong> {videoRef.current?.canPlayType('application/vnd.apple.mpegurl') ? 'Yes' : 'No'}</p>
            </div>
        </div>
    );
}

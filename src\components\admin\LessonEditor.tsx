import { useState } from 'react';
import { Lesson, ContentType, ContentBlock } from '@/types/academy';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  X,
  Trash,
  ChevronUp,
  ChevronDown,
  FileText,
  Video,
  Image,
  HelpCircle,
  Code,
  ExternalLink,
  LayoutGrid
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ContentBlockEditor from './ContentBlockEditor';

interface LessonEditorProps {
  lesson: Lesson;
  onSave: (lesson: Lesson) => void;
  onCancel: () => void;
}

const LessonEditor = ({ lesson, onSave, onCancel }: LessonEditorProps) => {
  const [editableLesson, setEditableLesson] = useState<Lesson>({
    ...lesson,
    contentBlocks: lesson.contentBlocks || []
  });

  const handleChange = (field: string, value: any) => {
    setEditableLesson(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddBlock = (type: ContentType) => {
    const newBlock: ContentBlock = {
      id: `block-${Date.now()}`,
      type,
      order: editableLesson.contentBlocks.length,
      content: '',
      caption: ''
    };

    // Initialize default content based on type
    switch (type) {
      case 'text':
        newBlock.content = 'Start typing your content here...';
        break;
      case 'quiz':
        newBlock.content = 'What is the primary use of AI in content creation?';
        newBlock.options = ['Replacing human creativity', 'Enhancing human creativity', 'Eliminating the need for editing', 'Making content creation more expensive'];
        newBlock.correctOption = 1;
        break;
      case 'code':
        newBlock.content = 'console.log("Hello, world!");';
        newBlock.codeLanguage = 'javascript';
        break;
      case 'embed':
        newBlock.content = '';
        newBlock.embedType = 'youtube';
        break;
    }

    setEditableLesson(prev => ({
      ...prev,
      contentBlocks: [...prev.contentBlocks, newBlock]
    }));
  };

  const handleUpdateBlock = (updatedBlock: ContentBlock) => {
    setEditableLesson(prev => ({
      ...prev,
      contentBlocks: prev.contentBlocks.map(block =>
        block.id === updatedBlock.id ? updatedBlock : block
      )
    }));
  };

  const handleDeleteBlock = (blockId: string) => {
    setEditableLesson(prev => ({
      ...prev,
      contentBlocks: prev.contentBlocks.filter(block => block.id !== blockId)
        .map((block, index) => ({
          ...block,
          order: index // Update order after deletion
        }))
    }));
  };

  const handleMoveBlock = (blockId: string, direction: 'up' | 'down') => {
    const blocks = [...editableLesson.contentBlocks];
    const index = blocks.findIndex(block => block.id === blockId);

    if (index < 0) return;

    const newIndex = direction === 'up' ? Math.max(0, index - 1) : Math.min(blocks.length - 1, index + 1);

    if (newIndex === index) return;

    // Update order properties
    blocks[index].order = newIndex;
    blocks[newIndex].order = index;

    // Swap positions in array
    [blocks[index], blocks[newIndex]] = [blocks[newIndex], blocks[index]];

    setEditableLesson(prev => ({
      ...prev,
      contentBlocks: blocks
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Sort content blocks by order before saving
    const sortedBlocks = [...editableLesson.contentBlocks].sort((a, b) => a.order - b.order);

    onSave({
      ...editableLesson,
      contentBlocks: sortedBlocks
    });
  };

  // Get icon for content type
  const getContentTypeIcon = (type: ContentType) => {
    switch (type) {
      case 'text': return <FileText className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      case 'image': return <Image className="h-4 w-4" />;
      case 'quiz': return <HelpCircle className="h-4 w-4" />;
      case 'code': return <Code className="h-4 w-4" />;
      case 'embed': return <ExternalLink className="h-4 w-4" />;
      default: return <LayoutGrid className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="lesson-title">Lesson Title</Label>
          <Input
            id="lesson-title"
            value={editableLesson.title}
            onChange={(e) => handleChange('title', e.target.value)}
            placeholder="e.g., Introduction to ChatGPT"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="lesson-description">Description</Label>
          <Textarea
            id="lesson-description"
            value={editableLesson.description || ''}
            onChange={(e) => handleChange('description', e.target.value)}
            placeholder="Briefly describe what this lesson covers..."
            rows={2}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="lesson-duration">Duration (minutes)</Label>
          <Input
            id="lesson-duration"
            type="number"
            value={editableLesson.duration || 0}
            onChange={(e) => handleChange('duration', parseInt(e.target.value))}
            min={0}
          />
        </div>

        <Separator className="my-6" />

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Lesson Content</h3>
            <div className="flex gap-2">
              <Tabs defaultValue="text" className="w-auto">
                <TabsList className="h-8">
                  <TabsTrigger value="text" className="px-2 py-1 h-8" onClick={() => handleAddBlock('text')}>
                    <FileText className="h-3.5 w-3.5 mr-1" />
                    Text
                  </TabsTrigger>
                  <TabsTrigger value="image" className="px-2 py-1 h-8" onClick={() => handleAddBlock('image')}>
                    <Image className="h-3.5 w-3.5 mr-1" />
                    Image
                  </TabsTrigger>
                  <TabsTrigger value="video" className="px-2 py-1 h-8" onClick={() => handleAddBlock('video')}>
                    <Video className="h-3.5 w-3.5 mr-1" />
                    Video
                  </TabsTrigger>
                  <TabsTrigger value="quiz" className="px-2 py-1 h-8" onClick={() => handleAddBlock('quiz')}>
                    <HelpCircle className="h-3.5 w-3.5 mr-1" />
                    Quiz
                  </TabsTrigger>
                  <TabsTrigger value="code" className="px-2 py-1 h-8" onClick={() => handleAddBlock('code')}>
                    <Code className="h-3.5 w-3.5 mr-1" />
                    Code
                  </TabsTrigger>
                  <TabsTrigger value="embed" className="px-2 py-1 h-8" onClick={() => handleAddBlock('embed')}>
                    <ExternalLink className="h-3.5 w-3.5 mr-1" />
                    Embed
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          <div className="space-y-4">
            {editableLesson.contentBlocks.length === 0 ? (
              <div className="bg-sortmy-darker/30 border border-dashed border-sortmy-blue/20 rounded-lg p-8 text-center">
                <div className="flex flex-col items-center gap-2">
                  <LayoutGrid className="h-10 w-10 text-sortmy-blue/40" />
                  <h3 className="text-lg font-medium">No Content Blocks Added</h3>
                  <p className="text-sm text-gray-400 max-w-md">
                    Add text, images, videos, quizzes, code snippets, or embeds to create your lesson content.
                  </p>
                </div>
              </div>
            ) : (
              editableLesson.contentBlocks
                .sort((a, b) => a.order - b.order)
                .map((block, index) => (
                  <Card key={block.id} className="bg-sortmy-darker/70 border-sortmy-blue/20">
                    <CardHeader className="p-4 pb-0 flex flex-row items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div className="flex items-center justify-center h-6 w-6 rounded-full bg-sortmy-blue/10 text-sortmy-blue">
                          {getContentTypeIcon(block.type)}
                        </div>
                        <CardTitle className="text-sm">
                          {block.type.charAt(0).toUpperCase() + block.type.slice(1)} Block
                        </CardTitle>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-400"
                          onClick={() => handleMoveBlock(block.id, 'up')}
                          disabled={index === 0}
                        >
                          <ChevronUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-400"
                          onClick={() => handleMoveBlock(block.id, 'down')}
                          disabled={index === editableLesson.contentBlocks.length - 1}
                        >
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-red-500/70 hover:text-red-500 hover:bg-red-500/10"
                          onClick={() => handleDeleteBlock(block.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4">
                      <ContentBlockEditor
                        block={block}
                        onChange={handleUpdateBlock}
                      />
                    </CardContent>
                  </Card>
                ))
            )}
          </div>
        </div>

        <Separator className="my-6" />

        <div className="flex justify-end space-x-2 pt-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button type="submit" className="bg-sortmy-blue hover:bg-sortmy-blue/90">
            <Save className="mr-2 h-4 w-4" />
            Save Lesson
          </Button>
        </div>
      </form>
    </div>
  );
};

export default LessonEditor;
import { Tool } from '@/types/tools';

export type SuggestionType = 'name' | 'description' | 'category' | 'tag';

export interface SearchSuggestion {
    text: string;
    type: SuggestionType;
    source: 'user' | 'library' | 'both';
    toolId?: string;
}

export interface ToolSearchProps {
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    suggestions: SearchSuggestion[];
    showSuggestions: boolean;
    setShowSuggestions: (show: boolean) => void;
    handleSelectSuggestion: (suggestion: SearchSuggestion) => void;
    selectedSuggestionIndex: number;
    handleKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

export interface TagsFilterProps {
    allTags: string[];
    selectedTags: string[];
    toggleTag: (tag: string) => void;
    clearTags: () => void;
}

export interface ToolGridProps {
    tools: Tool[];
    isLoading?: boolean;
    error?: Error | null;
    onDelete?: (tool: Tool) => void;
    onEdit?: (tool: Tool) => void;
    onAdd?: (tool: Tool) => void;
    canEdit?: boolean;
    canDelete?: boolean;
    canAdd?: boolean;
    isAdmin?: boolean;
    savingToolIds?: string[];
    emptyMessage?: string;
}

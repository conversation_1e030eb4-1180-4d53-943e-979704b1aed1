import { useState } from 'react';
import { ContentBlock } from '@/types/academy';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle } from 'lucide-react';

interface QuizBlockProps {
  block: ContentBlock;
  onAttempt: (isCorrect: boolean) => void;
  isAttempted: boolean;
}

const QuizBlock = ({ block, onAttempt, isAttempted }: QuizBlockProps) => {
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  
  const handleSubmit = () => {
    if (selectedOption === null) return;
    
    const correct = selectedOption === block.correctOption;
    setIsCorrect(correct);
    setHasSubmitted(true);
    onAttempt(correct);
  };
  
  const handleTryAgain = () => {
    setSelectedOption(null);
    setHasSubmitted(false);
  };
  
  // If already attempted and passed in previous session
  if (isAttempted && !hasSubmitted) {
    return (
      <div className="space-y-4">
        <div className="prose prose-invert max-w-none">
          <h3>{block.content}</h3>
        </div>
        
        <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <p className="text-green-400">You've already completed this question correctly!</p>
          </div>
        </div>
        
        <div className="space-y-2">
          {block.options?.map((option, index) => (
            <div 
              key={index}
              className={`
                p-4 border rounded-lg 
                ${index === block.correctOption ? 'bg-green-500/10 border-green-500/20' : 'border-sortmy-blue/20'}
              `}
            >
              {option}
              {index === block.correctOption && (
                <div className="mt-2 flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-sm text-green-400">Correct answer</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="prose prose-invert max-w-none">
        <h3>{block.content}</h3>
      </div>
      
      {hasSubmitted ? (
        <>
          <div className={`p-4 ${isCorrect ? 'bg-green-500/10 border border-green-500/20' : 'bg-red-500/10 border border-red-500/20'} rounded-lg`}>
            <div className="flex items-center">
              {isCorrect ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <p className="text-green-400">Correct answer!</p>
                </>
              ) : (
                <>
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  <p className="text-red-400">Incorrect answer. Try again!</p>
                </>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            {block.options?.map((option, index) => {
              const isSelected = index === selectedOption;
              const isCorrectOption = index === block.correctOption;
              
              return (
                <div 
                  key={index}
                  className={`
                    p-4 border rounded-lg 
                    ${isCorrectOption ? 'bg-green-500/10 border-green-500/20' : ''}
                    ${isSelected && !isCorrectOption ? 'bg-red-500/10 border-red-500/20' : ''}
                    ${!isSelected && !isCorrectOption ? 'border-sortmy-blue/20' : ''}
                  `}
                >
                  {option}
                  {isCorrectOption && (
                    <div className="mt-2 flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-sm text-green-400">Correct answer</span>
                    </div>
                  )}
                  {isSelected && !isCorrectOption && (
                    <div className="mt-2 flex items-center">
                      <XCircle className="h-4 w-4 text-red-500 mr-2" />
                      <span className="text-sm text-red-400">Your selection</span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          
          {!isCorrect && (
            <Button onClick={handleTryAgain} variant="outline">
              Try Again
            </Button>
          )}
        </>
      ) : (
        <>
          <RadioGroup value={selectedOption?.toString() || ""} onValueChange={(value) => setSelectedOption(parseInt(value))}>
            {block.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2 p-4 border border-sortmy-blue/20 rounded-lg mb-2 hover:bg-sortmy-blue/5">
                <RadioGroupItem value={index.toString()} id={`option-${block.id}-${index}`} />
                <Label className="flex-1 cursor-pointer" htmlFor={`option-${block.id}-${index}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
          
          <Button 
            onClick={handleSubmit} 
            disabled={selectedOption === null}
            className={selectedOption !== null ? "bg-sortmy-blue hover:bg-sortmy-blue/90" : "bg-gray-700"}
          >
            Submit Answer
          </Button>
        </>
      )}
    </div>
  );
};

export default QuizBlock;
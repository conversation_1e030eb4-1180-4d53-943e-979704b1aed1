# Portfolio System Upgrade: Firebase Storage + Livepeer Integration

## Overview

This upgrade replaces the Google Drive-based portfolio system with a more robust solution using Firebase Storage for images and Livepeer for videos, along with comprehensive draft management and automated cleanup.

## Key Improvements

### 🔄 **Storage Migration**
- **From**: Google Drive for all media
- **To**: Firebase Storage for images, Livepeer for videos
- **Benefits**: Better performance, reliability, and cost efficiency

### 📝 **Draft Management**
- Auto-save every 30 seconds
- Separate `portfolio_drafts` collection
- Resume interrupted uploads
- Automatic cleanup of abandoned drafts (7 days)

### 🗂️ **Archive System**
- Move old items to `portfolio_archives` collection
- Automatic cleanup of expired archives (90 days)
- Easy restoration of archived items

### 🧹 **Automated Cleanup**
- Orphaned file detection and removal
- Scheduled cleanup tasks
- Storage usage tracking
- Livepeer asset management

## New Components

### Core Components
1. **`FirebaseImageUploader.tsx`** - Handles image uploads with compression
2. **`PortfolioMediaUploader.tsx`** - Unified media upload interface
3. **`DraftManager.tsx`** - Draft management and auto-save
4. **`EnhancedPortfolioForm.tsx`** - New portfolio form with all features
5. **`EnhancedAddPortfolio.tsx`** - Updated add portfolio page

### Services
1. **`storageCleanupService.ts`** - Handles file cleanup operations
2. **`portfolioStorageService.ts`** - Portfolio-specific storage operations
3. **`scheduledCleanupService.ts`** - Automated maintenance tasks

## Database Schema

### New Collections

#### `portfolio_drafts`
```typescript
{
  id: string;
  userId: string;
  title: string;
  description: string;
  media_url?: string;
  media_urls?: string[];
  media_type?: 'image' | 'video' | 'audio';
  content_type: 'post' | 'reel' | 'both';
  tools_used: string[];
  tagged_toolkits?: string[];
  project_url?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  auto_save_count: number;
  storage_metadata?: {
    file_paths: string[];
    file_sizes: number[];
    upload_timestamps: string[];
    livepeer_asset_id?: string;
  };
}
```

#### `portfolio_archives`
```typescript
{
  // Same as portfolio item +
  archived_at: string;
}
```

#### `storage_cleanup_queue`
```typescript
{
  filePath: string;
  fileUrl: string;
  userId: string;
  reason: 'draft_abandoned' | 'item_deleted' | 'orphaned' | 'archive_expired';
  scheduledFor: Timestamp;
  createdAt: Timestamp;
  attempts: number;
  lastAttempt?: Timestamp;
  error?: string;
}
```

#### `storage_usage`
```typescript
{
  userId: string;
  totalSize: number;
  fileCount: number;
  lastUpdated: Timestamp;
  breakdown: {
    portfolio: number;
    drafts: number;
    archives: number;
  };
}
```

## Storage Structure

### Firebase Storage Paths
```
/portfolio/{userId}/{timestamp}-{randomId}.{ext}
/portfolio-drafts/{userId}/{timestamp}-{randomId}.{ext}
/portfolio-archives/{userId}/{timestamp}-{randomId}.{ext}
```

### Enhanced Livepeer Integration
- Better error handling and retry logic
- Improved draft management for video uploads
- Asset cleanup when uploads are abandoned
- Metadata preservation (title, description, tags)

## Security Rules

### Firebase Storage Rules
```javascript
// Portfolio files
match /portfolio/{userId}/{allPaths=**} {
  allow read: if true;
  allow write, delete: if request.auth != null && request.auth.uid == userId;
}

// Draft files
match /portfolio-drafts/{userId}/{allPaths=**} {
  allow read, write, delete: if request.auth != null && request.auth.uid == userId;
}

// Archive files
match /portfolio-archives/{userId}/{allPaths=**} {
  allow read, write, delete: if request.auth != null && request.auth.uid == userId;
}

// Admin cleanup access
match /{allPaths=**} {
  allow delete: if request.auth != null && request.auth.token.admin == true;
}
```

### Firestore Rules
```javascript
// Drafts collection
match /portfolio_drafts/{draftId} {
  allow read, write, delete: if isAuthenticated() && 
    resource.data.userId == request.auth.uid;
}

// Archives collection
match /portfolio_archives/{archiveId} {
  allow read, write, delete: if isAuthenticated() && 
    resource.data.userId == request.auth.uid;
}

// Cleanup queue (admin only)
match /storage_cleanup_queue/{queueId} {
  allow read, write, delete: if isAuthenticated() && 
    request.auth.token.admin == true;
}
```

## Usage Examples

### Basic Portfolio Creation
```typescript
import { EnhancedPortfolioForm } from '@/components/portfolio/EnhancedPortfolioForm';

<EnhancedPortfolioForm
  user={user}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

### Draft Management
```typescript
import { DraftManager } from '@/components/portfolio/DraftManager';

<DraftManager
  userId={user.id}
  onDraftSelect={handleDraftSelect}
  onDraftDelete={handleDraftDelete}
  onDraftPublish={handleDraftPublish}
/>
```

### Media Upload
```typescript
import { PortfolioMediaUploader } from '@/components/portfolio/PortfolioMediaUploader';

<PortfolioMediaUploader
  userId={user.id}
  onMediaUpload={handleMediaUpload}
  onError={handleError}
/>
```

### Cleanup Service
```typescript
import { setupAutomaticCleanup } from '@/services/scheduledCleanupService';

// In app initialization
useEffect(() => {
  setupAutomaticCleanup();
}, []);
```

## Migration Strategy

### Phase 1: Deploy New System
1. Deploy new components and services
2. Update Firebase rules and indexes
3. Test with new portfolio items

### Phase 2: Gradual Migration
1. Keep existing Google Drive system functional
2. Allow users to choose between old and new systems
3. Migrate user data gradually

### Phase 3: Complete Migration
1. Migrate all existing portfolio items
2. Remove Google Drive dependencies
3. Clean up old code and components

## Monitoring and Maintenance

### Automated Tasks
- **Daily**: Cleanup abandoned drafts and expired archives
- **Weekly**: Orphaned file detection and removal
- **Monthly**: Storage usage analysis and optimization

### Manual Tasks
- Monitor Livepeer usage and costs
- Review cleanup logs for issues
- Optimize storage rules based on usage patterns

## Benefits

### For Users
- ✅ Faster upload and loading times
- ✅ Better video streaming quality
- ✅ Automatic draft saving
- ✅ No more Google Drive authentication issues
- ✅ Better mobile experience

### For Developers
- ✅ Simplified codebase
- ✅ Better error handling
- ✅ Automated maintenance
- ✅ Improved monitoring
- ✅ Cost optimization

### For System
- ✅ Reduced storage costs
- ✅ Better performance
- ✅ Improved reliability
- ✅ Automated cleanup
- ✅ Better scalability

## Next Steps

1. **Test the new system** with the enhanced components
2. **Deploy to staging** environment for thorough testing
3. **Create migration scripts** for existing data
4. **Set up monitoring** for the new services
5. **Plan rollout strategy** for production deployment

## Support

For questions or issues with the new portfolio system:
1. Check the component documentation
2. Review the service logs
3. Test with the enhanced components
4. Monitor cleanup service status

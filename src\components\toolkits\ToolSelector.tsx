import { Tool, Toolkit } from '@/types/tools';
import { Search } from 'lucide-react';
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { Icons } from '@/components/icons';

interface ToolSelectorProps {
    availableTools: Tool[];
    availableToolkits?: Toolkit[]; // Add this prop for public toolkits
    selectedTools: string[];
    selectedToolkits?: string[];
    onChange: (selected: string[]) => void;
    onToolkitChange?: (selected: string[]) => void;
}

export function ToolSelector({
    availableTools,
    availableToolkits = [],
    selectedTools,
    selectedToolkits = [],
    onChange,
    onToolkitChange,
}: ToolSelectorProps) {
    const [searchQuery, setSearchQuery] = useState('');

    const filteredTools = availableTools.filter(tool =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const filteredToolkits = availableToolkits.filter(
        tk =>
            tk.source === 'library' &&
            tk.is_published &&
            tk.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const toggleTool = (toolId: string) => {
        const newSelected = selectedTools.includes(toolId)
            ? selectedTools.filter(id => id !== toolId)
            : [...selectedTools, toolId];
        onChange(newSelected);
    };

    const toggleToolkit = (toolkitId: string) => {
        if (!onToolkitChange) return;
        const newSelected = selectedToolkits.includes(toolkitId)
            ? selectedToolkits.filter(id => id !== toolkitId)
            : [...selectedToolkits, toolkitId];
        onToolkitChange(newSelected);
    };

    return (
        <div className="space-y-4">
            <div className="flex items-center gap-2">
                <Search className="w-4 h-4 text-gray-400" />
                <Input
                    placeholder="Search tools or toolkits..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-sortmy-darker border border-sortmy-blue/20 text-gray-200 placeholder:text-gray-400 focus:bg-sortmy-darker focus:border-sortmy-blue/40"
                />
            </div>

            <ScrollArea className="h-[300px] rounded-md border border-sortmy-blue/20 p-4 bg-sortmy-darker">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {/* Render tools */}
                    {filteredTools.map(tool => (
                        <button
                            key={tool.id}
                            onClick={() => toggleTool(tool.id)}
                            className={cn(
                                "flex flex-col items-center p-4 rounded-lg border transition-all",
                                selectedTools.includes(tool.id)
                                    ? "bg-sortmy-blue/20 border-sortmy-blue"
                                    : "border-sortmy-blue/20 hover:bg-sortmy-blue/10"
                            )}
                            type="button"
                        >
                            {(tool.logo_url || tool.logoUrl) ? (
                                <img
                                    src={tool.logo_url || tool.logoUrl}
                                    alt={tool.name}
                                    className="w-12 h-12 rounded-lg object-contain bg-sortmy-darker"
                                    onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                    }}
                                />
                            ) : (
                                <div className="w-12 h-12 rounded-lg bg-sortmy-blue/20 flex items-center justify-center">
                                    <Icons.Tool className="w-6 h-6 text-sortmy-blue/60" />
                                </div>
                            )}
                            <span className="mt-2 text-sm text-center font-medium text-gray-200">{tool.name}</span>
                        </button>
                    ))}
                    {/* Render public toolkits */}
                    {filteredToolkits.map(toolkit => (
                        <button
                            key={toolkit.id}
                            onClick={() => toggleToolkit(toolkit.id)}
                            className={cn(
                                "flex flex-col items-center p-4 rounded-lg border transition-all",
                                selectedToolkits.includes(toolkit.id)
                                    ? "bg-purple-800/30 border-purple-500"
                                    : "border-purple-500/20 hover:bg-purple-800/10"
                            )}
                            type="button"
                        >
                            {toolkit.logo_url ? (
                                <img
                                    src={toolkit.logo_url}
                                    alt={toolkit.name}
                                    className="w-12 h-12 rounded-lg object-contain bg-sortmy-darker"
                                    onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                    }}
                                />
                            ) : (
                                <div className="w-12 h-12 rounded-lg bg-purple-800/20 flex items-center justify-center">
                                    <Icons.Tool className="w-6 h-6 text-purple-400" />
                                </div>
                            )}
                            <span className="mt-2 text-sm text-center font-medium text-gray-200">{toolkit.name}</span>
                        </button>
                    ))}
                </div>
            </ScrollArea>
        </div>
    );
}


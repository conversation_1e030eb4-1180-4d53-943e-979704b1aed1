import { useState, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>List, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import NeonButton from '@/components/ui/NeonButton';

export interface AddProjectFormProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onAdd: (item: any) => void;
    isSubmitting?: boolean;
}

export default function AddProjectForm({ open, onOpenChange, onAdd, isSubmitting = false }: AddProjectFormProps) {
    const [addForm, setAddForm] = useState({
        title: '',
        description: '',
        tools_used: '',
        videoUrl: '',
        gdriveUrl: '',
        uploadMethod: 'livepeer',
    });
    const [uploading, setUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [uploadError, setUploadError] = useState('');
    const fileInputRef = useRef<HTMLInputElement>(null); const handleLivepeerUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 900000); // 15 minute timeout

        try {
            setUploading(true);
            setUploadError('');
            setUploadProgress(0);

            const apiKey = import.meta.env.VITE_LIVEPEER_API_KEY;
            const apiUrl = import.meta.env.VITE_LIVEPEER_API_URL;

            if (!apiKey) {
                throw new Error('Livepeer API key not found');
            }

            // Direct upload using v1 endpoint
            setUploadProgress(20);
            const formData = new FormData();
            formData.append('file', file);
            formData.append('name', file.name);

            // Using v1 endpoint structure
            const uploadRes = await fetch(`${apiUrl}/api/asset/request-upload`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: file.name,
                }),
                signal: controller.signal
            });

            if (!uploadRes.ok) {
                const errorText = await uploadRes.text();
                throw new Error(`Upload request failed: ${errorText}`);
            }

            const { url: uploadUrl, asset } = await uploadRes.json();

            if (!uploadUrl) {
                throw new Error('Upload URL not found in response');
            }

            // Upload the file
            setUploadProgress(30);
            const uploadFileRes = await fetch(uploadUrl, {
                method: 'PUT',
                headers: {
                    'Content-Type': file.type,
                },
                body: file,
                signal: controller.signal
            });

            if (!uploadFileRes.ok) {
                throw new Error('Failed to upload file to storage');
            }

            setUploadProgress(50);

            // Poll for asset status
            let status = asset.status?.phase || 'processing';
            let playbackUrl = '';
            let retryCount = 0;
            const maxRetries = 30; // 30 retries with exponential backoff
            let delay = 2000; // Start with 2 second delay

            while (status !== 'ready' && retryCount < maxRetries) {
                await new Promise(r => setTimeout(r, delay));
                try {
                    const pollRes = await fetch(`${apiUrl}/api/asset/${asset.id}`, {
                        headers: {
                            'Authorization': `Bearer ${apiKey}`,
                            'Accept': 'application/json'
                        },
                        signal: controller.signal
                    });

                    if (!pollRes.ok) {
                        throw new Error(`Failed to check upload status: ${pollRes.status}`);
                    }

                    const pollData = await pollRes.json();
                    status = pollData.asset.status?.phase || status;
                    playbackUrl = pollData.asset.playbackUrl;

                    // Calculate progress based on status
                    const progressMap: Record<string, number> = {
                        'waiting': 60,
                        'processing': 80,
                        'ready': 100,
                        'failed': 0
                    };
                    setUploadProgress(progressMap[status] || 70);

                    if (status === 'ready' && playbackUrl) {
                        break;
                    } else if (status === 'failed') {
                        throw new Error('Asset processing failed');
                    }

                    retryCount++;
                    delay = Math.min(delay * 1.5, 10000); // Exponential backoff, max 10s delay
                } catch (pollError) {
                    if (pollError instanceof Error && pollError.message.includes('failed')) {
                        throw pollError;
                    }
                    retryCount++;
                    delay = Math.min(delay * 1.5, 10000);
                }
            }

            if (!playbackUrl) {
                throw new Error('Upload processing timed out or failed to get playback URL');
            }

            setAddForm(f => ({ ...f, videoUrl: playbackUrl, gdriveUrl: '' }));
            setUploadProgress(100);
        } catch (err: any) {
            console.error('Livepeer upload error:', err);
            let errorMessage = 'Upload failed: ';
            if (err.name === 'AbortError') {
                errorMessage += 'Request timed out after 15 minutes';
            } else if (err instanceof Error) {
                errorMessage += err.message;
            } else {
                errorMessage += 'Unknown error occurred';
            }
            setUploadError(errorMessage);
            setUploadProgress(0);
        } finally {
            clearTimeout(timeoutId);
            setUploading(false);
        }
    };

    const handleGDriveUrl = (e: React.ChangeEvent<HTMLInputElement>) => {
        setAddForm(f => ({ ...f, gdriveUrl: e.target.value, videoUrl: '' }));
    };

    const handleAddFormSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!addForm.title || !addForm.description || (!addForm.videoUrl && !addForm.gdriveUrl)) {
            setUploadError('Please fill all required fields and upload a video.');
            return;
        }
        setUploading(true);
        setUploadError('');
        try {
            const newItem = {
                id: `local-${Date.now()}`,
                title: addForm.title,
                description: addForm.description,
                tools_used: addForm.tools_used ? addForm.tools_used.split(',').map(t => t.trim()) : [],
                media_url: addForm.videoUrl || addForm.gdriveUrl,
                media_type: 'video',
                content_type: 'post',
                categories: [],
                likes: 0,
                status: 'published',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                views: 0,
                is_public: true,
            };
            onAdd(newItem);
            onOpenChange(false);
            setAddForm({ title: '', description: '', tools_used: '', videoUrl: '', gdriveUrl: '', uploadMethod: 'livepeer' });
            setUploadProgress(0);
        } catch (err) {
            setUploadError('Failed to add project.');
        } finally {
            setUploading(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-[600px] bg-sortmy-dark border-sortmy-blue/20 backdrop-blur-md">
                <DialogTitle>Add New Project</DialogTitle>
                <DialogDescription>
                    Fill out the form below to add a new project. Upload a video via Livepeer or Google Drive.
                </DialogDescription>
                <form onSubmit={handleAddFormSubmit} className="space-y-4">
                    <Input
                        placeholder="Project Title"
                        value={addForm.title}
                        onChange={e => setAddForm(f => ({ ...f, title: e.target.value }))}
                        required
                        disabled={uploading || isSubmitting}
                    />
                    <Textarea
                        placeholder="Description"
                        value={addForm.description}
                        onChange={e => setAddForm(f => ({ ...f, description: e.target.value }))}
                        required
                        disabled={uploading || isSubmitting}
                    />
                    <Input
                        placeholder="Tools Used (comma separated)"
                        value={addForm.tools_used}
                        onChange={e => setAddForm(f => ({ ...f, tools_used: e.target.value }))}
                        disabled={uploading || isSubmitting}
                    />
                    <Tabs value={addForm.uploadMethod} onValueChange={val => setAddForm(f => ({ ...f, uploadMethod: val }))}>
                        <TabsList>
                            <TabsTrigger value="livepeer" disabled={uploading || isSubmitting}>Livepeer Video (Recommended)</TabsTrigger>
                            <TabsTrigger value="gdrive" disabled={uploading || isSubmitting}>Google Drive Link</TabsTrigger>
                        </TabsList>
                        <TabsContent value="livepeer">
                            <Input
                                type="file"
                                accept="video/*"
                                ref={fileInputRef}
                                onChange={handleLivepeerUpload}
                                disabled={uploading || isSubmitting}
                            />
                            {uploading && <div className="text-cyan-400">Uploading... {uploadProgress}%</div>}
                            {addForm.videoUrl && (
                                <video src={addForm.videoUrl} controls className="w-full mt-2 rounded" />
                            )}
                        </TabsContent>
                        <TabsContent value="gdrive">
                            <Input
                                placeholder="Google Drive Video URL"
                                value={addForm.gdriveUrl}
                                onChange={handleGDriveUrl}
                                disabled={uploading || isSubmitting}
                            />
                            {addForm.gdriveUrl && (
                                <video src={addForm.gdriveUrl} controls className="w-full mt-2 rounded" />
                            )}
                        </TabsContent>
                    </Tabs>
                    {uploadError && <div className="text-red-400 text-sm">{uploadError}</div>}
                    <div className="flex justify-end gap-2">
                        <NeonButton type="button" variant="cyan" onClick={() => onOpenChange(false)} disabled={uploading || isSubmitting}>
                            Cancel
                        </NeonButton>
                        <NeonButton type="submit" variant="gradient" disabled={uploading || isSubmitting}>
                            {uploading ? 'Uploading...' : isSubmitting ? 'Saving...' : 'Add Project'}
                        </NeonButton>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
}


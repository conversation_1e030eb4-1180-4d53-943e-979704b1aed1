import { doc, updateDoc, increment, getDoc, serverTimestamp, collection, addDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { User } from '@/types';
import {
  XP_REWARDS,
  ActivityType,
  calculateLevel,
  calculateLevelProgress,
  calculateStreakBonus,
  shouldGetDailyLoginXP,
  calculateNewStreak
} from '@/utils/xpSystem';

// XP Service for managing user experience points
export class XPService {

  // Award XP to user for specific activity
  static async awardXP(
    userId: string,
    activityType: ActivityType,
    description: string,
    metadata?: Record<string, any>
  ): Promise<{ xpEarned: number; newLevel: number; leveledUp: boolean }> {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data() as User;
      const baseXP = XP_REWARDS[activityType];
      let totalXPEarned = baseXP;

      // Check for first-time bonuses
      const isFirstTime = await this.checkFirstTimeActivity(userId, activityType);
      if (isFirstTime && this.hasFirstTimeBonus(activityType)) {
        totalXPEarned += XP_REWARDS.FIRST_TIME_BONUS;
      }

      // Calculate new totals
      const currentXP = userData.xp || 0;
      const newTotalXP = currentXP + totalXPEarned;
      const currentLevel = userData.level || 1;
      const newLevel = calculateLevel(newTotalXP);
      const leveledUp = newLevel > currentLevel;

      // Update user document
      await updateDoc(userRef, {
        xp: increment(totalXPEarned),
        level: newLevel,
        last_activity: serverTimestamp()
      });

      // Log the activity
      await this.logXPActivity(userId, activityType, totalXPEarned, description, metadata);

      return {
        xpEarned: totalXPEarned,
        newLevel,
        leveledUp
      };

    } catch (error) {
      console.error('Error awarding XP:', error);
      throw error;
    }
  }

  // Handle daily login XP and streak calculation
  static async handleDailyLogin(userId: string): Promise<{
    xpEarned: number;
    streakBonus: number;
    newStreak: number;
    leveledUp: boolean;
    newLevel: number;
  }> {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data() as User;
      const lastLogin = userData.last_login || new Date().toISOString();

      // Check if user should get daily login XP
      if (!shouldGetDailyLoginXP(lastLogin)) {
        return { xpEarned: 0, streakBonus: 0, newStreak: userData.streak_days || 0, leveledUp: false, newLevel: userData.level || 1 };
      }

      // Calculate new streak
      const currentStreak = userData.streak_days || 0;
      const streakChange = calculateNewStreak(lastLogin);
      let newStreak: number;

      if (streakChange === -999) {
        // Streak broken, reset to 1
        newStreak = 1;
      } else {
        // Continue or maintain streak
        newStreak = currentStreak + streakChange;
      }

      // Calculate XP rewards
      let totalXPEarned = XP_REWARDS.DAILY_LOGIN;
      const streakBonus = calculateStreakBonus(newStreak);
      totalXPEarned += streakBonus;

      // Calculate level changes
      const currentXP = userData.xp || 0;
      const newTotalXP = currentXP + totalXPEarned;
      const currentLevel = userData.level || 1;
      const newLevel = calculateLevel(newTotalXP);
      const leveledUp = newLevel > currentLevel;

      // Update user document
      await updateDoc(userRef, {
        xp: increment(totalXPEarned),
        level: newLevel,
        streak_days: newStreak,
        last_login: new Date().toISOString(),
        last_activity: serverTimestamp()
      });

      // Log the activity
      await this.logXPActivity(
        userId,
        'DAILY_LOGIN',
        totalXPEarned,
        `Daily login (${newStreak} day streak)`,
        { streakDays: newStreak, streakBonus }
      );

      return {
        xpEarned: totalXPEarned,
        streakBonus,
        newStreak,
        leveledUp,
        newLevel
      };

    } catch (error) {
      console.error('Error handling daily login:', error);
      throw error;
    }
  }

  // Check if this is the first time user performs this activity
  private static async checkFirstTimeActivity(_userId: string, _activityType: ActivityType): Promise<boolean> {
    try {
      // const activitiesRef = collection(db, 'xp_activities');
      // In a real implementation, you'd query for existing activities of this type
      // For now, we'll implement a simple check based on user data
      return false; // Simplified for now
    } catch (error) {
      console.error('Error checking first time activity:', error);
      return false;
    }
  }

  // Check if activity type has first-time bonus
  private static hasFirstTimeBonus(activityType: ActivityType): boolean {
    const firstTimeBonusActivities: ActivityType[] = [
      'CREATE_FIRST_PORTFOLIO',
      'CREATE_TOOL',
      'CREATE_TOOLKIT',
      'COMPLETE_LESSON'
    ];
    return firstTimeBonusActivities.includes(activityType);
  }

  // Log XP activity for tracking
  private static async logXPActivity(
    userId: string,
    activityType: ActivityType,
    xpEarned: number,
    description: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const activitiesRef = collection(db, 'xp_activities');
      await addDoc(activitiesRef, {
        userId,
        activityType,
        xpEarned,
        description,
        metadata: metadata || {},
        timestamp: serverTimestamp()
      });
    } catch (error) {
      console.error('Error logging XP activity:', error);
      // Don't throw error here to avoid breaking the main flow
    }
  }

  // Get user's XP progress information
  static async getUserXPProgress(userId: string): Promise<{
    totalXP: number;
    level: number;
    currentLevelXP: number;
    xpForNextLevel: number;
    progressPercentage: number;
    streakDays: number;
  }> {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data() as User;
      const totalXP = userData.xp || 0;
      const level = userData.level || 1;
      const streakDays = userData.streak_days || 0;

      const progress = calculateLevelProgress(totalXP, level);

      return {
        totalXP,
        level,
        currentLevelXP: progress.currentLevelXP,
        xpForNextLevel: progress.xpForNextLevel,
        progressPercentage: progress.progressPercentage,
        streakDays
      };

    } catch (error) {
      console.error('Error getting user XP progress:', error);
      throw error;
    }
  }
}

// Convenience functions for common activities
export const XPActions = {
  addToolToLibrary: (userId: string, toolName: string) =>
    XPService.awardXP(userId, 'ADD_TOOL_TO_LIBRARY', `Added ${toolName} to library`),

  createTool: (userId: string, toolName: string) =>
    XPService.awardXP(userId, 'CREATE_TOOL', `Created tool: ${toolName}`),

  createToolkit: (userId: string, toolkitName: string) =>
    XPService.awardXP(userId, 'CREATE_TOOLKIT', `Created toolkit: ${toolkitName}`),

  createFirstPortfolio: (userId: string) =>
    XPService.awardXP(userId, 'CREATE_FIRST_PORTFOLIO', 'Created first portfolio item'),

  createPortfolioItem: (userId: string, itemTitle: string) =>
    XPService.awardXP(userId, 'CREATE_PORTFOLIO_ITEM', `Created portfolio: ${itemTitle}`),

  completeLesson: (userId: string, lessonTitle: string) =>
    XPService.awardXP(userId, 'COMPLETE_LESSON', `Completed lesson: ${lessonTitle}`),

  createPost: (userId: string, postTitle: string) =>
    XPService.awardXP(userId, 'CREATE_POST', `Created post: ${postTitle}`),

  shareTool: (userId: string, toolName: string) =>
    XPService.awardXP(userId, 'SHARE_TOOL', `Shared tool: ${toolName}`),

  dailyLogin: (userId: string) =>
    XPService.handleDailyLogin(userId)
};

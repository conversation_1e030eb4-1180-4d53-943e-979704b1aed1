import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { useAuth } from '@/hooks/useAuth';
import type { Course, Lesson as OldLesson, UserProgress } from '@/types/course';
import type { Lesson as AcademyLesson } from '@/types/academy';
import GlassCard from '@/components/ui/GlassCard';
import LessonView from '@/components/academy/LessonView';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { ArrowLeft, CheckCircle, ChevronRight, Zap } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';

const CourseView: React.FC = () => {
    const { courseId } = useParams<{ courseId: string }>();
    const navigate = useNavigate();
    const { user } = useAuth();
    const { toast } = useToast();

    const [course, setCourse] = useState<Course | null>(null);
    const [currentModuleIdx, setCurrentModuleIdx] = useState(0);
    const [currentLessonIdx, setCurrentLessonIdx] = useState(0);
    const [userProgress, setUserProgress] = useState<UserProgress | null>(null);
    const [loading, setLoading] = useState(true);

    // Helper: flatten all lessons for progress calculation
    const allLessons = course
        ? course.modules.flatMap((mod) => mod.lessons.map((lesson) => ({ ...lesson, moduleId: mod.id })))
        : [];

    // Helper: completed lessons map
    const completedLessons = userProgress?.completedLessons || {};

    // Progress calculation
    const progress =
        allLessons.length > 0
            ? Math.round(
                (Object.values(completedLessons).filter(Boolean).length / allLessons.length) * 100
            )
            : 0;

    // Memoized loadCourseAndProgress to avoid unnecessary reruns
    const loadCourseAndProgress = useCallback(async () => {
        if (!courseId || !user?.uid) {
            return;
        }
        try {
            const courseRef = doc(db, 'courses', courseId);
            const courseDoc = await getDoc(courseRef);
            if (!courseDoc.exists()) {
                setCourse(null);
                setLoading(false);
                return;
            }
            const courseData = courseDoc.data();
            setCourse(courseData as Course);

            // Always ensure userProgress doc exists before reading
            const progressRef = doc(db, 'userProgress', `${user.uid}_${courseId}`);
            let progressDoc;
            try {
                progressDoc = await getDoc(progressRef);
            } catch {
                const newProgress: UserProgress = {
                    userId: user.uid,
                    courseId,
                    status: 'not-started',
                    completedLessons: {},
                    completedModules: {},
                    quizScores: {},
                    earnedXp: 0,
                    quizAttempts: [],
                    currentLesson: { moduleId: '', lessonId: '' },
                    updatedAt: new Date().toISOString(),
                    lastAccessedAt: new Date().toISOString(),
                };
                await setDoc(progressRef, newProgress);
                progressDoc = await getDoc(progressRef);
            }
            if (!progressDoc.exists()) {
                const newProgress: UserProgress = {
                    userId: user.uid,
                    courseId,
                    status: 'not-started',
                    completedLessons: {},
                    completedModules: {},
                    quizScores: {},
                    earnedXp: 0,
                    quizAttempts: [],
                    currentLesson: { moduleId: '', lessonId: '' },
                    updatedAt: new Date().toISOString(),
                    lastAccessedAt: new Date().toISOString(),
                };
                await setDoc(progressRef, newProgress);
                progressDoc = await getDoc(progressRef);
            }
            setUserProgress(progressDoc.data() as UserProgress);

            // Set initial module and lesson
            const firstModuleIdx = courseData.modules?.findIndex(
                (mod: any) => Array.isArray(mod.lessons) && mod.lessons.length > 0
            );
            setCurrentModuleIdx(firstModuleIdx >= 0 ? firstModuleIdx : 0);
            setCurrentLessonIdx(0);
        } catch (error) {
            setCourse(null);
        } finally {
            setLoading(false);
        }
    }, [courseId, user?.uid]);

    // Load course and progress
    useEffect(() => {
        if (courseId && user?.uid) {
            loadCourseAndProgress();
        }
    }, [courseId, user?.uid, loadCourseAndProgress]);

    // Navigation helpers
    const currentModule = course?.modules[currentModuleIdx] || null;
    const currentLesson = currentModule?.lessons[currentLessonIdx] || null;

    const handleLessonComplete = async () => {
        if (!currentLesson || !userProgress) return;
        const updatedProgress = {
            ...userProgress,
            completedLessons: {
                ...userProgress.completedLessons,
                [currentLesson.id]: true,
            },
            updatedAt: new Date().toISOString(),
        };
        if (currentModule) {
            const allLessonsComplete = currentModule.lessons.every(
                (lesson) => updatedProgress.completedLessons[lesson.id]
            );
            if (allLessonsComplete) {
                updatedProgress.completedModules = {
                    ...updatedProgress.completedModules,
                    [currentModule.id]: true,
                };
            }
        }
        try {
            await updateDoc(doc(db, 'userProgress', `${user!.uid}_${courseId}`), updatedProgress);
            setUserProgress(updatedProgress);
            toast({
                title: "Lesson completed",
                description:
                    currentLessonIdx < (currentModule?.lessons.length ?? 0) - 1
                        ? "Move on to the next lesson!"
                        : "You've completed this module!",
            });
        } catch (error) { }
    };

    const handleNextLesson = () => {
        if (!currentModule || !currentLesson) return;
        if (currentLessonIdx < currentModule.lessons.length - 1) {
            setCurrentLessonIdx((idx) => idx + 1);
            window.scrollTo(0, 0);
        } else if (currentModuleIdx < (course?.modules.length ?? 0) - 1) {
            setCurrentModuleIdx((idx) => idx + 1);
            setCurrentLessonIdx(0);
            window.scrollTo(0, 0);
        } else {
            toast({
                title: "Course completed!",
                description: "Congratulations! You've finished all modules.",
            });
            // Optionally navigate or show summary
        }
    };

    const handlePreviousLesson = () => {
        if (!currentModule || !currentLesson) return;
        if (currentLessonIdx > 0) {
            setCurrentLessonIdx((idx) => idx - 1);
            window.scrollTo(0, 0);
        } else if (currentModuleIdx > 0) {
            const prevModule = course?.modules[currentModuleIdx - 1];
            if (prevModule && prevModule.lessons.length > 0) {
                setCurrentModuleIdx((idx) => idx - 1);
                setCurrentLessonIdx(prevModule.lessons.length - 1);
                window.scrollTo(0, 0);
            }
        }
    };

    const handleSelectLesson = (moduleIdx: number, lessonIdx: number) => {
        // Only allow selecting completed lessons or the next available one
        const lesson = course?.modules[moduleIdx]?.lessons[lessonIdx];
        if (!lesson) return;
        const prevLesson =
            lessonIdx > 0
                ? course?.modules[moduleIdx]?.lessons[lessonIdx - 1]
                : moduleIdx > 0
                    ? course?.modules[moduleIdx - 1]?.lessons.slice(-1)[0]
                    : null;
        if (
            lessonIdx === 0 ||
            completedLessons[lesson.id] ||
            (prevLesson && completedLessons[prevLesson.id])
        ) {
            setCurrentModuleIdx(moduleIdx);
            setCurrentLessonIdx(lessonIdx);
            window.scrollTo(0, 0);
        } else {
            toast({
                title: "Lesson locked",
                description: "Complete the previous lessons first to unlock this one",
                variant: "destructive",
            });
        }
    };

    // Helper: Map lesson to AcademyLesson (for LessonView)
    const mapLessonToAcademy = (lesson: OldLesson, isCompleted: boolean): AcademyLesson => {
        // Handle video-text lessons (YouTube embed)
        if (lesson.type === 'video-text' && (lesson as { videoUrl?: string }).videoUrl) {
            return {
                id: lesson.id,
                title: lesson.title,
                description: 'Lesson',
                order: lesson.order,
                duration: (lesson as { duration?: number }).duration,
                isCompleted,
                contentBlocks: [
                    {
                        id: `${lesson.id}-content`,
                        type: 'video',
                        order: 0,
                        content: (lesson as { videoUrl?: string }).videoUrl ?? '',
                        options: [],
                        correctOption: undefined,
                        caption: lesson.content,
                    },
                ],
            };
        }
        // Handle image lessons
        if (lesson.type === 'text-image' && (lesson as { imageUrl?: string }).imageUrl) {
            return {
                id: lesson.id,
                title: lesson.title,
                description: 'Lesson',
                order: lesson.order,
                duration: (lesson as { duration?: number }).duration,
                isCompleted,
                contentBlocks: [
                    {
                        id: `${lesson.id}-content`,
                        type: 'image',
                        order: 0,
                        content: (lesson as { imageUrl?: string }).imageUrl ?? '',
                        options: [],
                        correctOption: undefined,
                        caption: lesson.content,
                    },
                ],
            };
        }
        // Default mapping for text/mcq
        return {
            id: lesson.id,
            title: lesson.title,
            description: 'Lesson',
            order: lesson.order,
            duration: (lesson as { duration?: number }).duration,
            isCompleted,
            contentBlocks: [
                {
                    id: `${lesson.id}-content`,
                    type:
                        lesson.type === 'mcq'
                            ? 'quiz'
                            : 'text',
                    order: 0,
                    content: lesson.content,
                    options: lesson.questions?.[0]?.options ?? [],
                    correctOption: lesson.questions?.[0]?.correctOption,
                    caption: undefined,
                },
            ],
        };
    };

    // Helper: get AcademyLesson for current lesson
    const getCurrentAcademyLesson = () => {
        if (!currentLesson) return undefined;
        return mapLessonToAcademy(
            currentLesson,
            !!completedLessons[currentLesson.id]
        );
    };

    if (loading) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center min-h-screen">
                    <div className="text-center">
                        <div className="animate-spin h-8 w-8 border-4 border-sortmy-blue border-t-transparent rounded-full mx-auto mb-4"></div>
                        <p>Loading course content...</p>
                    </div>
                </div>
            </DashboardLayout>
        );
    }
    if (!course) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center min-h-screen">
                    <div className="text-center">
                        <p>Course not found. Please try another course.</p>
                        <Button onClick={() => navigate('/dashboard/academy')} className="mt-4">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Academy
                        </Button>
                    </div>
                </div>
            </DashboardLayout>
        );
    }
    if (!currentModule || !currentLesson) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center h-64">
                    <p className="text-gray-400">No lessons available for this course.</p>
                </div>
            </DashboardLayout>
        );
    }

    return (
        <DashboardLayout>
            <div className="flex flex-col min-h-screen gap-6 p-4">
                <GlassCard className="p-6">
                    <div className="space-y-4">
                        <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => navigate('/dashboard/academy')}
                                    className="text-gray-400 hover:text-white"
                                >
                                    <ArrowLeft className="w-4 h-4 mr-1" />
                                    Back to Academy
                                </Button>
                            </div>
                            <div className="flex items-center bg-sortmy-blue/10 px-3 py-1.5 rounded-full">
                                <Zap className="w-4 h-4 text-sortmy-blue mr-1.5" />
                                <span className="text-sm font-medium text-sortmy-blue">
                                    +{(currentModule as any).xpReward ?? 0} XP
                                </span>
                            </div>
                        </div>
                        <div>
                            <h1 className="text-2xl font-bold text-white mb-2">{course.title}</h1>
                            <p className="text-gray-400">{course.description}</p>
                        </div>
                        <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                                <span className="text-gray-400">Course Progress</span>
                                <span className="text-gray-400">{progress}%</span>
                            </div>
                            <Progress value={progress} className="h-2" />
                        </div>
                    </div>
                </GlassCard>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {/* Lesson navigation sidebar */}
                    <div className="md:col-span-1">
                        <GlassCard className="p-4 sticky top-4">
                            <h3 className="text-lg font-semibold mb-4">Lessons</h3>
                            <div className="space-y-2">
                                {course.modules.map((mod, mIdx) =>
                                    mod.lessons.map((lesson, lIdx) => {
                                        // Determine if lesson is accessible
                                        const prevLesson =
                                            lIdx > 0
                                                ? mod.lessons[lIdx - 1]
                                                : mIdx > 0
                                                    ? course.modules[mIdx - 1].lessons.slice(-1)[0]
                                                    : null;
                                        const isAccessible =
                                            lIdx === 0 ||
                                            completedLessons[lesson.id] ||
                                            (prevLesson && completedLessons[prevLesson.id]);
                                        const isActive =
                                            mIdx === currentModuleIdx && lIdx === currentLessonIdx;
                                        return (
                                            <div
                                                key={lesson.id}
                                                className={`
                          p-3 rounded-lg flex items-center justify-between cursor-pointer
                          ${isActive ? 'bg-sortmy-blue/20 text-white' : 'hover:bg-sortmy-blue/10'}
                          ${!isAccessible ? 'opacity-50 cursor-not-allowed' : ''}
                        `}
                                                onClick={() => isAccessible && handleSelectLesson(mIdx, lIdx)}
                                            >
                                                <div className="flex items-center">
                                                    <div
                                                        className={`w-6 h-6 flex items-center justify-center rounded-full mr-2 ${completedLessons[lesson.id]
                                                            ? 'bg-green-500/20 text-green-400'
                                                            : 'bg-sortmy-blue/20 text-sortmy-blue'
                                                            }`}
                                                    >
                                                        {completedLessons[lesson.id] ? (
                                                            <CheckCircle className="w-4 h-4" />
                                                        ) : (
                                                            <span>
                                                                {course.modules
                                                                    .slice(0, mIdx)
                                                                    .reduce(
                                                                        (acc, mod) => acc + mod.lessons.length,
                                                                        0
                                                                    ) +
                                                                    lIdx +
                                                                    1}
                                                            </span>
                                                        )}
                                                    </div>
                                                    <span className="text-sm truncate max-w-[150px]">
                                                        {lesson.title}
                                                    </span>
                                                </div>
                                                <span className="text-xs text-gray-400">
                                                    {(lesson as any).duration ?? 5} min
                                                </span>
                                            </div>
                                        );
                                    })
                                )}
                            </div>
                        </GlassCard>
                    </div>
                    {/* Main lesson content */}
                    <div className="md:col-span-3 space-y-6">
                        {getCurrentAcademyLesson() && (
                            <LessonView
                                lesson={getCurrentAcademyLesson()!}
                                onComplete={handleLessonComplete}
                                isCompleted={!!completedLessons[currentLesson.id]}
                            />
                        )}
                        <div className="flex justify-between mt-6">
                            <Button
                                variant="outline"
                                onClick={handlePreviousLesson}
                                disabled={
                                    currentModuleIdx === 0 && currentLessonIdx === 0
                                }
                            >
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Previous Lesson
                            </Button>
                            <Button
                                onClick={handleNextLesson}
                                className={`${completedLessons[currentLesson.id]
                                    ? 'bg-sortmy-blue hover:bg-sortmy-blue/90'
                                    : 'bg-gray-700 hover:bg-gray-600'
                                    }`}
                                disabled={!completedLessons[currentLesson.id]}
                            >
                                {currentModule &&
                                    currentLessonIdx < currentModule.lessons.length - 1 ? (
                                    <>
                                        Next Lesson
                                        <ChevronRight className="ml-2 h-4 w-4" />
                                    </>
                                ) : currentModuleIdx < (course.modules.length - 1) ? (
                                    <>
                                        Next Module
                                        <ChevronRight className="ml-2 h-4 w-4" />
                                    </>
                                ) : (
                                    <>
                                        Complete Course
                                        <CheckCircle className="ml-2 h-4 w-4" />
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
};

export default CourseView;

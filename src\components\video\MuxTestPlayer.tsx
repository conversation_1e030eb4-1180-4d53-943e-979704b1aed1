import { useState } from 'react';
import { MuxPlayer } from './MuxPlayer';
import { extractMuxPlaybackId, isMuxUrl } from '@/lib/mux';

export function MuxTestPlayer() {
  const [videoUrl, setVideoUrl] = useState('https://stream.mux.com/PnndXcwARagR00m4gHeLa2AbZRmywzJG73kA9ki00xgOo.m3u8');
  const [isPlaying, setIsPlaying] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const playbackId = isMuxUrl(videoUrl) ? extractMuxPlaybackId(videoUrl) : null;

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h2 className="text-xl font-bold mb-4">Mux Video Test Player</h2>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">Enter Mux Video URL</label>
        <input
          type="text"
          value={videoUrl}
          onChange={(e) => setVideoUrl(e.target.value)}
          className="w-full p-2 border rounded"
          placeholder="https://stream.mux.com/..."
        />
      </div>

      {playbackId && (
        <div className="mb-4 text-sm bg-blue-50 p-2 rounded">
          <p>Detected playback ID: <span className="font-mono">{playbackId}</span></p>
        </div>
      )}

      <div className="mb-4 aspect-video bg-gray-100 rounded overflow-hidden">
        <MuxPlayer
          src={videoUrl}
          autoPlay={false}
          muted={true}
          controls={true}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onError={(err) => {
            setIsError(true);
            setErrorMessage(
              err && typeof err === 'object' && 'message' in err
                ? String((err as { message?: string }).message)
                : 'Unknown error'
            );
            console.error('Mux player error:', err);
          }}
        />
      </div>

      <div className="text-sm">
        <p>Status: {isPlaying ? 'Playing' : 'Paused'}</p>
        {isError && (
          <p className="text-red-500 mt-2">Error: {errorMessage}</p>
        )}
      </div>
    </div>
  );
}
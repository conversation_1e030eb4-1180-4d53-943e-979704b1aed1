import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { GlassCard } from '@/components/ui/glass-card';
import { AISuggestion } from '@/components/ai/AISuggestion';
import { toast } from 'react-hot-toast';
import { db } from '@/lib/firebase';
import { useAuth } from '@/hooks/useAuth';
import type { Course } from '@/types/course';
import { Sparkles } from 'lucide-react';

const Academy: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    try {
      if (!user) {
        toast.error('Please sign in to access the Academy');
        navigate('/login');
        return;
      }

      const coursesCollection = collection(db, 'courses');
      const coursesQuery = query(coursesCollection, orderBy('createdAt', 'desc'));
      const coursesSnapshot = await getDocs(coursesQuery);

      const coursesData = coursesSnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
      })) as Course[];

      setCourses(coursesData);
    } catch (error) {
      console.error('Error loading courses:', error);
      toast.error('Unable to load academy content. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-sortmy-blue border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Loading academy content...</p>
        </div>
      </div>
    );
  }

  // Otherwise show the academy overview
  return (
    <div className="space-y-10 p-1 md:p-8 max-w-7xl mx-auto w-full">
      <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4 mb-2">
        <div>
          <h1 className="text-4xl font-extrabold flex items-center gap-2">
            <Sparkles className="text-sortmy-blue animate-pulse h-8 w-8" />
            Academy
          </h1>
          <p className="text-lg text-gray-400 mt-2 max-w-2xl">
            Learn and master AI tools with our structured, interactive courses. Track your progress, earn XP, and unlock new skills.
          </p>
        </div>
        {user?.role === 'admin' && (
          <button
            onClick={() => navigate('/dashboard/admin/course-manager')}
            className="bg-sortmy-blue text-white px-6 py-3 rounded-lg shadow-lg hover:bg-sortmy-blue/90 font-semibold text-lg transition-all"
          >
            + Add Course
          </button>
        )}
      </div>

      <GlassCard variant="bordered" className="border-sortmy-blue/20 bg-sortmy-darker/80 shadow-2xl">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 p-8">
          {courses.map(course => (
            <div
              key={course.id}
              className="cursor-pointer group rounded-xl overflow-hidden bg-sortmy-dark/80 border border-sortmy-blue/10 hover:border-sortmy-blue/40 shadow-lg hover:shadow-2xl transition-all duration-200 flex flex-col"
              onClick={() => navigate(`/academy/course/${course.id}`)}
            >
              <div className="aspect-w-16 aspect-h-9 bg-sortmy-blue/10 relative">
                <img
                  src={course.thumbnail}
                  alt={course.title}
                  className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300 rounded-t-xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-sortmy-dark/80 to-transparent" />
              </div>
              <div className="p-5 flex-1 flex flex-col justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-1 text-white group-hover:text-sortmy-blue transition-colors">
                    {course.title}
                  </h2>
                  <p className="text-gray-400 mb-4 line-clamp-2 min-h-[48px]">
                    {course.description}
                  </p>
                </div>
                <div className="flex justify-between items-center mt-auto pt-2 border-t border-sortmy-blue/10">
                  <div className="text-sm text-sortmy-blue font-semibold flex items-center gap-1">
                    <Sparkles className="h-4 w-4" />
                    {course.modules?.reduce((total, module) => total + module.lessons.length, 0) || 0} lessons
                  </div>
                  <div className="text-sm text-gray-400">
                    {course.estimatedDuration} min
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </GlassCard>

      {/* AI Suggestion */}
      <div className="mb-8 flex justify-center">
        <AISuggestion
          suggestion="Start with foundation courses to build a strong understanding of AI tools. Then explore specialized courses based on your interests."
          actionText="Start Learning"
          onAction={() => {
            const firstCourse = courses[0];
            if (firstCourse) {
              navigate(`/academy/course/${firstCourse.id}`);
            }
          }}
        />
      </div>
    </div>
  );
};

export default Academy;

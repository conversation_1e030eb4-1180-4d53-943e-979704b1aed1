import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CourseService } from '@/lib/services/courseService';
import type { Course } from '@/types/course';
import { Button } from '@/components/ui/button';
import { Plus, Edit, Trash } from 'lucide-react';

const CourseManager = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    setLoading(true);
    try {
      const data = await CourseService.listCourses();
      setCourses(data);
    } catch (error) {
      // TODO: Show error toast
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this course?')) return;
    try {
      await CourseService.deleteCourse(id);
      fetchCourses();
    } catch (error) {
      // TODO: Show error toast
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading courses...</div>;
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Manage Courses</h1>
        <Button onClick={() => navigate('/admin/courses/new')}>
          <Plus className="mr-2 h-4 w-4" /> New Course
        </Button>
      </div>
      <div className="bg-sortmy-darker rounded-lg shadow p-6">
        {courses.length === 0 ? (
          <div className="text-center text-muted-foreground">No courses found. Click "New Course" to create one.</div>
        ) : (
          <table className="min-w-full divide-y divide-gray-700">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left">Title</th>
                <th className="px-4 py-2 text-left">Description</th>
                <th className="px-4 py-2 text-left">Author</th>
                <th className="px-4 py-2 text-left">Created</th>
                <th className="px-4 py-2 text-right">Actions</th>
              </tr>
            </thead>
            <tbody>
              {courses.map((course) => (
                <tr key={course.id} className="border-b border-gray-800 hover:bg-sortmy-blue/5">
                  <td className="px-4 py-2 font-medium">{course.title}</td>
                  <td className="px-4 py-2 max-w-xs truncate">{course.description}</td>
                  <td className="px-4 py-2">{course.authorName}</td>
                  <td className="px-4 py-2">{new Date(course.createdAt).toLocaleDateString()}</td>
                  <td className="px-4 py-2 text-right space-x-2">
                    <Button size="icon" variant="outline" onClick={() => navigate(`/admin/courses/edit/${course.id}`)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button size="icon" variant="destructive" onClick={() => handleDelete(course.id)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
};

export default CourseManager;
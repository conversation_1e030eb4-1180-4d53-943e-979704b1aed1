import { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import { trackInteraction } from '@/services/analyticsService';
import { MissionTracker } from '@/services/missionService';

interface LikeButtonProps {
  initialLiked: boolean;
  initialLikeCount: number;
  postId: string;
  user: { id: string; uid?: string; username?: string; xp?: number; level?: number; streak_days?: number; email?: string; badges?: string[]; following?: string[]; followers_count?: number; following_count?: number } | null;
  likePost: (postId: string, userId: string, liked: boolean, postType?: string) => Promise<void>;
  postType?: string; // 'portfolio' | 'video' | etc.
  onLikeChange?: (liked: boolean, newCount: number) => void;
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
}

const LikeButton: React.FC<LikeButtonProps> = ({
  initialLiked,
  initialLikeCount,
  postId,
  user,
  likePost,
  postType = 'portfolio',
  onLikeChange,
  size = 'md',
  showCount = true,
}) => {
  const [liked, setLiked] = useState(initialLiked);
  const [likeCount, setLikeCount] = useState(initialLikeCount);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLiked(initialLiked);
    setLikeCount(initialLikeCount);
  }, [initialLiked, initialLikeCount]);

  const handleLikeClick = async () => {
    console.log('🎯 LikeButton: handleLikeClick called, user:', user);
    if (!user) {
      console.log('❌ LikeButton: No user found');
      return;
    }
    const userId = user.uid || user.id;
    if (!userId) {
      console.log('❌ LikeButton: No userId found');
      return;
    }
    console.log('🎯 LikeButton: userId:', userId, 'liked:', liked);

    setLoading(true);
    setError(null);
    const prevLiked = liked;
    const prevCount = likeCount;

    // Optimistic update
    setLiked(!liked);
    setLikeCount(prev => prev + (liked ? -1 : 1));
    onLikeChange?.(!liked, likeCount + (liked ? -1 : 1));

    try {
      await likePost(postId, userId, !liked, postType);

      // Track mission progress for liking posts (only when liking, not unliking)
      if (!liked && user) {
        try {
          const trackingUserId = user.uid || user.id;
          console.log('🎯 LikeButton: Tracking like post mission for user:', trackingUserId, 'liked state:', liked);
          await MissionTracker.likePost(trackingUserId);
          console.log('🎯 LikeButton: Like post mission tracked successfully');
        } catch (missionError) {
          console.warn('❌ LikeButton: Error tracking like post mission:', missionError);
          // Don't fail the like if mission tracking fails
        }
      } else {
        console.log('🎯 LikeButton: Not tracking mission - liked:', liked, 'user:', !!user);
      }

      // Track like interaction in analytics
      if (!liked) {  // Only track when adding a like, not removing
        try {
          // Convert user data to match User type - ensure no undefined values
          const userAnalyticsData = {
            id: user.id || userId,
            uid: userId,
            email: user.email || '<EMAIL>',
            username: user.username || 'Anonymous User',
            xp: typeof user.xp === 'number' ? user.xp : 0,
            level: typeof user.level === 'number' ? user.level : 1,
            streak_days: typeof user.streak_days === 'number' ? user.streak_days : 0,
            last_login: new Date().toISOString(),
            badges: Array.isArray(user.badges) ? user.badges : [],
            following: Array.isArray(user.following) ? user.following : [],
            followers_count: typeof user.followers_count === 'number' ? user.followers_count : 0,
            following_count: typeof user.following_count === 'number' ? user.following_count : 0,
            avatar_url: user.avatar_url || ''
          };

          await trackInteraction(
            postId,
            postType === 'portfolio' ? 'portfolio' : 'profile',
            'like',
            userAnalyticsData
          );
        } catch (analyticsError) {
          console.warn('❌ Analytics tracking failed:', analyticsError);
          // Don't fail the like if analytics fails
        }
      }
    } catch (error: any) {
      // Rollback on error
      setLiked(prevLiked);
      setLikeCount(prevCount);
      onLikeChange?.(prevLiked, prevCount);
      setError(error?.message || 'Error toggling like');
      console.error('Error toggling like:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <button
        type="button"
        onClick={handleLikeClick}
        disabled={loading}
        style={{
          cursor: loading ? 'not-allowed' : 'pointer',
          background: 'none',
          border: 'none',
          outline: 'none',
          padding: '4px 8px',
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          gap: 4,
          fontWeight: 500,
          fontSize: size === 'sm' ? 14 : size === 'lg' ? 20 : 16,
          color: liked ? '#ef4444' : '#94a3b8',
          transition: 'color 0.2s',
        }}
        aria-label={liked ? 'Unlike' : 'Like'}
      >
        <Heart
          fill={liked ? '#ef4444' : 'none'}
          stroke={liked ? '#ef4444' : '#94a3b8'}
          width={size === 'sm' ? 18 : size === 'lg' ? 28 : 22}
          height={size === 'sm' ? 18 : size === 'lg' ? 28 : 22}
          style={{ marginRight: 6, transition: 'fill 0.2s, stroke 0.2s' }}
        />
        {showCount && <span>{likeCount}</span>}
      </button>
      {error && (
        <div style={{ color: 'red', fontSize: 12, marginTop: 4 }}>
          {error}
        </div>
      )}
    </>
  );
};

export default LikeButton;

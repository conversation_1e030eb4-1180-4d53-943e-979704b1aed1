import { useEffect, useState } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

/**
 * React hook to fetch a user's username and avatar_url from Firestore given a userId (uid or authorId).
 * Caches the result in state for the session.
 * Returns { username, avatarUrl, loading, error }
 */
export function useUsernameFromId(userId?: string) {
    const [username, setUsername] = useState<string | null>(null);
    const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!userId) {
            setUsername(null);
            setAvatarUrl(null);
            return;
        }
        setLoading(true);
        setError(null);
        getDoc(doc(db, 'users', userId))
            .then((snap) => {
                if (snap.exists()) {
                    setUsername(snap.data().username || null);
                    setAvatarUrl(snap.data().avatar_url || null);
                } else {
                    setUsername(null);
                    setAvatarUrl(null);
                }
            })
            .catch(() => {
                setError('Failed to fetch username');
                setUsername(null);
                setAvatarUrl(null);
            })
            .finally(() => setLoading(false));
    }, [userId]);

    return { username, avatarUrl, loading, error };
}

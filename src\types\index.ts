export interface User {
  id: string;
  uid: string;
  email: string | undefined;
  username?: string;
  is_premium?: boolean;
  claude_enabled?: boolean;
  created_at?: string;
  updated_at?: string;
  avatar_url?: string;
  bio?: string;
  profession?: string;
  website?: string;
  role?: 'admin' | 'intern' | 'basic';
  followers_count?: number;
  following_count?: number;
  following?: string[];
  // Gamification related properties
  xp: number;
  level: number;
  streak_days: number;
  last_login: string;
  badges: string[];
  ai_knowledge?: {
    overall: number;
    categories: Record<string, number>;
  };
  onboarded?: boolean; // New field for onboarding status
}

export interface PortfolioItem {
  id: string;
  userId: string;
  title: string;
  description: string;
  media_url: string;
  media_urls?: string[];
  media_type: 'image' | 'video' | 'audio';
  content_type: 'post' | 'reel' | 'both'; // Type of content (post, reel, or both)
  thumbnail_url?: string; // URL for video thumbnail
  tools_used: string[];
  categories: string[];
  likes: number;
  comments?: number;
  views: number;
  project_url?: string;
  is_public: boolean;
  status: 'published' | 'draft' | 'archived' | 'deleted';
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  archived_at?: string;
  is_featured?: boolean; // <-- Add this line to fix the error
  onClick?: () => void; // For handling click events in components
  // Author information (for display purposes)
  authorId?: string;
  authorName?: string;
  authorAvatar?: string;
}

export interface Tool {
  id: string;
  name: string;
  description: string;
  logo_url?: string;
  website_url?: string;
  website?: string;
  tags: string[];
  category?: string;
  created_at: string;
  updated_at?: string;
  user_id?: string;
  is_favorite?: boolean;
  rating?: number;
  price_tier?: string;
  notes?: string;
  source?: 'tools_collection' | 'user_tooltracker' | 'library';
}

export type ObjectFit = 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
export type ImageSizeType = 'contain' | 'cover' | '85%' | '75%' | '50%';
export type ImagePositionType =
  | 'center'
  | 'top'
  | 'bottom'
  | 'left'
  | 'right'
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right';

export interface ImageSettings {
  size: 'contain' | 'cover' | '85%' | '75%' | '50%';
  position: ImagePositionType;
  padding: number;
  scale: number;
  rotate: number;
  crop?: {
    unit: '%';
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface AITool {
  id: string;
  name: string;
  useCase: string;
  description: string;
  tags: string[] | string;
  pricing: 'Free' | 'Freemium' | 'Paid' | 'Subscription';
  excelsAt?: string;
  website?: string;
  logoUrl?: string;
  logoLink?: string;
  websiteLink?: string;
  videoUrl?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  category?: string;
  freeCredits?: string;
  imageSettings?: ImageSettings;
}

//# sourceMappingURL=types.js.map
export * from './gamification';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { CourseService } from '@/lib/services/courseService';
import { AppError } from '@/lib/errors';
import type { Course, UserProgress } from '@/types/course';

export function useCourse(courseId: string) {
    const { user } = useAuth();
    const [course, setCourse] = useState<Course | null>(null);
    const [progress, setProgress] = useState<UserProgress | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const loadCourseAndProgress = useCallback(async () => {
        if (!courseId) {
            setError('Course ID is required');
            return;
        }

        if (!user?.uid) {
            setError('User must be logged in');
            return;
        }

        try {
            setLoading(true);
            setError(null);

            // Load course
            const courseData = await CourseService.getCourse(courseId);
            if (!courseData) {
                throw new AppError('Course not found');
            }
            setCourse(courseData);

            // Load user progress
            const progressData = await CourseService.getUserProgress(
                courseId,
                user.uid
            );

            if (!progressData) {
                // Initialize new progress
                const newProgress: UserProgress = {
                    userId: user.uid,
                    courseId,
                    status: 'not-started', // Fix the status value
                    completedLessons: {},
                    completedModules: {},
                    quizScores: {},
                    earnedXp: 0,
                    quizAttempts: [],
                    currentLesson: {  // Provide required currentLesson object
                        moduleId: '',
                        lessonId: ''
                    },
                    lastAccessedAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }; await CourseService.initUserProgress(user.uid, courseId);
                setProgress(newProgress);
            } else {
                setProgress(progressData);
            }
        } catch (err) {
            console.error('Error loading course:', err);
            setError(err instanceof AppError ? err.message : 'Failed to load course');
        } finally {
            setLoading(false);
        }
    }, [courseId, user?.uid]);

    useEffect(() => {
        if (courseId && user?.uid) {
            loadCourseAndProgress();
        }
    }, [courseId, user?.uid, loadCourseAndProgress]);

    const updateProgress = useCallback(async (updates: Partial<UserProgress>) => {
        if (!user?.uid || !courseId) {
            throw new AppError('User must be logged in and courseId is required');
        }

        if (!progress) {
            throw new AppError('Progress must be initialized before updating');
        }

        try {
            const updatedProgress = await CourseService.updateUserProgress(
                courseId,
                user.uid,
                { ...progress, ...updates }
            );
            setProgress(updatedProgress);
            return updatedProgress;
        } catch (err) {
            console.error('Error updating progress:', err);
            throw err instanceof AppError ? err : new AppError('Failed to update progress');
        }
    }, [courseId, user?.uid, progress]);

    const checkModuleCompletion = useCallback(async (moduleId: string) => {
        if (!progress || !course) return false;

        const module = course.modules.find(m => m.id === moduleId);
        if (!module) return false;

        const allLessonsCompleted = module.lessons.every(
            lesson => progress.completedLessons[lesson.id]
        );

        if (allLessonsCompleted && !progress.completedModules[moduleId]) {
            await updateProgress({
                completedModules: {
                    ...progress.completedModules,
                    [moduleId]: true
                }
            });
        }

        return allLessonsCompleted;
    }, [course, progress, updateProgress]);

    return {
        course,
        progress,
        loading,
        error,
        updateProgress,
        checkModuleCompletion
    };
}

export function useCourseList() {
    const [courses, setCourses] = useState<Course[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        async function loadCourses() {
            try {
                setLoading(true);
                setError(null);
                const coursesList = await CourseService.listCourses();
                setCourses(coursesList);
            } catch (err) {
                console.error('Error loading courses:', err);
                setError('Failed to load courses');
            } finally {
                setLoading(false);
            }
        }

        loadCourses();
    }, []);

    return {
        courses,
        loading,
        error,
    };
}

export function useUserCourses() {
    const { user } = useAuth();
    const [courses, setCourses] = useState<
        { course: Course; progress: UserProgress }[]
    >([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!user?.uid) return; async function loadUserCourses() {
            // TypeScript doesn't know that user?.uid check persists in async context
            if (!user) return;

            try {
                setLoading(true);
                setError(null);
                const userCourses = await CourseService.listUserCourses(user.uid);
                setCourses(userCourses);
            } catch (err) {
                console.error('Error loading user courses:', err);
                setError('Failed to load your courses');
            } finally {
                setLoading(false);
            }
        }

        loadUserCourses();
    }, [user?.uid]);

    return {
        courses,
        loading,
        error,
    };
}

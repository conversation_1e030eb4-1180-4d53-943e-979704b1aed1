# Utilities and Services Documentation

## Firebase Services

### `src/lib/firebase.ts`
Firebase initialization and configuration:
```typescript
// Firebase app configuration
export const app = initializeApp(firebaseConfig);
export const db = getFirestore(app);
export const auth = getAuth(app);
export const storage = getStorage(app);
```

## Utility Functions

### `src/utils/networkUtils.ts`
Network status management:
```typescript
export const networkManager = {
  getNetworkStatus: () => boolean,
  handleNetworkChange: () => void,
  setupNetworkListeners: () => void
}
```

### `src/utils/dedupeById.ts`
Data deduplication utilities:
```typescript
export function dedupeById<T extends { id: string }>(items: T[]): T[] {
  // Removes duplicate items based on ID
}
```

## Service Layer

### `src/services/scheduledCleanupService.ts`
Automated cleanup management:
```typescript
export const setupAutomaticCleanup = () => {
  // Initializes cleanup routines
  // Schedules maintenance tasks
  // Manages storage optimization
}

export const setupBrowserCleanup = () => {
  // Handles browser storage cleanup
  // Manages cache optimization
  // Handles temporary files
}
```

### `src/services/portfolioStorageService.ts`
Portfolio storage operations:
```typescript
export const portfolioStorageService = {
  uploadMedia: async (file: File) => string,
  deleteMedia: async (path: string) => void,
  optimizeImage: async (file: File) => File,
  handleVideoUpload: async (file: File) => string,
  getUserDrafts: async (userId: string) => PortfolioDraft[],
  saveDraft: async (userId: string, draft: PortfolioDraft) => void,
  deleteDraft: async (userId: string, draftId: string) => void
}
```

### `src/services/likesService.ts`
Engagement tracking service:
```typescript
export const likesService = {
  getLikesAndViews: async (id: string, type: string) => LikeData,
  toggleLike: async (id: string, userId: string, type: string) => void,
  incrementViews: async (id: string, type: string) => number,
  subscribeLikesAndViews: (id: string, type: string, callback: Function) => Unsubscribe
}
```

### `src/services/creatorService.ts`
Creator management service:
```typescript
export const creatorService = {
  fetchCreatorsWithPortfolio: async () => Creator[],
  searchCreators: async (query: string) => Creator[],
  getFollowersWithDetails: async (userId: string) => Follower[],
  getFollowingWithDetails: async (userId: string) => Following[]
}
```

### `src/services/interactionService.ts`
User interaction service:
```typescript
export const interactionService = {
  getUserLikedPosts: async (userId: string) => Post[],
  getUserComments: async (userId: string) => Comment[],
  addComment: async (postId: string, userId: string, content: string) => void,
  deleteComment: async (commentId: string) => void
}
```

## Form Validation

### `src/lib/validation/portfolioValidation.ts`
Portfolio form validation schemas:
```typescript
export const portfolioFormSchema = z.object({
  title: z.string().min(2),
  description: z.string().min(10),
  tools: z.array(z.string()).min(1),
  media: z.array(z.object({
    type: z.enum(['image', 'video']),
    url: z.string().url()
  }))
})
```

### `src/lib/validation/toolValidation.ts`
Tool form validation schemas:
```typescript
export const toolFormSchema = z.object({
  name: z.string().min(2),
  category: z.string(),
  description: z.string().min(10),
  url: z.string().url(),
  integrations: z.array(z.string())
})
```

## Error Handling

### `src/lib/errors/index.ts`
Application error classes:
```typescript
export class AppError extends Error {
  constructor(message: string, public code: string) {
    super(message);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR');
  }
}

export class NetworkError extends AppError {
  constructor(message: string) {
    super(message, 'NETWORK_ERROR');
  }
}
```

## Capacitor Integration

### `src/lib/capacitor.ts`
Mobile integration utilities:
```typescript
export const initializeCapacitor = async () => {
  // Sets up mobile platform features
  // Initializes device capabilities
  // Configures platform-specific features
}

export const setupPushNotifications = async () => {
  // Configures push notification handling
  // Sets up notification listeners
  // Manages notification permissions
}
```

## Type Definitions

### `src/types/index.ts`
Core type definitions:
```typescript
export interface User {
  id: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: UserRole;
  createdAt: Timestamp;
}

export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  media: Media[];
  tools: string[];
  userId: string;
  createdAt: Timestamp;
}

export interface Tool {
  id: string;
  name: string;
  category: string;
  description: string;
  url: string;
  integrations: string[];
  usage: UsageStats;
}
```

### `src/types/academy.ts`
Academy-related types:
```typescript
export interface Course {
  id: string;
  title: string;
  description: string;
  modules: Module[];
  createdBy: string;
  published: boolean;
}

export interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
  order: number;
}
```

## Constants and Configuration

### `src/config/index.ts`
Application configuration:
```typescript
export const CONFIG = {
  firebase: {
    // Firebase configuration
  },
  storage: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'video/mp4']
  },
  api: {
    baseUrl: process.env.VITE_API_URL,
    timeout: 30000
  }
}
```

import { doc, getDoc, setDoc, updateDoc, deleteDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { ActivityType, XP_REWARDS } from '@/utils/xpSystem';

export interface Mission {
  id: string;
  title: string;
  description: string;
  xpReward: number;
  activityType: ActivityType;
  category: 'daily' | 'weekly' | 'achievement' | 'milestone';
  progress: number;
  maxProgress: number;
  isCompleted: boolean;
  isClaimed: boolean;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  milestoneInfo?: {
    currentTarget: number;
    currentXP: number;
    description: string;
    nextTarget?: number;
    nextXP?: number;
    detailedMilestones: Array<{
      target: number;
      xp: number;
      tier: string;
      description: string;
    }>;
  };
}

export interface UserMissionProgress {
  userId: string;
  missionId: string;
  progress: number;
  isCompleted: boolean;
  isClaimed: boolean;
  completedAt?: string;
  claimedAt?: string;
  updatedAt: string;
}

export class MissionService {
  private static dailyLoginInProgress = new Set<string>(); // Track users currently processing daily login

  // Test if rules deployment is working
  static async testRulesDeployment(): Promise<boolean> {
    try {
      console.log('MissionService: Testing rules deployment with open test collection...');
      const testRef = doc(db, 'test_permissions', 'deployment_test');
      await setDoc(testRef, {
        timestamp: new Date().toISOString(),
        test: 'rules_deployment'
      });
      await deleteDoc(testRef);
      console.log('MissionService: Rules deployment test PASSED');
      return true;
    } catch (error: any) {
      console.error('MissionService: Rules deployment test FAILED:', error.message);
      return false;
    }
  }

  // Test Firestore permissions for mission progress
  static async testFirestorePermissions(userId: string): Promise<boolean> {
    try {
      const testRef = doc(db, 'user_mission_progress', `${userId}_test`);
      await setDoc(testRef, {
        userId,
        missionId: 'test',
        progress: 0,
        isCompleted: false,
        isClaimed: false,
        completedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      // Clean up test document
      await deleteDoc(testRef);
      console.log('MissionService: Firestore permissions test passed');
      return true;
    } catch (error: any) {
      console.warn('MissionService: Firestore permissions test failed:', error.message);
      return false;
    }
  }

  // Get all available missions for a user - CRYSTAL CLEAR SIMPLE VERSION
  static async getUserMissions(userId: string): Promise<Mission[]> {
    try {
      console.log('🎯 Getting missions for user:', userId);

      // Generate base missions
      const missions = this.generateAllMissions();
      console.log('🎯 Generated missions:', missions.length);

      // Get today's date for daily mission resets
      const today = new Date().toISOString().split('T')[0];

      // Update each mission with current progress from localStorage
      const updatedMissions = missions.map(mission => {
        const progressKey = `mission_${userId}_${mission.id}`;
        const dateKey = `mission_date_${userId}_${mission.id}`;
        const completedKey = `mission_completed_${userId}_${mission.id}`;
        const claimedKey = `mission_claimed_${userId}_${mission.id}`;

        // Get stored values
        let progress = parseInt(localStorage.getItem(progressKey) || '0');
        let lastDate = localStorage.getItem(dateKey) || '';
        let isCompleted = localStorage.getItem(completedKey) === 'true';
        let isClaimed = localStorage.getItem(claimedKey) === 'true';

        // Reset daily missions if new day
        if (mission.category === 'daily' && lastDate !== today) {
          progress = 0;
          isCompleted = false;
          isClaimed = false;
          localStorage.setItem(dateKey, today);
          localStorage.removeItem(completedKey);
          localStorage.removeItem(claimedKey);
          localStorage.setItem(progressKey, '0');
        }

        // Update completion status
        if (progress >= mission.maxProgress && !isCompleted) {
          isCompleted = true;
          localStorage.setItem(completedKey, 'true');
        }

        return {
          ...mission,
          progress,
          isCompleted,
          isClaimed
        };
      });

      console.log('🎯 Updated missions with progress:', updatedMissions.map(m => `${m.id}: ${m.progress}/${m.maxProgress} ${m.isCompleted ? '✅' : '⏳'}`));

      return updatedMissions;
    } catch (error) {
      console.error('❌ Error getting user missions:', error);
      return this.generateAllMissions();
    }
  }

  // Update mission progress
  static async updateMissionProgress(
    userId: string,
    missionId: string,
    progressIncrement: number = 1
  ): Promise<void> {
    try {
      const mission = this.getMissionById(missionId);
      if (!mission) return;

      const progressRef = doc(db, 'user_mission_progress', `${userId}_${missionId}`);

      try {
        const progressDoc = await getDoc(progressRef);

        if (progressDoc.exists()) {
          const currentData = progressDoc.data() as UserMissionProgress;
          const newProgress = Math.min(currentData.progress + progressIncrement, mission.maxProgress);
          const isCompleted = newProgress >= mission.maxProgress;

          console.log(`Updating existing mission progress: ${missionId}, ${currentData.progress} -> ${newProgress}/${mission.maxProgress}`);

          await updateDoc(progressRef, {
            progress: newProgress,
            isCompleted,
            completedAt: isCompleted && !currentData.isCompleted ? new Date().toISOString() : currentData.completedAt,
            updatedAt: new Date().toISOString()
          });
        } else {
          const newProgress = Math.min(progressIncrement, mission.maxProgress);
          const isCompleted = newProgress >= mission.maxProgress;

          console.log(`Creating new mission progress: ${missionId}, ${newProgress}/${mission.maxProgress}`);

          await setDoc(progressRef, {
            userId,
            missionId,
            progress: newProgress,
            isCompleted,
            isClaimed: false,
            completedAt: isCompleted ? new Date().toISOString() : undefined,
            updatedAt: new Date().toISOString()
          });
        }
      } catch (firestoreError: any) {
        console.warn('Firestore permission error, using local tracking:', firestoreError.message);
        // Store in localStorage as fallback
        const localKey = `mission_${userId}_${missionId}`;
        const existingData = localStorage.getItem(localKey);
        let currentProgress = 0;

        if (existingData) {
          const parsed = JSON.parse(existingData);
          currentProgress = parsed.progress || 0;
        }

        const newProgress = Math.min(currentProgress + progressIncrement, mission.maxProgress);
        const isCompleted = newProgress >= mission.maxProgress;

        const localData = {
          userId,
          missionId,
          progress: newProgress,
          isCompleted,
          isClaimed: false,
          completedAt: isCompleted ? new Date().toISOString() : undefined,
          updatedAt: new Date().toISOString()
        };
        localStorage.setItem(localKey, JSON.stringify(localData));
      }
    } catch (error) {
      console.error('Error updating mission progress:', error);
    }
  }

  // Mark mission as claimed - SIMPLIFIED AND FIXED
  static async claimMission(userId: string, missionId: string): Promise<boolean> {
    try {
      console.log('🎯 Claiming mission:', missionId, 'for user:', userId);

      // Check if mission is completed using our simple localStorage approach
      const completedKey = `mission_completed_${userId}_${missionId}`;
      const claimedKey = `mission_claimed_${userId}_${missionId}`;

      const isCompleted = localStorage.getItem(completedKey) === 'true';
      const isClaimed = localStorage.getItem(claimedKey) === 'true';

      if (!isCompleted) {
        throw new Error('Mission not completed yet');
      }

      if (isClaimed) {
        throw new Error('Mission already claimed');
      }

      // Mark as claimed
      localStorage.setItem(claimedKey, 'true');

      console.log('🎉 Mission claimed successfully:', missionId);
      return true;
    } catch (error) {
      console.error('❌ Error claiming mission:', error);
      return false;
    }
  }





  // Calculate total XP for milestone missions (sum of all milestone rewards)
  private static calculateMilestoneXP(activityType: ActivityType, milestones: number[]): number {
    const baseXP = XP_REWARDS[activityType] || 5;
    let totalXP = 0;

    milestones.forEach((milestone, index) => {
      const tier = index + 1;
      const tierXP = baseXP * milestone + (5 * tier); // Base XP * milestone + 5 per tier
      totalXP += tierXP;
    });

    return totalXP;
  }

  // Generate all missions (daily and progressive achievements)
  static generateAllMissions(): Mission[] {
    // Use local timezone for consistent date handling
    const today = new Date();
    const localToday = new Date(today.getTime() - (today.getTimezoneOffset() * 60000));

    // Set expiry to end of today (not tomorrow)
    const endOfToday = new Date(localToday);
    endOfToday.setHours(23, 59, 59, 999); // End of today in local time

    console.log('Generating progressive missions - Today:', localToday.toISOString().split('T')[0], 'Expires:', endOfToday.toISOString());

    const missions: Mission[] = [
      // Daily Login Mission
      {
        id: 'daily_login',
        title: 'Daily Login',
        description: 'Log in to earn your daily XP bonus',
        xpReward: XP_REWARDS.DAILY_LOGIN,
        activityType: 'DAILY_LOGIN',
        category: 'daily',
        progress: 0,
        maxProgress: 1,
        isCompleted: false,
        isClaimed: false,
        expiresAt: endOfToday.toISOString(),
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      },



      // Daily Like Posts Mission (milestone-style, resets daily)
      {
        id: 'like_posts_daily',
        title: 'Post Appreciator',
        description: 'Like 3 posts', // Show only current milestone
        xpReward: 11, // XP for current milestone (2×3 + 5)
        activityType: 'LIKE_POST',
        category: 'daily',
        progress: 0,
        maxProgress: 3, // Current milestone target
        isCompleted: false,
        isClaimed: false,
        expiresAt: endOfToday.toISOString(),
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      },

      // Daily Time Keeper Mission (milestone-style, resets daily)
      {
        id: 'time_keeper_daily',
        title: 'Time Keeper',
        description: 'Stay online for 10 minutes', // Show only current milestone
        xpReward: 25, // XP for current milestone (2×10 + 5)
        activityType: 'STAY_ONLINE',
        category: 'daily',
        progress: 0,
        maxProgress: 10, // Current milestone target
        isCompleted: false,
        isClaimed: false,
        expiresAt: endOfToday.toISOString(),
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      },

      // Daily Create Posts Mission (resets daily)
      {
        id: 'create_posts_daily',
        title: 'Content Creator',
        description: 'Create posts: 1 post daily',
        xpReward: XP_REWARDS.CREATE_POST,
        activityType: 'CREATE_POST',
        category: 'daily',
        progress: 0,
        maxProgress: 1, // Only 1 post per day gets reward
        isCompleted: false,
        isClaimed: false,
        expiresAt: endOfToday.toISOString(),
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      },

      // Milestone: Tool Collection (lifetime progress)
      {
        id: 'tool_collector_milestone',
        title: 'Tool Collector',
        description: 'Collect 3 tools', // Show only current milestone
        xpReward: 20, // XP for current milestone (5×3 + 5)
        activityType: 'ADD_TOOL_TO_LIBRARY',
        category: 'milestone',
        progress: 0,
        maxProgress: 3, // Current milestone target
        isCompleted: false,
        isClaimed: false,
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      },

      // Milestone: Toolkit Creation (lifetime progress)
      {
        id: 'toolkit_creator_milestone',
        title: 'Toolkit Creator',
        description: 'Create 1 toolkit', // Show only current milestone
        xpReward: 15, // XP for current milestone (10×1 + 5)
        activityType: 'CREATE_TOOLKIT',
        category: 'milestone',
        progress: 0,
        maxProgress: 1, // Current milestone target
        isCompleted: false,
        isClaimed: false,
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      },

      // Milestone: Academy Courses (lifetime progress)
      {
        id: 'academy_scholar_milestone',
        title: 'Scholar',
        description: 'Complete 1 course', // Show only current milestone
        xpReward: 30, // XP for current milestone (25×1 + 5)
        activityType: 'COMPLETE_LESSON',
        category: 'milestone',
        progress: 0,
        maxProgress: 1, // Current milestone target
        isCompleted: false,
        isClaimed: false,
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      },



      // Keep the original portfolio milestone
      {
        id: 'create_portfolio_milestone',
        title: 'Portfolio Creator',
        description: 'Create your first portfolio item',
        xpReward: XP_REWARDS.CREATE_FIRST_PORTFOLIO,
        activityType: 'CREATE_FIRST_PORTFOLIO',
        category: 'milestone',
        progress: 0,
        maxProgress: 1,
        isCompleted: false,
        isClaimed: false,
        createdAt: today.toISOString(),
        updatedAt: today.toISOString()
      }
    ];

    console.log('🎯 MISSION GENERATION DEBUG:');
    console.log('Total missions generated:', missions.length);
    missions.forEach(mission => {
      console.log(`- ${mission.id}: ${mission.title} (${mission.category}) - ${mission.activityType}`);
    });

    return missions;
  }

  // Get mission by ID
  static getMissionById(missionId: string): Mission | null {
    const missions = this.generateAllMissions();
    return missions.find(m => m.id === missionId) || null;
  }



  // Track activity for mission progress
  static async trackActivity(userId: string, activityType: ActivityType): Promise<void> {
    try {
      console.log(`🎯 TRACKING: ${activityType} for user: ${userId}`);

      // Use simple localStorage approach for immediate feedback
      const today = new Date().toISOString().split('T')[0];

      // Get all missions that match this activity type
      const allMissions = this.generateAllMissions();
      console.log(`🎯 All missions:`, allMissions.map(m => ({ id: m.id, activityType: m.activityType })));

      const relevantMissions = allMissions.filter(m => m.activityType === activityType);

      console.log(`🎯 Found ${relevantMissions.length} missions for ${activityType}:`, relevantMissions.map(m => m.id));

      if (relevantMissions.length === 0) {
        console.log(`❌ No missions found for activity type: ${activityType}`);
        console.log(`Available activity types:`, [...new Set(allMissions.map(m => m.activityType))]);
        return;
      }

      for (const mission of relevantMissions) {
        console.log(`🎯 Processing mission: ${mission.id} (${mission.title}) - Category: ${mission.category}`);

        const progressKey = `mission_${userId}_${mission.id}`;
        const dateKey = `mission_date_${userId}_${mission.id}`;
        const completedKey = `mission_completed_${userId}_${mission.id}`;

        // Get current progress
        let currentProgress = parseInt(localStorage.getItem(progressKey) || '0');
        let lastDate = localStorage.getItem(dateKey) || '';

        console.log(`🎯 Current progress for ${mission.id}: ${currentProgress}/${mission.maxProgress}, Last date: ${lastDate}, Today: ${today}`);

        // Reset daily missions if it's a new day
        if (mission.category === 'daily' && lastDate !== today) {
          console.log(`🎯 Resetting daily mission ${mission.id} for new day`);
          currentProgress = 0;
          localStorage.setItem(dateKey, today);
          localStorage.removeItem(completedKey); // Reset completion status
        }

        // Don't increment if already completed (for non-repeatable missions)
        const isCompleted = localStorage.getItem(completedKey) === 'true';
        if (isCompleted && mission.category === 'milestone') {
          console.log(`🎯 Mission ${mission.id} already completed (milestone)`);
          continue;
        }

        // Increment progress
        currentProgress += 1;
        localStorage.setItem(progressKey, currentProgress.toString());

        console.log(`🎯 Updated ${mission.id}: ${currentProgress}/${mission.maxProgress}`);

        // Mark as completed if target reached
        if (currentProgress >= mission.maxProgress) {
          localStorage.setItem(completedKey, 'true');
          console.log(`🎉 Mission completed: ${mission.title}`);
        }
      }
    } catch (error) {
      console.error('❌ Error tracking activity:', error);
    }
  }

  // Handle milestone mission tracking (lifetime cumulative progress)
  private static async handleMilestoneMission(userId: string, mission: Mission, activityType: ActivityType): Promise<void> {
    try {
      // Skip if already completed
      if (mission.isCompleted) {
        console.log(`Milestone mission ${mission.id} already completed, skipping`);
        return;
      }

      // Get total count for this activity type across all user data
      const totalCount = await this.getTotalActivityCount(userId, activityType);
      console.log(`Total ${activityType} count for user ${userId}: ${totalCount}`);

      // Define milestones for each mission type
      const milestones = this.getMilestonesForMission(mission.id);

      // Find the highest milestone reached
      let newProgress = 0;
      let milestonesCompleted = 0;

      for (const milestone of milestones) {
        if (totalCount >= milestone) {
          newProgress = milestone;
          milestonesCompleted++;
        } else {
          break;
        }
      }

      const isCompleted = newProgress >= mission.maxProgress;

      if (newProgress > mission.progress) {
        console.log(`Updating milestone mission ${mission.id}: ${mission.progress} -> ${newProgress}/${mission.maxProgress} (${milestonesCompleted} milestones)`);

        // Update the mission progress
        const progressRef = doc(db, 'user_mission_progress', `${userId}_${mission.id}`);

        try {
          await setDoc(progressRef, {
            userId,
            missionId: mission.id,
            progress: newProgress,
            isCompleted,
            isClaimed: false,
            completedAt: isCompleted ? new Date().toISOString() : undefined,
            updatedAt: new Date().toISOString(),
            milestonesCompleted // Track how many milestones completed
          }, { merge: true });
        } catch (firestoreError) {
          // Fallback to localStorage
          const localKey = `mission_${userId}_${mission.id}`;
          const localData = {
            userId,
            missionId: mission.id,
            progress: newProgress,
            isCompleted,
            isClaimed: false,
            completedAt: isCompleted ? new Date().toISOString() : undefined,
            updatedAt: new Date().toISOString(),
            milestonesCompleted
          };
          localStorage.setItem(localKey, JSON.stringify(localData));
          console.log(`Stored milestone mission in localStorage: ${localKey}`);
        }
      }
    } catch (error) {
      console.error('Error handling milestone mission:', error);
    }
  }

  // Handle daily milestone mission tracking (resets daily but works like milestones within the day)
  private static async handleDailyMilestoneMission(userId: string, mission: Mission, activityType: ActivityType): Promise<void> {
    try {
      // Get current progress for today only (daily missions reset)
      const progressRef = doc(db, 'user_mission_progress', `${userId}_${mission.id}`);
      let currentProgress = mission.progress;

      // Get milestones for this mission
      const milestones = this.getMilestonesForMission(mission.id);

      // Increment progress by 1
      const newProgress = currentProgress + 1;

      // Find the highest milestone reached
      let milestoneProgress = 0;
      let milestonesCompleted = 0;

      for (const milestone of milestones) {
        if (newProgress >= milestone) {
          milestoneProgress = milestone;
          milestonesCompleted++;
        } else {
          break;
        }
      }

      const isCompleted = milestoneProgress >= milestones[milestones.length - 1];

      console.log(`Updating daily milestone mission ${mission.id}: ${currentProgress} -> ${newProgress} (milestone: ${milestoneProgress})`);

      try {
        await setDoc(progressRef, {
          userId,
          missionId: mission.id,
          progress: newProgress,
          isCompleted,
          isClaimed: false,
          completedAt: isCompleted ? new Date().toISOString() : undefined,
          updatedAt: new Date().toISOString(),
          milestonesCompleted
        }, { merge: true });
      } catch (firestoreError) {
        // Fallback to localStorage
        const localKey = `mission_${userId}_${mission.id}`;
        const localData = {
          userId,
          missionId: mission.id,
          progress: newProgress,
          isCompleted,
          isClaimed: false,
          completedAt: isCompleted ? new Date().toISOString() : undefined,
          updatedAt: new Date().toISOString(),
          milestonesCompleted
        };
        localStorage.setItem(localKey, JSON.stringify(localData));
        console.log(`Stored daily milestone mission in localStorage: ${localKey}`);
      }
    } catch (error) {
      console.error('Error handling daily milestone mission:', error);
    }
  }

  // Get milestones for specific mission types
  private static getMilestonesForMission(missionId: string): number[] {
    switch (missionId) {
      case 'tool_collector_milestone': return [3, 5, 10, 15, 25, 50];
      case 'toolkit_creator_milestone': return [1, 3, 5, 10, 15];
      case 'academy_scholar_milestone': return [1, 3, 5, 10];
      // Daily milestone missions
      case 'like_posts_daily': return [3, 5, 10, 15];
      case 'time_keeper_daily': return [10, 15, 25, 30, 45, 60];
      default: return [1];
    }
  }

  // Get current milestone info for a mission based on progress
  static getCurrentMilestoneInfo(missionId: string, currentProgress: number): {
    currentTarget: number;
    currentXP: number;
    description: string;
    nextTarget?: number;
    nextXP?: number;
    isCompleted?: boolean;
  } {
    const milestones = this.getMilestonesForMission(missionId);
    const activityType = this.getActivityTypeForMission(missionId);
    const baseXP = XP_REWARDS[activityType] || 5;

    // Find the next uncompleted milestone (the one to show in main card)
    for (let i = 0; i < milestones.length; i++) {
      const milestone = milestones[i];
      if (currentProgress < milestone) {
        const tier = i + 1;
        const xpReward = baseXP * milestone + (5 * tier);
        const nextMilestone = milestones[i + 1];
        const nextTier = i + 2;
        const nextXP = nextMilestone ? baseXP * nextMilestone + (5 * nextTier) : undefined;

        return {
          currentTarget: milestone,
          currentXP: xpReward,
          description: this.getMilestoneDescription(missionId, milestone),
          nextTarget: nextMilestone,
          nextXP,
          isCompleted: false
        };
      }
    }

    // All milestones completed - show the last one as completed
    const lastMilestone = milestones[milestones.length - 1];
    const lastTier = milestones.length;
    const lastXP = baseXP * lastMilestone + (5 * lastTier);

    return {
      currentTarget: lastMilestone,
      currentXP: lastXP,
      description: this.getMilestoneDescription(missionId, lastMilestone) + ' (All Complete!)',
      isCompleted: true
    };
  }

  // Get detailed milestone progression for a mission
  static getDetailedMilestones(missionId: string): Array<{
    target: number;
    xp: number;
    tier: string;
    description: string;
  }> {
    const milestones = this.getMilestonesForMission(missionId);
    const activityType = this.getActivityTypeForMission(missionId);
    const baseXP = XP_REWARDS[activityType] || 5;
    const tierNames = ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond', 'Master'];

    return milestones.map((milestone, index) => {
      const tier = index + 1;
      const xpReward = baseXP * milestone + (5 * tier);

      return {
        target: milestone,
        xp: xpReward,
        tier: tierNames[index] || `Tier ${tier}`,
        description: this.getMilestoneDescription(missionId, milestone)
      };
    });
  }

  // Get activity type for mission ID
  private static getActivityTypeForMission(missionId: string): ActivityType {
    switch (missionId) {
      case 'tool_collector_milestone': return 'ADD_TOOL_TO_LIBRARY';
      case 'toolkit_creator_milestone': return 'CREATE_TOOLKIT';
      case 'academy_scholar_milestone': return 'COMPLETE_LESSON';
      // Daily milestone missions
      case 'like_posts_daily': return 'LIKE_POST';
      case 'time_keeper_daily': return 'STAY_ONLINE';
      default: return 'ADD_TOOL_TO_LIBRARY';
    }
  }

  // Get milestone description
  private static getMilestoneDescription(missionId: string, target: number): string {
    switch (missionId) {
      case 'tool_collector_milestone': return `Collect ${target} tools`;
      case 'toolkit_creator_milestone': return `Create ${target} toolkit${target > 1 ? 's' : ''}`;
      case 'academy_scholar_milestone': return `Complete ${target} course${target > 1 ? 's' : ''}`;
      // Daily milestone missions
      case 'like_posts_daily': return `Like ${target} post${target > 1 ? 's' : ''} today`;
      case 'time_keeper_daily': return `Stay online for ${target} minute${target > 1 ? 's' : ''} today`;
      default: return `Complete ${target} items`;
    }
  }

  // Get total activity count for a user (this would need to be implemented based on your data structure)
  private static async getTotalActivityCount(userId: string, activityType: ActivityType): Promise<number> {
    try {
      // For now, return a mock count based on localStorage or implement actual counting logic
      // This should count total activities across the user's history

      // Check localStorage for a simple counter (temporary solution)
      const counterKey = `total_${activityType.toLowerCase()}_${userId}`;
      const stored = localStorage.getItem(counterKey);
      let count = stored ? parseInt(stored) : 0;

      // Increment the count for this activity
      count += 1;
      localStorage.setItem(counterKey, count.toString());

      console.log(`Updated total ${activityType} count to ${count} for user ${userId}`);
      return count;
    } catch (error) {
      console.error('Error getting total activity count:', error);
      return 1; // Default to 1 if error
    }
  }

  // Handle daily login mission with proper date checking
  private static async handleDailyLoginMission(userId: string, missionId: string): Promise<void> {
    try {
      // Validate user ID
      if (!userId || userId.trim() === '') {
        console.error('MissionService: Invalid userId for daily login mission');
        return;
      }

      // Prevent duplicate processing
      const userKey = `${userId}_${missionId}`;
      if (this.dailyLoginInProgress.has(userKey)) {
        console.log('MissionService: Daily login already in progress for user:', userId);
        return;
      }

      this.dailyLoginInProgress.add(userKey);

      // Check if user is authenticated
      const { auth } = await import('@/lib/firebase');
      if (!auth.currentUser || auth.currentUser.uid !== userId) {
        console.warn('MissionService: User not authenticated or UID mismatch, falling back to localStorage');
        throw new Error('User not authenticated');
      }

      const progressRef = doc(db, 'user_mission_progress', `${userId}_${missionId}`);

      // Use consistent date format (local date in YYYY-MM-DD format)
      const today = new Date();
      const todayString = today.getFullYear() + '-' +
        String(today.getMonth() + 1).padStart(2, '0') + '-' +
        String(today.getDate()).padStart(2, '0');

      console.log('MissionService: Daily login mission check - Today:', todayString, 'UserId:', userId, 'Auth UID:', auth.currentUser.uid);

      try {
        const progressDoc = await getDoc(progressRef);

        if (progressDoc.exists()) {
          const currentData = progressDoc.data() as UserMissionProgress;
          const lastCompletedDate = currentData.completedAt ?
            new Date(currentData.completedAt).toISOString().split('T')[0] : null;

          // Only update if not already completed today
          if (lastCompletedDate !== todayString) {
            console.log('MissionService: Updating daily login mission in Firestore');
            await updateDoc(progressRef, {
              progress: 1,
              isCompleted: true,
              isClaimed: false,
              completedAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            });
          } else {
            console.log('MissionService: Daily login already completed today:', lastCompletedDate);
          }
        } else {
          // Create new progress entry
          console.log('MissionService: Creating new daily login mission progress in Firestore');
          const newProgressData = {
            userId,
            missionId,
            progress: 1,
            isCompleted: true,
            isClaimed: false,
            completedAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          console.log('MissionService: Progress data to create:', newProgressData);
          await setDoc(progressRef, newProgressData);
          console.log('MissionService: Successfully created daily login mission progress in Firestore');
        }
      } catch (firestoreError: any) {
        console.error('MissionService: Firestore permission error for daily login mission, using local tracking:', {
          error: firestoreError.message,
          code: firestoreError.code,
          userId,
          missionId,
          documentId: `${userId}_${missionId}`
        });

        // Check if already completed today in localStorage
        const localKey = `mission_${userId}_${missionId}`;
        const existingData = localStorage.getItem(localKey);

        if (existingData) {
          try {
            const parsed = JSON.parse(existingData);
            const completedDate = parsed.completedAt ? new Date(parsed.completedAt).toISOString().split('T')[0] : null;

            console.log('MissionService: Checking existing localStorage data:', {
              completedDate,
              todayString,
              isCompleted: parsed.isCompleted,
              isClaimed: parsed.isClaimed
            });

            if (completedDate === todayString && parsed.isCompleted) {
              console.log('MissionService: Daily login already completed today in localStorage:', completedDate);
              console.log('MissionService: Preventing duplicate daily login completion');
              return;
            }

            // Also check if it was claimed today (extra safety)
            if (completedDate === todayString && parsed.isClaimed) {
              console.log('MissionService: Daily login already claimed today in localStorage:', completedDate);
              console.log('MissionService: Preventing duplicate daily login claim');
              return;
            }
          } catch (parseError) {
            console.warn('Error parsing existing localStorage data:', parseError);
          }
        }

        // Store in localStorage as fallback with simpler structure
        const localData = {
          userId,
          missionId,
          progress: 1,
          isCompleted: true,
          isClaimed: false,
          completedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        localStorage.setItem(localKey, JSON.stringify(localData));
        console.log('MissionService: Saved to localStorage:', localKey, localData);
      }
    } catch (error) {
      console.error('Error handling daily login mission:', error);
    } finally {
      // Clean up the progress flag
      const userKey = `${userId}_${missionId}`;
      this.dailyLoginInProgress.delete(userKey);
    }
  }
}

// CRYSTAL CLEAR MISSION TRACKER - SIMPLE AND FUNCTIONAL
export const MissionTracker = {
  // Simple tracking functions that actually work
  addToolToLibrary: (userId: string) => {
    console.log('🎯 Tracking: Add Tool to Library');
    MissionService.trackActivity(userId, 'ADD_TOOL_TO_LIBRARY');
  },

  createTool: (userId: string) => {
    console.log('🎯 Tracking: Create Tool');
    MissionService.trackActivity(userId, 'CREATE_TOOL');
  },

  createToolkit: (userId: string) => {
    console.log('🎯 Tracking: Create Toolkit');
    MissionService.trackActivity(userId, 'CREATE_TOOLKIT');
  },

  createFirstPortfolio: (userId: string) => {
    console.log('🎯 Tracking: Create First Portfolio');
    MissionService.trackActivity(userId, 'CREATE_FIRST_PORTFOLIO');
  },

  createPortfolioItem: (userId: string) => {
    console.log('🎯 Tracking: Create Portfolio Item');
    MissionService.trackActivity(userId, 'CREATE_PORTFOLIO_ITEM');
  },

  completeLesson: (userId: string) => {
    console.log('🎯 Tracking: Complete Lesson');
    MissionService.trackActivity(userId, 'COMPLETE_LESSON');
  },

  createPost: (userId: string) => {
    console.log('🎯 Tracking: Create Post');
    MissionService.trackActivity(userId, 'CREATE_POST');
  },

  shareTool: (userId: string) => {
    console.log('🎯 Tracking: Share Tool');
    MissionService.trackActivity(userId, 'SHARE_TOOL');
  },

  likePost: (userId: string) => {
    console.log('🎯 Tracking: Like Post');
    MissionService.trackActivity(userId, 'LIKE_POST');
  },

  dailyLogin: (userId: string) => {
    console.log('🎯 Tracking: Daily Login');
    MissionService.trackActivity(userId, 'DAILY_LOGIN');
  },

  // Simple time tracking
  stayOnline: (userId: string) => {
    console.log('🎯 Tracking: Stay Online (1 minute)');
    MissionService.trackActivity(userId, 'STAY_ONLINE');
  },

  // Start automatic time tracking
  startTimeTracking: (userId: string) => {
    if (typeof window !== 'undefined') {
      console.log('🎯 Starting time tracking for user:', userId);

      // Track time every minute
      const interval = setInterval(() => {
        console.log('🎯 Auto-tracking: 1 minute online');
        MissionService.trackActivity(userId, 'STAY_ONLINE');
      }, 60000); // Every minute

      // Store interval ID for cleanup
      localStorage.setItem(`time_interval_${userId}`, interval.toString());
    }
  },

  // Stop time tracking
  stopTimeTracking: (userId: string) => {
    if (typeof window !== 'undefined') {
      const intervalId = localStorage.getItem(`time_interval_${userId}`);
      if (intervalId) {
        console.log('🎯 Stopping time tracking for user:', userId);
        clearInterval(parseInt(intervalId));
        localStorage.removeItem(`time_interval_${userId}`);
      }
    }
  }
};

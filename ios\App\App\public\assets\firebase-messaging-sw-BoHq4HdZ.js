importScripts("https://www.gstatic.com/firebasejs/10.5.0/firebase-app-compat.js");importScripts("https://www.gstatic.com/firebasejs/10.5.0/firebase-messaging-compat.js");let c={apiKey:"AIzaSyCSSBKFkrnBoK0b1Y3RmA97WdwcY9YLKcA",authDomain:"smai-og.firebaseapp.com",projectId:"smai-og",storageBucket:"smai-og.firebasestorage.app",messagingSenderId:"220186510992",appId:"1:220186510992:web:3d9e07c3df55d1f4ea7a15",measurementId:"G-4MR0WK595H"},r=null;self.addEventListener("message",i=>{var t;((t=i.data)==null?void 0:t.type)==="FIREBASE_CONFIG"&&(i.data.config&&(c=i.data.config),f())});function f(){try{firebase.apps.length||firebase.initializeApp(c),r=firebase.messaging(),console.log("Firebase Messaging initialized in service worker"),l()}catch(i){console.error("Failed to initialize Firebase in service worker:",i)}}function l(){r&&r.onBackgroundMessage(async i=>{var o,e,a;console.log("[firebase-messaging-sw.js] Received background message:",i);const t=((o=i.notification)==null?void 0:o.title)||"New Message",n={body:((e=i.notification)==null?void 0:e.body)||"",icon:"/logo.png",badge:"/logo.png",tag:((a=i.data)==null?void 0:a.tag)||"default",data:i.data||{},requireInteraction:!0};try{await self.registration.showNotification(t,n)}catch(s){console.error("Error showing notification:",s)}})}self.addEventListener("notificationclick",i=>{console.log("[firebase-messaging-sw.js] Notification clicked:",i),i.notification.close(),i.waitUntil(clients.matchAll({type:"window",includeUncontrolled:!0}).then(t=>{var o;const n=((o=i.notification.data)==null?void 0:o.url)||"/";for(const e of t){const a=new URL(e.url),s=new URL(n,self.location.origin);if(a.pathname===s.pathname&&"focus"in e)return e.focus()}if(clients.openWindow){const e=new URL(n,self.location.origin).href;return clients.openWindow(e)}}))});
//# sourceMappingURL=firebase-messaging-sw-BoHq4HdZ.js.map

import { useEffect, useState, useCallback } from 'react';
import <PERSON><PERSON>per, { Area } from 'react-easy-crop';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import type { ImageSettings } from '@/types/tools';

interface ImageCropperProps {
    imageFile?: File;
    imageUrl?: string;
    settings?: ImageSettings;
    onComplete: (originalUrl: string, settings: ImageSettings) => void;
}

export function ImageCropper({
    imageFile,
    imageUrl,
    settings: initialSettings, // Used for initialization
    onComplete
}: ImageCropperProps) {
    const [crop, setCrop] = useState({ x: 0, y: 0 });
    const [zoom, setZoom] = useState(initialSettings?.scale || 1);
    const [imageSrc, setImageSrc] = useState<string>('');
    const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

    // Create image URL from file
    const processImage = useCallback(
        async (file: File) => {
            try {
                const url = URL.createObjectURL(file);
                setImageSrc(url);
                return url;
            } catch (error) {
                console.error('Error processing image:', error);
                return null;
            }
        },
        []
    );

    // Handle completion
    const handleComplete = useCallback(() => {
        if (!croppedAreaPixels) return;

        const settings: ImageSettings = {
            size: 'contain',
            position: 'center',
            padding: 0,
            scale: zoom,
            rotate: 0,
            crop: {
                x: croppedAreaPixels.x,
                y: croppedAreaPixels.y,
                width: croppedAreaPixels.width,
                height: croppedAreaPixels.height,
                unit: '%'
            }
        };

        onComplete(imageSrc, settings);
    }, [imageSrc, croppedAreaPixels, zoom, onComplete]);

    // Process image when file or URL changes
    useEffect(() => {
        if (imageFile) {
            processImage(imageFile).catch(console.error);
        } else if (imageUrl) {
            setImageSrc(imageUrl);
        }
    }, [imageFile, imageUrl, processImage]);

    const onCropComplete = useCallback((_: Area, croppedAreaPixels: Area) => {
        setCroppedAreaPixels(croppedAreaPixels);
    }, []);

    if (!imageSrc) {
        return (
            <div className="flex items-center justify-center h-[300px]">
                <p className="text-gray-400">Loading image...</p>
            </div>
        );
    }

    return (
        <div className="p-6 space-y-6">
            <div className="relative h-[300px]">
                <Cropper
                    image={imageSrc}
                    crop={crop}
                    zoom={zoom}
                    aspect={1}
                    onCropChange={setCrop}
                    onZoomChange={setZoom}
                    onCropComplete={onCropComplete}
                />
            </div>

            <div className="space-y-4">
                <div className="space-y-2">
                    <p className="text-sm font-medium">Zoom</p>
                    <Slider
                        min={1}
                        max={3}
                        step={0.1}
                        value={[zoom]}
                        onValueChange={([value]) => setZoom(value)}
                    />
                </div>

                <div className="flex justify-end">
                    <Button onClick={handleComplete}>
                        Crop & Save
                    </Button>
                </div>
            </div>
        </div>
    );
}


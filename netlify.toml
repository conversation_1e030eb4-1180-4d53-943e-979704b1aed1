[build]
  publish = "dist"
  command = "npm run build"

[[headers]]
  for = "/*"
  [headers.values]
    Cross-Origin-Embedder-Policy = "unsafe-none"
    Cross-Origin-Opener-Policy = "same-origin-allow-popups"
    Cross-Origin-Resource-Policy = "cross-origin"
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With"
    Access-Control-Allow-Credentials = "true"
    Content-Security-Policy = "default-src 'self' blob: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.gpteng.co https://*.googleapis.com https://*.google.com https://*.gstatic.com https://*.ytimg.com https://apis.google.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: https: http: https://*.weserv.nl https://asset.brandfetch.io https://converter.app https://chatgpt.com https://*.ytimg.com https://images.unsplash.com https://plus.unsplash.com https://framerusercontent.com https://storage.googleapis.com https://upload.wikimedia.org https://seeklogo.com https://media.assettype.com https://blueerasoftech.com https://d3njjcbhbojbot.cloudfront.net; connect-src 'self' blob: data: ws: wss: http://localhost:* https://localhost:* https://*.googleapis.com https://*.google.com https://*.firebaseio.com https://*.firebasestorage.googleapis.com https://storage.googleapis.com https://cdn.gpteng.co https://*.livepeer.studio https://livepeer.studio https://*.lvpr.tv https://lvpr.tv https://*.livepeer.com https://origin.livepeer.com https://*.lp-playback.studio https://vod-cdn.lp-playback.studio https://livepeercdn.com https://*.livepeercdn.com https://test-streams.mux.dev https://api.mux.com https://*.mux.com https://stream.mux.com https://image.mux.com https://*.doubleclick.net https://asset.brandfetch.io https://converter.app https://chatgpt.com https://firestore.googleapis.com https://identitytoolkit.googleapis.com https://*.ytimg.com https://apis.google.com https://securetoken.googleapis.com; frame-src 'self' https://*.google.com https://*.firebaseapp.com https://*.googleapis.com https://*.lvpr.tv https://lvpr.tv https://www.youtube.com https://youtube.com; media-src 'self' blob: data: https://*.livepeer.studio https://livepeer.studio https://*.lvpr.tv https://lvpr.tv https://*.livepeer.com https://origin.livepeer.com https://livepeer.com https://*.lp-playback.studio https://vod-cdn.lp-playback.studio https://livepeercdn.com https://*.livepeercdn.com https://test-streams.mux.dev https://stream.mux.com https://*.mux.com https://*.doubleclick.net https://www.youtube.com https://youtube.com; worker-src 'self' blob:; child-src 'self' https://www.youtube.com https://youtube.com;"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

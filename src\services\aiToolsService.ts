import { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, deleteField } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { AITool } from '@/types';

const COLLECTION_NAME = 'aiTools';

// Helper function to format website URLs
export const formatWebsiteUrl = (url: string): string => {
  if (!url) return '';
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }
  return url;
};

// Helper function to process tags
export const processTags = (tags: string | string[] | undefined): string[] => {
  if (!tags) return [];
  if (Array.isArray(tags)) return tags;
  return tags.split(',').map(tag => tag.trim()).filter(Boolean);
};

// Add a single AI tool
export const addAITool = async (tool: Omit<AITool, 'id'>): Promise<string> => {
  try {
    const toolWithTimestamp = {
      ...tool,
      tags: processTags(tool.tags),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const docRef = await addDoc(collection(db, COLLECTION_NAME), toolWithTimestamp);
    return docRef.id;
  } catch (error) {
    console.error('Error adding AI tool:', error);
    throw error;
  }
};

// Bulk add AI tools
export const bulkAddAITools = async (tools: Omit<AITool, 'id'>[]): Promise<string[]> => {
  try {
    const ids: string[] = [];

    for (const tool of tools) {
      const toolWithTimestamp = {
        ...tool,
        tags: processTags(tool.tags),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const docRef = await addDoc(collection(db, COLLECTION_NAME), toolWithTimestamp);
      ids.push(docRef.id);
    }

    return ids;
  } catch (error) {
    console.error('Error bulk adding AI tools:', error);
    throw error;
  }
};

// Get all AI tools
export const getAllAITools = async (): Promise<AITool[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, COLLECTION_NAME));

    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        tags: processTags(data.tags)
      } as AITool;
    });
  } catch (error) {
    console.error('Error getting AI tools:', error);
    throw error;
  }
};

// Get AI tools by tag
export const getAIToolsByTag = async (tag: string): Promise<AITool[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, COLLECTION_NAME));

    return querySnapshot.docs
      .map(doc => ({
        id: doc.id,
        ...doc.data(),
        tags: processTags(doc.data().tags)
      } as AITool))
      .filter(tool => {
        const toolTags = processTags(tool.tags);
        return toolTags.some(t => t.toLowerCase().includes(tag.toLowerCase()));
      });
  } catch (error) {
    console.error('Error getting AI tools by tag:', error);
    throw error;
  }
};

// Update an AI tool
export const updateAITool = async (id: string, tool: Partial<AITool>): Promise<void> => {
  try {
    const toolRef = doc(db, COLLECTION_NAME, id);
    const updateData = {
      ...tool,
      tags: tool.tags ? processTags(tool.tags) : undefined,
      updatedAt: new Date().toISOString()
    };

    if (tool.imageSettings === null || tool.imageSettings === undefined) {
      await updateDoc(toolRef, {
        ...updateData,
        imageSettings: deleteField()
      });
    } else {
      await updateDoc(toolRef, updateData);
    }
  } catch (error) {
    console.error('Error updating AI tool:', error);
    throw error;
  }
};

// Delete an AI tool
export const deleteAITool = async (id: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, COLLECTION_NAME, id));
  } catch (error) {
    console.error('Error deleting AI tool:', error);
    throw error;
  }
};

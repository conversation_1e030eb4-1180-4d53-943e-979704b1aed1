// Helper functions for Firestore operations

export const cleanFirestoreData = <T extends Record<string, any>>(data: T): Partial<T> => {
    const cleanedData: Partial<T> = {};

    Object.entries(data).forEach(([key, value]) => {
        // Skip undefined values
        if (value === undefined) {
            return;
        }

        // Handle nested objects
        if (value && typeof value === 'object' && !Array.isArray(value)) {
            const cleanedNested = cleanFirestoreData(value);
            if (Object.keys(cleanedNested).length > 0) {
                cleanedData[key as keyof T] = cleanedNested as any;
            }
            return;
        }

        // Handle arrays
        if (Array.isArray(value)) {
            // Filter out undefined values from arrays
            const cleanedArray = value.filter(item => item !== undefined);
            if (cleanedArray.length > 0) {
                cleanedData[key as keyof T] = cleanedArray as any;
            }
            return;
        }

        // Add valid value to cleaned data
        cleanedData[key as keyof T] = value;
    });

    return cleanedData;
};

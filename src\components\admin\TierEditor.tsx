import { useState } from 'react';
import { Tier } from '@/types/academy';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Save, X } from 'lucide-react';

interface TierEditorProps {
  tier: Tier;
  onSave: (tier: Tier) => void;
  onCancel: () => void;
}

const TierEditor = ({ tier, onSave, onCancel }: TierEditorProps) => {
  const [editableTier, setEditableTier] = useState<Tier>({...tier});
  
  const handleChange = (field: string, value: any) => {
    setEditableTier(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(editableTier);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4 py-4">
      <div className="space-y-2">
        <Label htmlFor="tier-name">Learning Path Name</Label>
        <Input
          id="tier-name"
          value={editableTier.name}
          onChange={(e) => handleChange('name', e.target.value)}
          placeholder="e.g., AI Fundamentals"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="tier-description">Description</Label>
        <Textarea
          id="tier-description"
          value={editableTier.description || ''}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Describe what students will learn in this path..."
          rows={3}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="tier-order">Display Order</Label>
          <Input
            id="tier-order"
            type="number"
            value={editableTier.order || 0}
            onChange={(e) => handleChange('order', parseInt(e.target.value))}
            min={0}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="tier-xp">XP Required to Unlock</Label>
          <Input
            id="tier-xp"
            type="number"
            value={editableTier.requiredXp || 0}
            onChange={(e) => handleChange('requiredXp', parseInt(e.target.value))}
            min={0}
          />
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="tier-unlock"
          checked={editableTier.isUnlocked}
          onCheckedChange={(checked) => handleChange('isUnlocked', checked)}
        />
        <Label htmlFor="tier-unlock">Unlock this learning path for all users</Label>
      </div>
      
      <Separator />
      
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button type="submit" className="bg-sortmy-blue hover:bg-sortmy-blue/90">
          <Save className="mr-2 h-4 w-4" />
          Save Learning Path
        </Button>
      </div>
    </form>
  );
};

export default TierEditor;
import { collection, getDocs, query, where, addDoc, deleteDoc, doc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Tool } from '@/types';
// import { XPActions } from '@/services/xpService';
import { MissionTracker } from '@/services/missionService';

export const fetchUserTools = async (userId: string): Promise<Tool[]> => {
  const q = query(
    collection(db, 'userTools'),
    where('userId', '==', userId)
  );
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Tool));
};

export const fetchAllTools = async (): Promise<Tool[]> => {
  const q = query(collection(db, 'tools'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Tool));
};

export const addUserTool = async (userId: string, tool: Tool) => {
  await addDoc(collection(db, 'userTools'), {
    ...tool,
    userId,
    addedAt: new Date().toISOString()
  });

  // Track mission progress and award XP for adding tool to library
  try {
    await MissionTracker.addToolToLibrary(userId);
    // Note: XP is now claimed manually through missions, not auto-awarded
  } catch (error) {
    console.error('Error tracking mission progress for adding tool:', error);
    // Don't throw error to avoid breaking the main flow
  }
};

export const deleteUserTool = async (toolId: string) => {
  await deleteDoc(doc(db, 'userTools', toolId));
};

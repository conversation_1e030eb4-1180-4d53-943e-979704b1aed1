{"version": 3, "file": "firebase-messaging-sw-BoHq4HdZ.js", "sources": ["../../public/firebase-messaging-sw.js"], "sourcesContent": ["// Give the service worker access to Firebase Messaging.\r\nimportScripts('https://www.gstatic.com/firebasejs/10.5.0/firebase-app-compat.js');\r\nimportScripts('https://www.gstatic.com/firebasejs/10.5.0/firebase-messaging-compat.js');\r\n\r\n// Default configuration that will be overridden\r\nlet firebaseConfig = {\r\n  apiKey: \"AIzaSyCSSBKFkrnBoK0b1Y3RmA97WdwcY9YLKcA\",\r\n  authDomain: \"smai-og.firebaseapp.com\",\r\n  projectId: \"smai-og\",\r\n  storageBucket: \"smai-og.firebasestorage.app\",\r\n  messagingSenderId: \"220186510992\",\r\n  appId: \"1:220186510992:web:3d9e07c3df55d1f4ea7a15\",\r\n  measurementId: \"G-4MR0WK595H\"\r\n};\r\n\r\nlet messaging = null;\r\n\r\n// Listen for messages from the main thread\r\nself.addEventListener('message', (event) => {\r\n  if (event.data?.type === 'FIREBASE_CONFIG') {\r\n    // Only update the config if we receive a valid one\r\n    if (event.data.config) {\r\n      firebaseConfig = event.data.config;\r\n    }\r\n    initializeFirebase();\r\n  }\r\n});\r\n\r\n// Initialize Firebase\r\nfunction initializeFirebase() {\r\n  try {\r\n    if (!firebase.apps.length) {\r\n      firebase.initializeApp(firebaseConfig);\r\n    }\r\n    messaging = firebase.messaging();\r\n    console.log('Firebase Messaging initialized in service worker');\r\n    setupBackgroundListener();\r\n  } catch (error) {\r\n    console.error('Failed to initialize Firebase in service worker:', error);\r\n  }\r\n}\r\n\r\n// Set up background message handler\r\nfunction setupBackgroundListener() {\r\n  if (!messaging) return;\r\n\r\n  messaging.onBackgroundMessage(async (payload) => {\r\n    console.log('[firebase-messaging-sw.js] Received background message:', payload);\r\n\r\n    const notificationTitle = payload.notification?.title || 'New Message';\r\n    const notificationOptions = {\r\n      body: payload.notification?.body || '',\r\n      icon: '/logo.png',\r\n      badge: '/logo.png',\r\n      tag: payload.data?.tag || 'default',\r\n      data: payload.data || {},\r\n      requireInteraction: true\r\n    };\r\n\r\n    try {\r\n      await self.registration.showNotification(notificationTitle, notificationOptions);\r\n    } catch (error) {\r\n      console.error('Error showing notification:', error);\r\n    }\r\n  });\r\n}\r\n\r\n// Handle notification click\r\nself.addEventListener('notificationclick', (event) => {\r\n  console.log('[firebase-messaging-sw.js] Notification clicked:', event);\r\n  \r\n  event.notification.close();\r\n  \r\n  // This looks to see if the current is already open and focuses if it is\r\n  event.waitUntil(\r\n    clients.matchAll({ \r\n      type: 'window', \r\n      includeUncontrolled: true \r\n    })\r\n    .then((clientList) => {\r\n      // Get URL from notification data or use default\r\n      const url = event.notification.data?.url || '/';\r\n      \r\n      // Try to focus an existing window first\r\n      for (const client of clientList) {\r\n        const clientUrl = new URL(client.url);\r\n        const notificationUrl = new URL(url, self.location.origin);\r\n        \r\n        if (clientUrl.pathname === notificationUrl.pathname && 'focus' in client) {\r\n          return client.focus();\r\n        }\r\n      }\r\n      \r\n      // If no window is open or matching URL not found, open a new one\r\n      if (clients.openWindow) {\r\n        // Ensure URL is absolute\r\n        const absoluteUrl = new URL(url, self.location.origin).href;\r\n        return clients.openWindow(absoluteUrl);\r\n      }\r\n    })\r\n  );\r\n});\r\n"], "names": ["firebaseConfig", "messaging", "event", "_a", "initializeFirebase", "setupBackgroundListener", "error", "payload", "_b", "_c", "notificationTitle", "notificationOptions", "clientList", "url", "client", "clientUrl", "notificationUrl", "absoluteUrl"], "mappings": "AACA,cAAc,kEAAkE,EAChF,cAAc,wEAAwE,EAGtF,IAAIA,EAAiB,CACnB,OAAQ,0CACR,WAAY,0BACZ,UAAW,UACX,cAAe,8BACf,kBAAmB,eACnB,MAAO,4CACP,cAAe,cACjB,EAEIC,EAAY,KAGhB,KAAK,iBAAiB,UAAYC,GAAU,CAlB5C,IAAAC,IAmBMA,EAAAD,EAAM,OAAN,YAAAC,EAAY,QAAS,oBAEnBD,EAAM,KAAK,SACbF,EAAiBE,EAAM,KAAK,QAE9BE,IAEJ,CAAC,EAGD,SAASA,GAAqB,CAC5B,GAAI,CACG,SAAS,KAAK,QACjB,SAAS,cAAcJ,CAAc,EAEvCC,EAAY,SAAS,YACrB,QAAQ,IAAI,kDAAkD,EAC9DI,GACD,OAAQC,EAAO,CACd,QAAQ,MAAM,mDAAoDA,CAAK,CACxE,CACH,CAGA,SAASD,GAA0B,CAC5BJ,GAELA,EAAU,oBAAoB,MAAOM,GAAY,CA9CnD,IAAAJ,EAAAK,EAAAC,EA+CI,QAAQ,IAAI,0DAA2DF,CAAO,EAE9E,MAAMG,IAAoBP,EAAAI,EAAQ,eAAR,YAAAJ,EAAsB,QAAS,cACnDQ,EAAsB,CAC1B,OAAMH,EAAAD,EAAQ,eAAR,YAAAC,EAAsB,OAAQ,GACpC,KAAM,YACN,MAAO,YACP,MAAKC,EAAAF,EAAQ,OAAR,YAAAE,EAAc,MAAO,UAC1B,KAAMF,EAAQ,MAAQ,CAAE,EACxB,mBAAoB,EAC1B,EAEI,GAAI,CACF,MAAM,KAAK,aAAa,iBAAiBG,EAAmBC,CAAmB,CAChF,OAAQL,EAAO,CACd,QAAQ,MAAM,8BAA+BA,CAAK,CACnD,CACL,CAAG,CACH,CAGA,KAAK,iBAAiB,oBAAsBJ,GAAU,CACpD,QAAQ,IAAI,mDAAoDA,CAAK,EAErEA,EAAM,aAAa,QAGnBA,EAAM,UACJ,QAAQ,SAAS,CACf,KAAM,SACN,oBAAqB,EAC3B,CAAK,EACA,KAAMU,GAAe,CA/E1B,IAAAT,EAiFM,MAAMU,IAAMV,EAAAD,EAAM,aAAa,OAAnB,YAAAC,EAAyB,MAAO,IAG5C,UAAWW,KAAUF,EAAY,CAC/B,MAAMG,EAAY,IAAI,IAAID,EAAO,GAAG,EAC9BE,EAAkB,IAAI,IAAIH,EAAK,KAAK,SAAS,MAAM,EAEzD,GAAIE,EAAU,WAAaC,EAAgB,UAAY,UAAWF,EAChE,OAAOA,EAAO,OAEjB,CAGD,GAAI,QAAQ,WAAY,CAEtB,MAAMG,EAAc,IAAI,IAAIJ,EAAK,KAAK,SAAS,MAAM,EAAE,KACvD,OAAO,QAAQ,WAAWI,CAAW,CACtC,CACP,CAAK,CACL,CACA,CAAC"}
// import { doc, updateDoc, increment, getDoc, serverTimestamp } from 'firebase/firestore';
// import { db } from '@/lib/firebase';
// import { User } from '@/types';

// XP reward amounts for different activities
export const XP_REWARDS = {
    // Tool-related activities
    ADD_TOOL_TO_LIBRARY: 5,
    CREATE_TOOL: 10,
    CREATE_TOOLKIT: 10,
    SHARE_TOOL: 10,

    // Portfolio activities
    CREATE_FIRST_PORTFOLIO: 50,
    CREATE_PORTFOLIO_ITEM: 25,

    // Academy activities
    COMPLETE_LESSON: 25,
    COMPLETE_COURSE: 100,

    // Social activities
    CREATE_POST: 25,
    LIKE_POST: 2,
    COMMENT_ON_POST: 5,

    // Daily activities
    DAILY_LOGIN: 25,
    WEEKLY_STREAK_BASE: 100,

    // Time-based activities
    STAY_ONLINE: 2, // XP per minute online

    // Milestone bonuses
    FIRST_TIME_BONUS: 50, // Additional bonus for first-time activities
} as const;

// Activity types for tracking
export type ActivityType = keyof typeof XP_REWARDS;

// XP activity log entry
export interface XPActivity {
    id: string;
    userId: string;
    activityType: ActivityType;
    xpEarned: number;
    timestamp: string;
    description: string;
    metadata?: Record<string, any>;
}

// Level calculation helper
export function calculateLevel(totalXP: number): number {
    const baseXP = 100;
    let level = 1;
    let xpNeeded = baseXP;
    let currentXP = totalXP;

    while (currentXP >= xpNeeded) {
        currentXP -= xpNeeded;
        level++;
        xpNeeded = Math.floor(baseXP * Math.pow(1.5, level - 2));
    }

    return level;
}

// Calculate XP needed for next level
export function calculateXPForNextLevel(currentLevel: number): number {
    const baseXP = 100;
    return Math.floor(baseXP * Math.pow(1.5, currentLevel - 1));
}

// Calculate XP progress within current level
export function calculateLevelProgress(totalXP: number, currentLevel: number): {
    currentLevelXP: number;
    xpForNextLevel: number;
    progressPercentage: number;
} {
    const baseXP = 100;
    let xpUsed = 0;

    // Calculate total XP used for previous levels
    for (let i = 1; i < currentLevel; i++) {
        xpUsed += Math.floor(baseXP * Math.pow(1.5, i - 1));
    }

    const currentLevelXP = totalXP - xpUsed;
    const xpForNextLevel = calculateXPForNextLevel(currentLevel);
    const progressPercentage = Math.min(100, Math.round((currentLevelXP / xpForNextLevel) * 100));

    return {
        currentLevelXP,
        xpForNextLevel,
        progressPercentage
    };
}

// Calculate streak bonus XP
export function calculateStreakBonus(streakDays: number): number {
    if (streakDays < 7) return 0;

    const weeklyStreaks = Math.floor(streakDays / 7);
    let totalBonus = 0;

    // Progressive weekly bonuses: 100, 200, 300, etc.
    for (let week = 1; week <= weeklyStreaks; week++) {
        totalBonus += XP_REWARDS.WEEKLY_STREAK_BASE * week;
    }

    return totalBonus;
}

// Check if user should get daily login XP
export function shouldGetDailyLoginXP(lastLogin: string): boolean {
    const lastLoginDate = new Date(lastLogin);
    const today = new Date();

    // Reset time to start of day for comparison
    lastLoginDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    // Return true if last login was not today
    return lastLoginDate.getTime() !== today.getTime();
}

// Calculate new streak days
export function calculateNewStreak(lastLogin: string): number {
    const lastLoginDate = new Date(lastLogin);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Reset times to start of day
    lastLoginDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);
    yesterday.setHours(0, 0, 0, 0);

    if (lastLoginDate.getTime() === yesterday.getTime()) {
        // Consecutive day - increment streak
        return 1; // This will be added to current streak
    } else if (lastLoginDate.getTime() === today.getTime()) {
        // Already logged in today - no change
        return 0;
    } else {
        // Streak broken - reset to 1
        return -999; // Special value to indicate reset
    }
}
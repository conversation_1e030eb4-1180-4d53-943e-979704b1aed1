import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Check if the browser supports notifications
export const checkNotificationSupport = () => {
  return 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window;
};

// Request notification permission
export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    const permission = await Notification.requestPermission();
    return permission === 'granted';
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

// Helper function to wait for service worker registration
const waitForServiceWorker = async (maxAttempts = 30, interval = 1000): Promise<ServiceWorkerRegistration | null> => {
  for (let i = 0; i < maxAttempts; i++) {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration?.active) {
      return registration;
    }
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  return null;
};

// Register the service worker for push notifications
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  try {
    // First check if service worker is already registered and active
    const existingRegistration = await navigator.serviceWorker.getRegistration();
    if (existingRegistration?.active) {
      console.log('Service worker already registered and active');
      return existingRegistration;
    }

    // Load the service worker loader script
    const script = document.createElement('script');
    script.src = '/firebase-messaging-sw-loader.js';
    script.type = 'module';
    document.head.appendChild(script);

    // Wait for the service worker to be registered and activated
    const registration = await waitForServiceWorker();
    if (registration) {
      console.log('Service worker registered successfully');
      return registration;
    }

    // If the loader script fails, try direct registration as fallback
    console.log('Attempting fallback service worker registration');
    return await navigator.serviceWorker.register('/firebase-messaging-sw.js');
  } catch (error) {
    console.error('Service worker registration failed:', error);
    return null;
  }
};

// Get FCM token and save it to the user's document
export const getFCMToken = async (userId: string): Promise<string | null> => {
  try {
    const messaging = getMessaging();

    // Check if permission is granted
    if (Notification.permission !== 'granted') {
      console.warn('Notification permission not granted');
      return null;
    }

    // Get token
    const currentToken = await getToken(messaging, {
      vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
    });

    if (currentToken) {
      // Save the token to the user's document
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        await setDoc(userRef, {
          ...userDoc.data(),
          fcmTokens: [...(userDoc.data().fcmTokens || []), currentToken]
        }, { merge: true });
      }

      return currentToken;
    } else {
      console.warn('No registration token available');
      return null;
    }
  } catch (error) {
    console.error('Error getting FCM token:', error);
    return null;
  }
};

// Set up foreground message handler
export const setupMessageHandler = (callback: (payload: any) => void) => {
  try {
    const messaging = getMessaging();

    return onMessage(messaging, (payload) => {
      console.log('Message received in foreground:', payload);
      callback(payload);
    });
  } catch (error) {
    console.error('Error setting up message handler:', error);
    return () => { };
  }
};

// Convert blob URL to data URL
const convertBlobUrlToDataUrl = async (url: string): Promise<string> => {
  try {
    const response = await fetch(url);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error converting blob URL:', error);
    throw error;
  }
};

// Extended NotificationOptions type to include image property
interface ExtendedNotificationOptions extends NotificationOptions {
  image?: string;
}

// Show a notification
export const showNotification = async (title: string, options: ExtendedNotificationOptions = {}): Promise<Notification | null> => {
  try {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return null;
    }

    // Convert any blob URLs in the options to data URLs
    if (options.icon && options.icon.startsWith('blob:')) {
      options.icon = await convertBlobUrlToDataUrl(options.icon);
    }
    if (options.image && options.image.startsWith('blob:')) {
      options.image = await convertBlobUrlToDataUrl(options.image);
    }

    if (Notification.permission === 'granted') {
      const notification = new Notification(title, {
        icon: '/logo.png',
        badge: '/logo.png',
        ...options
      });

      notification.onclick = () => {
        window.focus();
        notification.close();

        // Handle click action if provided
        if (options.data?.url) {
          window.location.href = options.data.url;
        }
      };

      return notification;
    }
    return null;
  } catch (error) {
    console.warn('Error showing notification:', error);
    return null;
  }
};

// Initialize push notifications
export const initializePushNotifications = async (userId: string) => {
  if (!checkNotificationSupport()) {
    console.warn('Push notifications are not supported in this browser');
    return false;
  }

  const permissionGranted = await requestNotificationPermission();
  if (!permissionGranted) {
    console.warn('Notification permission denied');
    return false;
  }

  const serviceWorkerRegistration = await registerServiceWorker();
  if (!serviceWorkerRegistration) {
    console.warn('Service worker registration failed');
    return false;
  }

  const token = await getFCMToken(userId);
  return !!token;
};

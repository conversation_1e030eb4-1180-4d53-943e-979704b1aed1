import React from "react";
import { <PERSON>Lef<PERSON>, ArrowRight } from "lucide-react";

interface SliderProps {
    children: React.ReactNode[];
}

const useVisibleCount = () => {
    const [visible, setVisible] = React.useState(1);
    React.useEffect(() => {
        const mq = window.matchMedia("(min-width: 768px)");
        const update = () => setVisible(mq.matches ? 3 : 1);
        update();
        mq.addEventListener("change", update);
        return () => mq.removeEventListener("change", update);
    }, []);
    return visible;
};

const Slider: React.FC<SliderProps> = ({ children }) => {
    const total = children.length;
    const visibleCount = useVisibleCount();
    const [current, setCurrent] = React.useState(0);
    const maxIndex = Math.max(0, total - visibleCount);

    const prev = () => setCurrent((c) => (c === 0 ? maxIndex : c - visibleCount < 0 ? 0 : c - visibleCount));
    const next = () => setCurrent((c) => (c >= maxIndex ? 0 : c + visibleCount > maxIndex ? maxIndex : c + visibleCount));

    const visibleItems = children.slice(current, current + visibleCount);
    // If at the end, fill with empty slots for layout consistency
    while (visibleItems.length < visibleCount) visibleItems.push(<div key={`empty-${visibleItems.length}`} className="" />);

    return (
        <div className="relative w-full flex flex-col items-center">
            <div className={`w-full grid grid-cols-1 md:grid-cols-3 gap-6 transition-all duration-300`}>
                {visibleItems}
            </div>
            <div className="flex justify-center gap-4 mt-4">
                <button
                    aria-label="Previous"
                    className="p-2 rounded-full bg-sortmy-blue/20 hover:bg-sortmy-blue/40"
                    onClick={prev}
                >
                    <ArrowLeft className="w-5 h-5 text-sortmy-blue" />
                </button>
                <button
                    aria-label="Next"
                    className="p-2 rounded-full bg-sortmy-blue/20 hover:bg-sortmy-blue/40"
                    onClick={next}
                >
                    <ArrowRight className="w-5 h-5 text-sortmy-blue" />
                </button>
            </div>
            <div className="flex justify-center gap-2 mt-2">
                {Array.from({ length: Math.ceil(total / visibleCount) }).map((_, i) => (
                    <span
                        key={i}
                        className={`w-2 h-2 rounded-full ${i === Math.floor(current / visibleCount) ? "bg-sortmy-blue" : "bg-sortmy-blue/20"}`}
                    />
                ))}
            </div>
        </div>
    );
};

export default Slider;

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { db } from '@/lib/firebase';
import { doc, collection, getDoc, setDoc, updateDoc, serverTimestamp, Timestamp, FieldValue } from 'firebase/firestore';
import type { Course, Module, Lesson } from '@/types/course';

export enum CreationStep {
    CourseDetails,
    ModuleCreation,
    LessonCreation
}

interface CourseCreationState {
    step: CreationStep;
    courseId: string | null;
    selectedModuleId: string | null;
    isDraft: boolean;
}

interface FirebaseCourse extends Omit<Course, 'createdAt' | 'updatedAt'> {
    createdAt: Timestamp | FieldValue;
    updatedAt: Timestamp | FieldValue;
}

export function useCourseCreation(courseId?: string) {
    const { user } = useAuth();
    const [state, setState] = useState<CourseCreationState>({
        step: CreationStep.CourseDetails,
        courseId: courseId || null,
        selectedModuleId: null,
        isDraft: true
    });

    const [course, setCourse] = useState<Course | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Load existing course if courseId is provided
    useEffect(() => {
        if (courseId) {
            loadCourse(courseId);
        } else {
            // Initialize empty course when creating new
            setCourse({
                id: '',
                title: '',
                description: '',
                thumbnail: '',
                modules: [],
                authorId: user?.uid || '',
                authorName: user?.email?.split('@')[0] || 'Unknown Author',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            });
            setLoading(false);
        }
    }, [courseId, user?.uid, user?.email]);

    const loadCourse = async (id: string) => {
        try {
            setLoading(true);
            const courseDoc = await getDoc(doc(db, 'courses', id));
            if (courseDoc.exists()) {
                const data = courseDoc.data() as FirebaseCourse;
                // Convert Timestamp to ISO string for Course type
                setCourse({
                    ...data,
                    id: courseDoc.id,
                    createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
                    updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : new Date().toISOString(),
                } as Course);
                setState(prev => ({
                    ...prev,
                    courseId: id,
                    step: CreationStep.ModuleCreation
                }));
            }
        } catch (err) {
            setError('Failed to load course');
            console.error('Error loading course:', err);
        } finally {
            setLoading(false);
        }
    };

    const saveCourseDetails = async (data: Partial<Course>) => {
        if (!user) return false;

        try {
            setLoading(true);
            setError(null);

            const courseData = {
                ...data,
                authorId: user.uid,
                authorName: user.email?.split('@')[0] || 'Unknown Author',
                createdBy: user.uid, // Ensure createdBy is always set for Firestore rules
                isDraft: true,
                modules: course?.modules || [],
                updatedAt: serverTimestamp(),
                createdAt: state.courseId ? course?.createdAt : serverTimestamp()
            };

            if (state.courseId) {
                // Update existing course
                await updateDoc(doc(db, 'courses', state.courseId), courseData);
                setCourse(prev => ({
                    ...prev!,
                    ...data,
                    updatedAt: new Date().toISOString()
                }));
            } else {
                // Create new course
                const courseRef = doc(collection(db, 'courses'));
                await setDoc(courseRef, courseData);
                setState(prev => ({
                    ...prev,
                    courseId: courseRef.id,
                    step: CreationStep.ModuleCreation
                }));
                setCourse({
                    ...courseData,
                    id: courseRef.id,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                } as Course);
            }

            return true;
        } catch (err) {
            setError('Failed to save course details');
            console.error('Error saving course:', err);
            return false;
        } finally {
            setLoading(false);
        }
    };

    const saveModule = async (moduleData: Partial<Module>) => {
        if (!state.courseId || !course) return false;

        try {
            setLoading(true);
            setError(null);

            const updatedModules = [...(course.modules || [])];

            if (moduleData.id) {
                // Update existing module
                const moduleIndex = updatedModules.findIndex(m => m.id === moduleData.id);
                if (moduleIndex >= 0) {
                    updatedModules[moduleIndex] = { ...updatedModules[moduleIndex], ...moduleData };
                }
            } else {
                // Add new module
                const newModule: Module = {
                    id: `module-${Date.now()}`,
                    courseId: state.courseId,
                    title: moduleData.title || 'New Module',
                    order: (course.modules?.length || 0) + 1,
                    lessons: [],
                    ...moduleData
                };
                updatedModules.push(newModule);
            }

            await updateDoc(doc(db, 'courses', state.courseId), {
                modules: updatedModules,
                updatedAt: serverTimestamp()
            });

            setCourse(prev => ({ ...prev!, modules: updatedModules }));
            return true;
        } catch (err) {
            setError('Failed to save module');
            console.error('Error saving module:', err);
            return false;
        } finally {
            setLoading(false);
        }
    };

    const saveLesson = async (moduleId: string, lessonData: Partial<Lesson>) => {
        if (!state.courseId || !course) return false;

        try {
            setLoading(true);
            setError(null);

            const updatedModules = [...course.modules];
            const moduleIndex = updatedModules.findIndex(m => m.id === moduleId);

            if (moduleIndex === -1) {
                throw new Error('Module not found');
            }

            const updatedLessons = [...(updatedModules[moduleIndex].lessons || [])];

            if (lessonData.id) {
                // Update existing lesson
                const lessonIndex = updatedLessons.findIndex(l => l.id === lessonData.id);
                if (lessonIndex >= 0) {
                    updatedLessons[lessonIndex] = { ...updatedLessons[lessonIndex], ...lessonData };
                }
            } else {
                // Add new lesson
                const newLesson: Lesson = {
                    id: `lesson-${Date.now()}`,
                    moduleId,
                    type: lessonData.type || 'text',
                    title: lessonData.title || 'New Lesson',
                    order: (updatedLessons.length || 0) + 1,
                    content: lessonData.content || '',
                    ...lessonData
                };
                updatedLessons.push(newLesson);
            }

            updatedModules[moduleIndex].lessons = updatedLessons;

            await updateDoc(doc(db, 'courses', state.courseId), {
                modules: updatedModules,
                updatedAt: serverTimestamp()
            });

            setCourse(prev => ({ ...prev!, modules: updatedModules }));
            return true;
        } catch (err) {
            setError('Failed to save lesson');
            console.error('Error saving lesson:', err);
            return false;
        } finally {
            setLoading(false);
        }
    };

    const publishCourse = async () => {
        if (!state.courseId || !course) return false;

        try {
            setLoading(true);
            setError(null);

            await updateDoc(doc(db, 'courses', state.courseId), {
                isDraft: false,
                publishedAt: serverTimestamp(),
                updatedAt: serverTimestamp()
            });

            setCourse(prev => ({ ...prev!, isDraft: false }));
            setState(prev => ({ ...prev, isDraft: false }));
            return true;
        } catch (err) {
            setError('Failed to publish course');
            console.error('Error publishing course:', err);
            return false;
        } finally {
            setLoading(false);
        }
    };

    return {
        state,
        setState,
        course,
        setCourse,
        loading,
        error,
        saveCourseDetails,
        saveModule,
        saveLesson,
        publishCourse
    };
}

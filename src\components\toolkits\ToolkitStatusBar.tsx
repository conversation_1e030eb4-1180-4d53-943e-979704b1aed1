import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  // Users, // Removed unused import
  Eye,
  Calendar,
  Share,
  Upload,
  Download,
  ExternalLink,
  // User, // Removed unused import
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Toolkit } from '@/types/tools';
import { User as UserType } from '@/types';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { publishToPortfolio, unpublishToolkitFromPortfolio } from '@/services/toolkitService';

interface ToolkitStatusBarProps {
  toolkit: Toolkit;
  onUpdate?: () => void;
}

export const ToolkitStatusBar: React.FC<ToolkitStatusBarProps> = ({
  toolkit,
  onUpdate
}) => {
  const { user } = useAuth(); // Removed unused isAdmin
  const { toast } = useToast();

  const [sharedUsers, setSharedUsers] = useState<UserType[]>([]);
  const [loadingSharedUsers, setLoadingSharedUsers] = useState(false);
  const [publishingToPortfolio, setPublishingToPortfolio] = useState(false);
  const [portfolioStatus, setPortfolioStatus] = useState<{
    isPublished: boolean;
    portfolioId?: string;
    publishedAt?: string;
  }>({
    isPublished: !!toolkit.portfolioId,
    portfolioId: toolkit.portfolioId,
    publishedAt: toolkit.published_at
  });

  const isOwner = user && (user.uid === toolkit.created_by || user.uid === toolkit.user_id);

  useEffect(() => {
    if (toolkit.shared_with && toolkit.shared_with.length > 0) {
      fetchSharedUsers();
    }
  }, [toolkit.shared_with]);

  const fetchSharedUsers = async () => {
    if (!toolkit.shared_with || toolkit.shared_with.length === 0) return;

    setLoadingSharedUsers(true);
    try {
      const users = await Promise.all(
        toolkit.shared_with.map(async (userId) => {
          const userDoc = await getDoc(doc(db, 'users', userId));
          if (userDoc.exists()) {
            return { id: userDoc.id, uid: userDoc.id, ...userDoc.data() } as UserType;
          }
          return null;
        })
      );

      setSharedUsers(users.filter(Boolean) as UserType[]);
    } catch (error) {
      console.error('Error fetching shared users:', error);
    } finally {
      setLoadingSharedUsers(false);
    }
  };

  const handlePublishToPortfolio = async () => {
    if (!user || !isOwner) return;

    setPublishingToPortfolio(true);
    try {
      if (portfolioStatus.isPublished) {
        // Unpublish from portfolio
        await unpublishToolkitFromPortfolio(toolkit.id);
        setPortfolioStatus({
          isPublished: false,
          portfolioId: undefined,
          publishedAt: undefined
        });
        toast({
          title: 'Success',
          description: 'Toolkit removed from portfolio',
        });
      } else {
        // Publish to portfolio
        await publishToPortfolio(toolkit.id, user.uid);
        setPortfolioStatus({
          isPublished: true,
          publishedAt: new Date().toISOString()
        });
        toast({
          title: 'Success',
          description: 'Toolkit published to your portfolio',
        });
      }

      if (onUpdate) {
        onUpdate();
      }
    } catch (error) {
      console.error('Error updating portfolio status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update portfolio status',
        variant: 'destructive'
      });
    } finally {
      setPublishingToPortfolio(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'private': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <Card className="w-full bg-gray-900 border-gray-800">
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold flex items-center gap-2 text-white">
              <Eye className="w-5 h-5" />
              Toolkit Status
            </h3>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(toolkit.is_published ? 'published' : 'private')}>
                {toolkit.is_published ? 'Published' : 'Private'}
              </Badge>
              {portfolioStatus.isPublished && (
                <Badge variant="outline" className="bg-purple-900 text-purple-300 border-purple-700">
                  In Portfolio
                </Badge>
              )}
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-blue-400">{toolkit.tools?.length || 0}</div>
              <div className="text-sm text-gray-300">Tools</div>
            </div>
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-green-400">{sharedUsers.length}</div>
              <div className="text-sm text-gray-300">Shared With</div>
            </div>
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-purple-400">{toolkit.tags?.length || 0}</div>
              <div className="text-sm text-gray-300">Tags</div>
            </div>
            <div className="text-center p-3 bg-gray-800 rounded-lg">
              <div className="text-2xl font-bold text-orange-400">
                {portfolioStatus.isPublished ? '1' : '0'}
              </div>
              <div className="text-sm text-gray-300">Portfolio</div>
            </div>
          </div>

          {/* Shared Users */}
          {sharedUsers.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2 text-white">
                <Share className="w-4 h-4" />
                Shared with ({sharedUsers.length})
              </h4>
              <ScrollArea className="h-20">
                <div className="flex flex-wrap gap-2">
                  {loadingSharedUsers ? (
                    <div className="text-sm text-gray-400">Loading shared users...</div>
                  ) : (
                    sharedUsers.map((sharedUser) => (
                      <div
                        key={sharedUser.id}
                        className="flex items-center gap-2 bg-gray-800 border border-gray-700 rounded-lg p-2 text-sm"
                      >
                        <Avatar className="w-6 h-6">
                          <AvatarImage src={sharedUser.avatar_url} />
                          <AvatarFallback className="bg-gray-700 text-gray-200">
                            {sharedUser.username?.charAt(0)?.toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-gray-200">{sharedUser.username || 'Unknown User'}</span>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Portfolio Status */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2 text-white">
              <Upload className="w-4 h-4" />
              Portfolio Status
            </h4>
            <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
              <div className="flex items-center gap-3">
                {portfolioStatus.isPublished ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <XCircle className="w-5 h-5 text-gray-500" />
                )}
                <div>
                  <div className="font-medium text-gray-200">
                    {portfolioStatus.isPublished ? 'Published to Portfolio' : 'Not in Portfolio'}
                  </div>
                  {portfolioStatus.publishedAt && (
                    <div className="text-sm text-gray-400 flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {formatDate(portfolioStatus.publishedAt)}
                    </div>
                  )}
                </div>
              </div>

              {isOwner && (
                <Button
                  onClick={handlePublishToPortfolio}
                  disabled={publishingToPortfolio}
                  variant={portfolioStatus.isPublished ? "outline" : "default"}
                  size="sm"
                  className={portfolioStatus.isPublished ? "border-gray-600 text-gray-300 hover:bg-gray-700" : "bg-blue-600 hover:bg-blue-700"}
                >
                  {publishingToPortfolio ? (
                    <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  ) : portfolioStatus.isPublished ? (
                    <>
                      <Download className="w-4 h-4 mr-2" />
                      Remove from Portfolio
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4 mr-2" />
                      Add to Portfolio
                    </>
                  )}
                </Button>
              )}
            </div>

            {portfolioStatus.isPublished && portfolioStatus.portfolioId && (
              <Button
                variant="outline"
                size="sm"
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
                onClick={() => window.open(`/portfolio/${user?.username}`, '_blank')}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View in Portfolio
              </Button>
            )}
          </div>

          {/* Creation Info */}
          <div className="pt-3 border-t border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>Created: {formatDate(toolkit.created_at)}</span>
              </div>
              {toolkit.updated_at && (
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>Updated: {formatDate(toolkit.updated_at)}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


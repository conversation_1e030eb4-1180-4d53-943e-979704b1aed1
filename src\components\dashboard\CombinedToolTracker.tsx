import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, Loader2, Briefcase, Layers } from 'lucide-react';
import { collection, doc, getDocs, query, where, updateDoc, deleteDoc, setDoc } from 'firebase/firestore';
import { db, storage } from '@/lib/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import type { Tool, UserTool, Toolkit } from '@/types/tools';
import type { AITool } from '@/types';
import { MissionTracker } from '@/services/missionService';
import { Button } from '@/components/ui/button';
// Removed unused Separator import
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ToolSearch } from './ToolSearch';
import { TagsFilter } from './TagsFilter';
import { ToolGrid } from './ToolGrid';
import { ClickEffect } from '@/components/ui/click-effect';
import NeonButton from '@/components/ui/NeonButton';
import { ToolkitManager } from '@/components/toolkits/ToolkitManager';
import { ToolkitPreview } from '@/components/toolkits/ToolkitPreview';
import GlassCard from '@/components/ui/GlassCard';
import { CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import NeuCard from '@/components/ui/NeuCard';
import NeonSkeleton from '@/components/ui/NeonSkeleton';
import { dedupeById } from '@/utils/dedupeById';

// Helper functions for tag and image handling
// Update the type checking for string arrays
const ensureStringArray = (tags: string | string[] | undefined | null): string[] => {
  if (!tags) return [];
  if (Array.isArray(tags)) return tags.filter(Boolean);
  if (typeof tags === 'string') {
    return tags.split(',').map((tag: string) => tag.trim()).filter(Boolean);
  }
  return [];
};


// Helper function to normalize tool properties
const normalizeToolProperties = (tool: any): Tool => {
  const tags = ensureStringArray(tool.tags);
  const baseProperties = {
    id: tool.id,
    name: tool.name,
    description: tool.description || tool.useCase || '',
    logo_url: tool.logo_url || tool.logoUrl || tool.logoLink || '',
    website: tool.website || tool.websiteLink || tool.link || '',
    use_case: tool.use_case || tool.useCase || '',
    excels_at: tool.excels_at || tool.excelsAt || '',
    pricing: tool.pricing || '',
    tags,
    created_at: tool.created_at || tool.addedAt || new Date().toISOString(),
    updated_at: tool.updated_at || new Date().toISOString(),
    user_id: tool.user_id || '',
    category: tool.category || '',
    status: tool.status || 'active',
    source: tool.source || 'user_tooltracker'
  };

  return {
    ...baseProperties,
  };
};

// Removed unused EmptyUserToolsProps interface

// Removed unused EmptyUserTools component

// CombinedToolTracker Component
export const CombinedToolTracker = () => {
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const libraryRef = useRef<HTMLDivElement>(null);

  // Common state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);

  // Search suggestions state
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Define types for search suggestions
  type SuggestionType = 'name' | 'description' | 'category' | 'tag';

  interface SearchSuggestion {
    text: string;
    type: SuggestionType;
    source: 'user' | 'library' | 'both';
    toolId?: string;
  }

  // User tools state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [toolToDelete, setToolToDelete] = useState<Tool | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  // Library tools state
  const [savingToolIds, setSavingToolIds] = useState<string[]>([]);
  const [editingTool, setEditingTool] = useState<Tool | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  // Toolkits state
  const [showToolkitManager, setShowToolkitManager] = useState(false);
  const [editingToolkit, setEditingToolkit] = useState<Toolkit | undefined>(undefined);

  // Update the toolkits query with proper loading state
  const { data: allToolkits = [], isLoading: isLoadingToolkits } = useQuery<Toolkit[]>({
    queryKey: ['toolkits', user?.uid || 'public'],
    queryFn: async () => {
      const toolkitsRef = collection(db, 'toolkits');
      const queries = [];
      // Always fetch public/admin toolkits
      queries.push(query(toolkitsRef, where('source', '==', 'library'), where('is_published', '==', true)));
      if (user?.uid) {
        // User's own toolkits
        queries.push(query(toolkitsRef, where('created_by', '==', user.uid)));
        // Toolkits shared with the user
        queries.push(query(toolkitsRef, where('shared_with', 'array-contains', user.uid)));
      }
      const snapshots = await Promise.all(queries.map(q => getDocs(q)));
      const all = snapshots.flatMap(snap => snap.docs.map(doc => ({ id: doc.id, ...doc.data() })));
      // Deduplicate by id
      const toolkitMap = new Map<string, Toolkit>();
      all.forEach(tk => toolkitMap.set(tk.id, tk as Toolkit));
      return Array.from(toolkitMap.values());
    },
    // Always enabled (so public toolkits show for logged-out users)
    enabled: true,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  // Fetch all tools for the current user (from 'tools' collection)
  const { data: userTools = [], isLoading: isLoadingUserTools, error: userToolsError } = useQuery<UserTool[]>({
    queryKey: ['userTools', user?.uid],
    queryFn: async () => {
      if (!user?.uid) return [];
      const toolsRef = collection(db, 'tools');
      const q = query(toolsRef, where('user_id', '==', user.uid));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as UserTool[];
    },
    enabled: !!user?.uid,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  // Update the library tools query with error handling
  const { data: libraryTools, isLoading: isLoadingLibrary } = useQuery<Tool[]>({
    queryKey: ['aiTools'],
    queryFn: async () => {
      try {
        const toolsCollection = collection(db, 'aiTools');
        const toolsSnapshot = await getDocs(toolsCollection);

        const toolsList: Tool[] = [];
        toolsSnapshot.forEach((doc) => {
          const aiToolData = doc.data() as AITool; // Use AITool type
          // Transform AITool to Tool interface
          const tool: Tool = {
            id: doc.id,
            name: aiToolData.name,
            description: aiToolData.description,
            logo_url: aiToolData.logoUrl, // Map logoUrl to logo_url
            logoUrl: aiToolData.logoUrl, // Keep both for compatibility
            website: aiToolData.website,
            tags: ensureStringArray(aiToolData.tags),
            category: aiToolData.category,
            created_at: aiToolData.createdAt || new Date().toISOString(),
            updated_at: aiToolData.updatedAt,
            user_id: aiToolData.createdBy,
            pricing: aiToolData.pricing,
            useCase: aiToolData.useCase,
            excelsAt: aiToolData.excelsAt,
            source: 'library',
          };

          toolsList.push(tool);
        });

        return toolsList;
      } catch (error) {
        console.error('Error fetching library tools:', error);
        throw error;
      }
    },
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  // Loading and error states
  const isLoading = isLoadingUserTools || isLoadingLibrary || isLoadingToolkits;
  const error = userToolsError;

  // Extract tags from a tool
  const extractTags = (tool: Tool): string[] => {
    if (!tool.tags) return [];
    if (Array.isArray(tool.tags)) return tool.tags;
    return ensureStringArray(tool.tags);
  };

  // Add tags from a tool to a Set
  const addToolTagsToSet = (tagsSet: Set<string>, tool: Tool) => {
    const tags = extractTags(tool);
    tags.forEach(tag => tag && tagsSet.add(tag));
  };

  // Update all unique tags from both user tools and library tools
  useEffect(() => {
    const tagsSet = new Set<string>();

    if (userTools?.length) {
      userTools.forEach(tool => addToolTagsToSet(tagsSet, tool));
    }

    if (libraryTools?.length) {
      libraryTools.forEach(tool => addToolTagsToSet(tagsSet, tool));
    }

    setAllTags(Array.from(tagsSet));
  }, [userTools, libraryTools]);

  // Generate search suggestions based on the search query
  const generateSuggestions = (query: string) => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    const queryLower = query.toLowerCase();
    const newSuggestions: SearchSuggestion[] = [];
    const addedSuggestions = new Set<string>(); // To avoid duplicates

    // Helper function to add a suggestion if it's not already added
    const addSuggestion = (text: string, type: SuggestionType, source: 'user' | 'library' | 'both', toolId?: string) => {
      const key = `${text}-${type}`;
      if (!addedSuggestions.has(key) && text.toLowerCase().includes(queryLower)) {
        newSuggestions.push({ text, type, source, toolId });
        addedSuggestions.add(key);
      }
    };

    // Add suggestions from user tools
    if (userTools?.length) {
      userTools.forEach(tool => {
        // Process tool properties and tags
        const toolTags = extractTags(tool);
        toolTags.forEach(tag => {
          tag && addSuggestion(tag, 'tag', 'user', tool.id);
        });

        // Add name suggestions
        if ((tool as any).name) {
          addSuggestion((tool as any).name, 'name', 'user', tool.id);
        }

        // Add description suggestions
        if ((tool as any).description) {
          const desc = (tool as any).description;
          // Only add if the description is not too long and contains the query
          if (desc.length < 50) {
            addSuggestion(desc, 'description', 'user', tool.id);
          }
        }

        // Add category suggestions
        if ((tool as any).category) {
          addSuggestion((tool as any).category, 'category', 'user', tool.id);
        }
      });
    }

    // Add suggestions from library tools
    if (libraryTools && libraryTools.length > 0) {
      libraryTools.forEach(tool => {
        // Add name suggestions
        addSuggestion(tool.name, 'name', 'library', tool.id);

        // Add description/useCase suggestions
        if (tool.description && tool.description.length < 50) {
          addSuggestion(tool.description, 'description', 'library', tool.id);
        }
        if (tool.useCase && tool.useCase.length < 50) {
          addSuggestion(tool.useCase, 'description', 'library', tool.id);
        }

        // Add tag suggestions
        if (tool.tags) {
          const tags = ensureStringArray(tool.tags);

          tags.forEach((tag: string) => {
            // Check if this tag also exists in user tools
            const existsInUserTools = userTools?.some(userTool =>
              userTool.tags &&
              Array.isArray(userTool.tags) &&
              userTool.tags.includes(tag)
            );

            addSuggestion(tag, 'tag', existsInUserTools ? 'both' : 'library', tool.id);
          });
        }
      });
    }

    // Limit the number of suggestions to avoid overwhelming the UI
    setSuggestions(newSuggestions.slice(0, 10));
  };

  // Update suggestions when search query changes
  useEffect(() => {
    generateSuggestions(searchQuery);
  }, [searchQuery, userTools, libraryTools]);

  // Handle clicks outside the suggestions dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showSuggestions &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSuggestions]);

  // Filter user tools
  const filteredUserTools = userTools && Array.isArray(userTools) ? userTools.filter((tool: UserTool) => {
    // Filter by search query
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      (tool as any).name?.toLowerCase().includes(searchLower) ||
      (tool as any).description?.toLowerCase().includes(searchLower) ||
      ((tool as any).tags && Array.isArray((tool as any).tags) && (tool as any).tags.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase().includes(searchLower)
      ))
    );

    // Filter by selected tags
    const matchesTags = selectedTags.length === 0 || (
      (tool as any).tags &&
      Array.isArray((tool as any).tags) &&
      selectedTags.every(tag => (tool as any).tags.includes(tag))
    );

    return matchesSearch && matchesTags;
  }) : [];

  // Filter library tools
  const filteredLibraryTools = libraryTools && Array.isArray(libraryTools) ? libraryTools.filter((tool: Tool) => {
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      tool.name.toLowerCase().includes(searchLower) ||
      (tool.description && tool.description.toLowerCase().includes(searchLower)) ||
      (tool.useCase && tool.useCase.toLowerCase().includes(searchLower)) ||
      (tool.tags && Array.isArray(tool.tags) && tool.tags.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase().includes(searchLower)
      ))
    );

    // Fix type assertion for tags
    const matchesTags = selectedTags.length === 0 || (
      tool.tags &&
      Array.isArray(tool.tags) &&
      selectedTags.every((tag: string) => tool.tags.includes(tag))
    );

    return matchesSearch && matchesTags;
  }) : [];

  // Toggle tag selection
  const toggleTag = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  // Handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: SearchSuggestion) => {
    // If it's a tag, add it to selected tags
    if (suggestion.type === 'tag') {
      if (!selectedTags.includes(suggestion.text)) {
        toggleTag(suggestion.text);
      }
      // Clear the search query if it was used to find this tag
      if (searchQuery.toLowerCase().includes(suggestion.text.toLowerCase())) {
        setSearchQuery('');
      }
    } else {
      // For other types, set the search query to the suggestion text
      setSearchQuery(suggestion.text);
    }

    // Hide suggestions after selection
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);

    // Focus back on the input
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Handle keyboard navigation for suggestions
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < suggestions.length) {
          handleSelectSuggestion(suggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
      default:
        break;
    }
  };
  // Handle deleting a user tool
  const handleDeleteClick = (tool: any) => {
    // Check if trying to delete a library tool without admin privileges
    if ((tool as any).source === 'library' && !isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can delete library tools.',
        variant: 'destructive',
      });
      return;
    }
    setToolToDelete(tool as Tool);
    setShowDeleteDialog(true);
  };

  // Confirm deletion of a user tool
  const confirmDelete = async () => {
    if (!toolToDelete) return;

    setIsDeleting(true);
    try {
      // Always delete from tools collection for user tools
      await deleteDoc(doc(db, 'tools', toolToDelete.id));

      // Invalidate and refetch the userTools query
      await queryClient.invalidateQueries({
        queryKey: ['userTools'] as const,
        exact: true,
      });

      toast({
        title: "Tool deleted",
        description: "Your tool has been removed successfully.",
      });
    } catch (error) {
      console.error('Error deleting tool:', error);
      toast({
        title: "Error",
        description: "Failed to delete the tool. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setToolToDelete(null);
    }
  };

  // Ensure all required fields are present in tool data
  const prepareToolForSave = (tool: Tool | AITool): Tool => {
    return normalizeToolProperties({
      ...tool,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      source: 'user_tooltracker',
      status: 'active'
    });
  };

  // Single function to add a tool to user's tools
  const handleAddTool = async (tool: Tool | AITool) => {
    if (!user?.uid || !tool.id) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to add tools to your tracker.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSavingToolIds(prev => [...prev, tool.id]);
      // Always set user_id to current user, and remove undefined/non-serializable fields
      const normalizedTool = {
        ...prepareToolForSave(tool),
        user_id: user.uid,
        original_tool_id: tool.id, // Store original tool id for reference
      };
      // Remove deprecated/undefined fields for Firestore compatibility
      delete (normalizedTool as any).imageSettings;
      delete (normalizedTool as any).logoUrl;
      delete (normalizedTool as any).logoLink;
      delete (normalizedTool as any).websiteLink;
      delete (normalizedTool as any).useCase;
      delete (normalizedTool as any).excelsAt;
      // Remove undefined fields in a type-safe way
      (Object.keys(normalizedTool) as Array<keyof typeof normalizedTool>).forEach(key => {
        if (normalizedTool[key] === undefined) {
          delete normalizedTool[key];
        }
      });

      // Use composite key for user tools to avoid collisions
      const compositeId = `${user.uid}_${tool.id}`;
      const toolRef = doc(db, 'tools', compositeId);
      await setDoc(toolRef, normalizedTool);

      // Track mission progress for adding tool to library
      try {
        await MissionTracker.addToolToLibrary(user.uid);
      } catch (missionError) {
        console.warn('Error tracking tool addition mission:', missionError);
        // Don't fail the tool addition if mission tracking fails
      }

      // Update local state
      queryClient.invalidateQueries({
        queryKey: ['userTools', user.uid],
        exact: true
      });

      toast({
        title: 'Tool Added',
        description: 'Tool has been added to your tracker.',
      });
    } catch (error) {
      console.error('Error adding tool:', error);
      toast({
        title: 'Error',
        description: 'Failed to add tool to tracker.',
        variant: 'destructive',
      });
    } finally {
      setSavingToolIds(prev => prev.filter(id => id !== tool.id));
    }
  };

  // Handle editing a tool (admin only)
  const handleEditTool = async (tool: Tool) => {
    if (!isAdmin) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can edit tools.',
        variant: 'destructive',
      });
      return;
    }
    setEditingTool(tool);
    setIsEditing(true);
  };

  // Handle saving edited tool
  const handleSaveEdit = async (updatedTool: Tool) => {
    try {
      // Determine collection: admin editing library tool => aiTools, else tools
      const isLibraryTool = updatedTool.source === 'library';
      const collectionName = isAdmin && isLibraryTool ? 'aiTools' : 'tools';
      const toolRef = doc(db, collectionName, updatedTool.id);
      const toolData: Partial<Tool> = {
        ...updatedTool,
        updated_at: new Date().toISOString(),
        tags: ensureStringArray(updatedTool.tags),
        category: updatedTool.category || '',
      };
      // Always set user_id if missing (for user tools)
      if (!toolData.user_id && collectionName === 'tools') {
        toolData.user_id = user?.uid;
      }
      // Remove image_settings from update
      if ('image_settings' in toolData) {
        delete toolData.image_settings;
      }
      // Try updateDoc, if fails with not-found, use setDoc
      try {
        await updateDoc(toolRef, toolData);
      } catch (err: any) {
        if (err.code === 'not-found' || (err.message && err.message.includes('No document to update'))) {
          await setDoc(toolRef, toolData, { merge: true });
        } else {
          throw err;
        }
      }
      // Update local state only for user tools
      if (collectionName === 'tools') {
        queryClient.setQueriesData({ queryKey: ['userTools', user?.uid] }, (old: any) => {
          if (!Array.isArray(old)) return old;
          return old.map((t: Tool) =>
            t.id === updatedTool.id ? { ...t, ...toolData } : t
          );
        });
      }
      setIsEditing(false);
      setEditingTool(null);
      toast({
        title: 'Tool updated',
        description: 'The tool has been updated successfully.',
      });
    } catch (error) {
      console.error('Error updating tool:', error);
      toast({
        title: 'Error',
        description: 'Failed to update the tool.',
        variant: 'destructive',
      });
    }
  };

  const handleImageUpload = async (file: File, toolId: string) => {
    try {
      setIsUploading(true);
      const logoRef = ref(storage, `tool-logos/${toolId}/${file.name}`);
      await uploadBytes(logoRef, file);
      const logo_url = await getDownloadURL(logoRef);

      if (editingTool) {
        setEditingTool(prev => ({
          ...prev!,
          logo_url,
          logoUrl: logo_url, // Ensure input updates too
        }));
        setIsEditing(true); // Open editor after upload
      }

      return logo_url;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload image.',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  // Update handleToolkitSave to fix type error with arrayUnion
  const handleToolkitSave = async (toolkit: Toolkit) => {
    try {
      if (!user?.uid) return;

      const toolkitRef = toolkit.id ?
        doc(db, 'toolkits', toolkit.id) :
        doc(collection(db, 'toolkits'));

      const toolkitData = {
        ...toolkit,
        created_by: user.uid,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      if (toolkit.id) {
        await updateDoc(toolkitRef, toolkitData);
      } else {
        await setDoc(toolkitRef, {
          ...toolkitData,
          id: toolkitRef.id,
        });
      }

      // Invalidate and refetch toolkits
      await queryClient.invalidateQueries({ queryKey: ['toolkits', user.uid] });

      setShowToolkitManager(false);
      setEditingToolkit(undefined);

      toast({
        title: toolkit.id ? 'Toolkit Updated' : 'Toolkit Created',
        description: `Your toolkit has been ${toolkit.id ? 'updated' : 'created'} successfully.`
      });
    } catch (error) {
      console.error('Error saving toolkit:', error);
      toast({
        title: 'Error',
        description: 'Failed to save toolkit. Please try again.',
        variant: 'destructive'
      });
    }
  };

  // Handle deleting a toolkit
  const handleToolkitDelete = async (toolkit: Toolkit) => {
    if (!user?.uid) return;
    try {
      await deleteDoc(doc(db, 'toolkits', toolkit.id));
      toast({
        title: 'Success',
        description: 'Toolkit deleted successfully'
      });
      // Refresh toolkits
      queryClient.invalidateQueries({ queryKey: ['toolkits', user.uid] });
    } catch (error) {
      console.error('Error deleting toolkit:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete toolkit',
        variant: 'destructive'
      });
    }
  };

  // Add handleEditToolkit function before render
  const handleEditToolkit = (toolkit: Toolkit) => {
    setEditingToolkit(toolkit);
    setShowToolkitManager(true);
  };

  // Merge allToolkits and sharedToolkits for display, deduplicating by id
  const displayedToolkits = [
    ...allToolkits,
    // ...sharedToolkits.filter(
    //   tk => !allToolkits.some(utk => utk.id === tk.id)
    // ),
  ];

  // Deduplicate filtered tools by id before rendering
  const dedupedUserTools = dedupeById(filteredUserTools);
  const dedupedLibraryTools = dedupeById(filteredLibraryTools);

  // Render
  return (
    <div className="space-y-8">
      {/* Header Section - Remove Create Toolkit button */}
      <GlassCard
        variant="glowing"
        intensity="medium"
        className="border-sortmy-blue/20 hover:border-sortmy-blue/40 transition-all duration-300"
      >
        <div className="relative p-8">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
            <div className="space-y-3">
              <h1 className="text-4xl font-bold text-white">
                Tool Tracker
              </h1>
              <p className="text-gray-300 text-lg max-w-2xl">
                Manage your AI toos and create powerful toolkits.
              </p>
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  {filteredUserTools.length} Your Tools
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  {displayedToolkits.length} Toolkits
                </span>
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                  {filteredLibraryTools.length} Library Tools
                </span>
              </div>
            </div>

            <div className="flex flex-wrap gap-3">
              {isAdmin && (
                <Link to="/dashboard/tools/library/add">
                  <Button variant="ghost" className="text-sm text-white border border-transparent hover:bg-sortmy-blue/10 hover:border-sortmy-blue/20 transition-all duration-300">
                    <PlusCircle className="w-4 h-4 mr-2" />
                    Add to Library
                  </Button>
                </Link>
              )}

              <Link to="/dashboard/tools/add">
                <Button variant="outline" className="text-sm text-white border-sortmy-blue/30 hover:bg-sortmy-blue/10 hover:border-sortmy-blue/50 transition-all duration-300">
                  <PlusCircle className="w-4 h-4 mr-2" />
                  Add your own Tool
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Search & Filter Section - Update to NeuCard */}
      <NeuCard
        variant="elevated"
        color="dark"
        className="border border-sortmy-blue/20"
      >
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <div className="w-1 h-6 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full mr-2"></div>
            <span className="text-white">Search & Filter</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <ToolSearch
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              suggestions={suggestions}
              showSuggestions={showSuggestions}
              setShowSuggestions={setShowSuggestions}
              handleSelectSuggestion={handleSelectSuggestion}
              selectedSuggestionIndex={selectedSuggestionIndex}
              handleKeyDown={handleKeyDown}
            />

            <TagsFilter
              allTags={allTags}
              selectedTags={selectedTags}
              toggleTag={toggleTag}
              clearTags={() => setSelectedTags([])}
            />
          </div>
        </CardContent>
      </NeuCard>

      {/* User's Tools Section */}
      {filteredUserTools.length === 0 ? (
        <GlassCard
          variant="glowing"
          intensity="medium"
          className="border-sortmy-blue/20 p-8 text-center"
        >
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Briefcase className="w-8 h-8 text-gray-400" />
          </div>
          <h4 className="text-lg font-semibold text-white mb-2">No tools yet</h4>
          <p className="text-gray-400 mb-6 max-w-md mx-auto">
            Start building your AI toolkit by adding tools from our library or creating your own
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <ClickEffect effect="ripple" color="blue">
              <NeonButton
                variant="gradient" // Changed from "blue" to "gradient"
                onClick={() => {
                  const librarySection = document.getElementById('library-section');
                  librarySection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                <PlusCircle className="w-4 h-4 mr-2" />
                Browse Library
              </NeonButton>
            </ClickEffect>
            <ClickEffect effect="ripple" color="cyan">
              <Link to="/dashboard/tools/add">
                <NeonButton variant="cyan">
                  <PlusCircle className="w-4 h-4 mr-2" />
                  Add Custom Tool
                </NeonButton>
              </Link>
            </ClickEffect>
          </div>
        </GlassCard>
      ) : (
        <NeuCard
          variant="flat"
          color="dark"
          className="border border-sortmy-blue/20"
        >
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Briefcase className="w-5 h-5 mr-2 text-sortmy-blue" />
              <span className="text-white">Your Tools</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ToolGrid
              tools={dedupedUserTools}
              isLoading={isLoading}
              error={error}
              onDelete={handleDeleteClick}
              emptyMessage="You haven't added any tools yet"
            />
          </CardContent>
        </NeuCard>
      )}



      {/* Enhanced Toolkits Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">            <div className="w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center">
            <Layers className="w-4 h-4 text-white" />
          </div>
            <div>
              <h3 className="text-2xl font-bold text-white">Toolkits</h3>
              <p className="text-gray-400">Curated collections of tools for specific workflows</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-400 bg-gray-800/50 px-3 py-1 rounded-full">
              {displayedToolkits.length} toolkits
            </div>
            <ClickEffect effect="ripple" color="blue">
              <NeonButton
                variant="gradient" // Changed from "blue" to "gradient"
                onClick={() => setShowToolkitManager(true)}
                disabled={isLoadingToolkits}
                className="shadow-lg hover:shadow-blue-500/25"
              >
                {isLoadingToolkits ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <PlusCircle className="w-4 h-4 mr-2" />
                    Create Toolkit
                  </>
                )}
              </NeonButton>
            </ClickEffect>
          </div>
        </div>

        {isLoadingToolkits ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <NeonSkeleton
                key={`toolkit-skeleton-${i}`}
                className="h-64"
              />
            ))}
          </div>
        ) : displayedToolkits.length === 0 ? (
          <GlassCard
            variant="glowing"
            intensity="medium"
            className="border-sortmy-blue/20 p-8 text-center"
          >
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-500/20 to-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Layers className="w-8 h-8 text-gray-400" />
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">No toolkits yet</h4>
            <p className="text-gray-400 mb-6 max-w-md mx-auto">
              Create your first toolkit to organize tools by workflow, project, or purpose
            </p>
            <ClickEffect effect="ripple" color="blue">
              <NeonButton
                variant="gradient" // Changed from "blue" to "gradient"
                onClick={() => setShowToolkitManager(true)}
                disabled={isLoadingToolkits}
              >
                <PlusCircle className="w-4 h-4 mr-2" />
                Create Your First Toolkit
              </NeonButton>
            </ClickEffect>
          </GlassCard>
        ) : (
          <NeuCard
            variant="elevated"
            color="dark"
            className="border border-sortmy-blue/20"
          >
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Layers className="w-5 h-5 mr-2 text-sortmy-blue" />
                <span className="text-white">Your Toolkits</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ToolkitGrid
                toolkits={displayedToolkits}
                tools={[...filteredUserTools, ...filteredLibraryTools]}
                onEdit={handleEditToolkit}
                onDelete={handleToolkitDelete}
              />
            </CardContent>
          </NeuCard>
        )}
      </div>

      {/* Enhanced Separator */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>
        </div>          <div className="relative flex justify-center text-sm">
          <span className="bg-indigo-900/50 px-4 text-indigo-400 rounded-full">Discover New Tools</span>
        </div>
      </div>

      {/* Enhanced AI Tools Library Section */}
      <div ref={libraryRef} id="library-section" className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">            <div className="w-8 h-8 bg-gradient-to-br from-indigo-400 to-blue-500 rounded-lg flex items-center justify-center">
            <PlusCircle className="w-4 h-4 text-white" />
          </div>
            <div>
              <h3 className="text-2xl font-bold text-white">AI Tools Library</h3>
              <p className="text-gray-400">Discover and add powerful AI tools to your collection</p>
            </div>
          </div>
          <div className="text-sm text-gray-400 bg-gray-800/50 px-3 py-1 rounded-full">
            {filteredLibraryTools.length} available
          </div>
        </div>

        <NeuCard
          variant="flat"
          color="dark"
          className="border border-sortmy-blue/20"
        >
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <PlusCircle className="w-5 h-5 mr-2 text-sortmy-blue" />
              <span className="text-white">AI Tools Library</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ToolGrid
              tools={dedupedLibraryTools}
              onAdd={handleAddTool}
              isLoadingTools={isLoadingLibrary}
              savingToolIds={savingToolIds}
              isAdmin={isAdmin}
              onEdit={handleEditTool}
            />
          </CardContent>
        </NeuCard>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md">
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tool?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{(toolToDelete as any)?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <button
              className="px-4 py-2 rounded-md bg-transparent border border-[#01AAE9]/20 text-white hover:bg-[#01AAE9]/10 transition-colors"
              disabled={isDeleting}
              onClick={() => setShowDeleteDialog(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 rounded-md bg-red-500/80 text-white hover:bg-red-500 transition-colors"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 inline animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit Tool Dialog */}
      <Dialog open={isEditing} onOpenChange={(open) => !open && setIsEditing(false)}>
        <DialogContent className="bg-sortmy-dark border-[#01AAE9]/20 backdrop-blur-md max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Tool</DialogTitle>
            <DialogDescription>
              Update the tool details below.
            </DialogDescription>
          </DialogHeader>

          {editingTool && (
            <div className="space-y-6 py-4">
              {/* Name and Description */}
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={editingTool.name}
                    onChange={(e) => {
                      setEditingTool({ ...editingTool, name: e.target.value });
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={editingTool.description || ''}
                    onChange={(e) => {
                      setEditingTool({ ...editingTool, description: e.target.value });
                    }}
                    className="min-h-[100px]"
                  />
                </div>
              </div>

              {/* Image Section */}
              <div className="space-y-4">
                <Label>Tool Logo</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Image Preview */}
                  <div className="space-y-4">
                    <div className="relative w-40 h-40 mx-auto rounded-2xl overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
                      {editingTool.logoUrl ? (
                        <img
                          src={editingTool.logo_url || editingTool.logoUrl || ''}
                          alt={editingTool.name + ' logo'}
                          className="w-24 h-24 object-contain rounded-lg border border-sortmy-blue/30 bg-sortmy-dark/40 mx-auto"
                        />
                      ) : null}
                    </div>

                    {/* Upload Controls */}
                    <div className="flex flex-col gap-2">
                      <Label
                        htmlFor="logo-upload"
                        className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-[#01AAE9]/40 rounded-xl cursor-pointer hover:border-[#01AAE9]/60 transition-colors"
                      >
                        <input
                          id="logo-upload"
                          type="file"
                          className="hidden"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file && editingTool) {
                              handleImageUpload(file, editingTool.id);
                            }
                          }}
                          disabled={isUploading}
                        />
                        <div className="flex flex-col items-center gap-2 text-gray-400">
                          {isUploading ? (
                            <>
                              <Loader2 className="w-6 h-6 animate-spin" />
                              <span>Uploading...</span>
                            </>
                          ) : (
                            <>
                              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <span>Click to upload image</span>
                              <span className="text-xs text-gray-500">SVG, PNG, JPG or GIF (max. 5MB)</span>
                            </>
                          )}
                        </div>
                      </Label>

                      <div className="relative">
                        <Input
                          value={editingTool.logoUrl || ''}
                          onChange={(e) => {
                            setEditingTool({ ...editingTool, logoUrl: e.target.value });
                          }}
                          placeholder="Or enter image URL..."
                          className="pr-20"
                        />
                        {editingTool.logoUrl && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-1 top-1 h-7"
                            onClick={() => setEditingTool({ ...editingTool, logoUrl: '' })}
                          >
                            Clear
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={Array.isArray(editingTool.tags) ? editingTool.tags.join(', ') : editingTool.tags || ''}
                  onChange={(e) => {
                    const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
                    setEditingTool({ ...editingTool, tags });
                  }}
                  placeholder="Enter tags separated by commas"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => editingTool && handleSaveEdit(editingTool)}
              disabled={!editingTool || isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Toolkit Manager Dialog */}
      <ToolkitManager
        isOpen={showToolkitManager}
        onClose={() => {
          setShowToolkitManager(false);
          setEditingToolkit(undefined);
        }}
        availableTools={[...dedupedUserTools, ...dedupedLibraryTools]}
        onSave={handleToolkitSave}
        editingToolkit={editingToolkit}
      />
    </div>
  );
};

export default CombinedToolTracker;

// Toolkit Grid Component
interface ToolkitGridProps {
  toolkits: Toolkit[];
  tools: Tool[]; // Add this
  onEdit?: (toolkit: Toolkit) => void;
  onDelete?: (toolkit: Toolkit) => void;
}

function ToolkitGrid({ toolkits, tools, onEdit, onDelete }: ToolkitGridProps) {
  if (!toolkits?.length) {
    return (
      <div className="text-center p-6 bg-sortmy-darker border border-sortmy-blue/20 rounded-lg">
        <p className="text-gray-400">No toolkits found</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
      {toolkits.map((toolkit) => (
        <Link
          key={`toolkit-${toolkit.id}-${toolkit.created_at}`}
          to={`/dashboard/toolkits/${toolkit.id}`}
          className="block"
        >
          <ToolkitPreview
            toolkit={toolkit}
            tools={tools}
            onEdit={() => onEdit?.(toolkit)}
            onDelete={() => onDelete?.(toolkit)}
            isOwner={true}
          />
        </Link>
      ))}
    </div>
  );
}

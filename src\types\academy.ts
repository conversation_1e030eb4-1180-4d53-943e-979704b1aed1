// Types of content that can be in a lesson
export type ContentType = 'text' | 'video' | 'image' | 'quiz' | 'code' | 'embed';

// A single content block within a lesson
export interface ContentBlock {
  id: string;
  type: ContentType;
  order: number;
  content: string; // For text, this is the actual content; for media, this could be a URL
  caption?: string;
  options?: string[]; // For quiz questions
  correctOption?: number; // For quiz questions
  embedType?: 'youtube' | 'vimeo' | 'other'; // For embed content
  codeLanguage?: string; // For code blocks
}

// A single lesson within a module
export interface Lesson {
  id: string;
  title: string;
  description?: string;
  order: number;
  duration?: number; // In minutes
  isCompleted: boolean;
  contentBlocks: ContentBlock[];
}

export interface Module {
  id: string;
  title: string;
  description?: string;
  xpReward: number;
  isCompleted: boolean;
  videoId?: string;
  resourceUrl?: string;
  imageUrl?: string;
  lessons?: Lesson[]; // New field for structured lessons
  // Additional fields for AI tools
  excelsAt?: string;
  freeCredits?: string;
  category?: string;
  createdAt?: string;
  updatedAt?: string;
  authorId?: string;
  authorName?: string;
  estimatedDuration?: number; // In minutes
}

export interface Tier {
  id: string;
  name: string;
  isUnlocked: boolean;
  modules: Module[];
  description?: string;
  order?: number;
  requiredXp?: number;
}

// Quiz attempt tracking
export interface QuizAttempt {
  userId: string;
  lessonId: string;
  contentBlockId: string;
  selectedOption: number;
  isCorrect: boolean;
  timestamp: string;
}

// Progress tracking
export interface UserProgress {
  userId: string;
  courseId: string;
  status: 'not-started' | 'in-progress' | 'completed';  // Fix the enum values
  completedLessons: { [lessonId: string]: boolean };
  completedModules: { [moduleId: string]: boolean };
  quizScores: { [quizId: string]: number };
  earnedXp: number;
  quizAttempts: QuizAttempt[];
  lastAccessedModuleId?: string;
  lastAccessedLessonId?: string;
  currentLesson: {  // Remove optional since it's required
    moduleId: string;
    lessonId: string;
  };
  updatedAt: string;
  lastAccessedAt: string;
}
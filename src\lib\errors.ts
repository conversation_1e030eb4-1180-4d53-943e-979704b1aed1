// Base error class for application errors
export class AppError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AppError';
    }
}

// Network-related errors
export class NetworkError extends AppError {
    constructor(message: string) {
        super(message);
        this.name = 'NetworkError';
    }
}

// Authentication errors
export class AuthError extends AppError {
    constructor(message: string) {
        super(message);
        this.name = 'AuthError';
    }
}

// Data validation errors
export class ValidationError extends AppError {
    constructor(message: string) {
        super(message);
        this.name = 'ValidationError';
    }
}

// Permission errors
export class PermissionError extends AppError {
    constructor(message: string) {
        super(message);
        this.name = 'PermissionError';
    }
}

// Generic operation errors
export class OperationError extends AppError {
    constructor(message: string) {
        super(message);
        this.name = 'OperationError';
    }
}

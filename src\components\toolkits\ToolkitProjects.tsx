import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  FolderOpen,
  ExternalLink,
  Calendar,
  Eye,
  Heart,
  MessageCircle,
  // User, // Removed unused import
  Loader2,
  Image as ImageIcon,
  Video,
  FileText
} from 'lucide-react';
import { PortfolioItem } from '@/types';
import { getProjectsUsingToolkit } from '@/services/toolkitService';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface ToolkitProjectsProps {
  toolkitId: string;
}

export const ToolkitProjects: React.FC<ToolkitProjectsProps> = ({
  toolkitId
}) => {
  const [projects, setProjects] = useState<PortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProjects();
  }, [toolkitId]);

  const fetchProjects = async () => {
    setLoading(true);
    setError(null);

    try {
      const projectsData = await getProjectsUsingToolkit(toolkitId);

      // Enrich projects with author information
      const enrichedProjects = await Promise.all(
        projectsData.map(async (project) => {
          try {
            const userDoc = await getDoc(doc(db, 'users', project.userId));
            if (userDoc.exists()) {
              const userData = userDoc.data();
              return {
                ...project,
                authorName: userData.username || 'Unknown User',
                authorAvatar: userData.avatar_url
              } as PortfolioItem;
            }
            return project as PortfolioItem;
          } catch (error) {
            console.error('Error fetching user data:', error);
            return project as PortfolioItem;
          }
        })
      );

      setProjects(enrichedProjects);
    } catch (error) {
      console.error('Error fetching projects:', error);
      setError('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getMediaIcon = (mediaType: string) => {
    switch (mediaType) {
      case 'video': return <Video className="w-4 h-4" />;
      case 'image': return <ImageIcon className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const handleViewProject = (project: PortfolioItem) => {
    // Navigate to project or open in new tab
    if (project.authorName) {
      window.open(`/portfolio/${project.authorName}`, '_blank');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="w-5 h-5" />
            Projects Using This Toolkit
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="w-5 h-5" />
            Projects Using This Toolkit
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-500">
            <p>{error}</p>
            <Button
              onClick={fetchProjects}
              variant="outline"
              size="sm"
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderOpen className="w-5 h-5" />
          Projects Using This Toolkit ({projects.length})
        </CardTitle>
        {projects.length > 0 && (
          <p className="text-sm text-gray-600">
            This toolkit has been used in {projects.length} project{projects.length !== 1 ? 's' : ''}
          </p>
        )}
      </CardHeader>
      <CardContent>
        {projects.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FolderOpen className="w-12 h-12 mx-auto mb-3 opacity-30" />
            <p className="font-medium mb-1">No projects yet</p>
            <p className="text-sm">This toolkit hasn't been used in any projects</p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {projects.map((project) => (
                <div
                  key={project.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-lg">{project.title}</h4>
                        <Badge className={getStatusColor(project.status)}>
                          {project.status}
                        </Badge>
                        {!project.is_public && (
                          <Badge variant="outline">Private</Badge>
                        )}
                      </div>

                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                        {project.description}
                      </p>

                      {/* Author Info */}
                      {project.authorName && (
                        <div className="flex items-center gap-2 mb-2">
                          <Avatar className="w-5 h-5">
                            <AvatarImage src={project.authorAvatar} />
                            <AvatarFallback>
                              {project.authorName.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-600">
                            by {project.authorName}
                          </span>
                        </div>
                      )}

                      {/* Project Stats */}
                      <div className="flex items-center gap-4 text-xs text-gray-500 mb-2">
                        <span className="flex items-center gap-1">
                          {getMediaIcon(project.media_type)}
                          {project.content_type}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {formatDate(project.created_at)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {project.views || 0}
                        </span>
                        <span className="flex items-center gap-1">
                          <Heart className="w-3 h-3" />
                          {project.likes || 0}
                        </span>
                        {project.comments && (
                          <span className="flex items-center gap-1">
                            <MessageCircle className="w-3 h-3" />
                            {project.comments}
                          </span>
                        )}
                      </div>

                      {/* Tools Used */}
                      {project.tools_used && project.tools_used.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {project.tools_used.slice(0, 3).map((tool, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tool}
                            </Badge>
                          ))}
                          {project.tools_used.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{project.tools_used.length - 3} more
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Action Button */}
                    <Button
                      onClick={() => handleViewProject(project)}
                      variant="outline"
                      size="sm"
                      className="ml-4"
                    >
                      <ExternalLink className="w-4 h-4 mr-1" />
                      View
                    </Button>
                  </div>

                  {/* Project URL */}
                  {project.project_url && (
                    <div className="pt-2 border-t border-gray-100">
                      <a
                        href={project.project_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                      >
                        <ExternalLink className="w-3 h-3" />
                        Project Link
                      </a>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};


import { useState, KeyboardEvent } from 'react';
import { Input } from './input';
import { Badge } from './badge';
import { X } from 'lucide-react';

interface TagInputProps {
    id?: string;
    tags: string[];
    onChange: (tags: string[]) => void;
    className?: string;
}

export function TagInput({ id, tags, onChange, className }: TagInputProps) {
    const [inputValue, setInputValue] = useState('');

    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
        if ((e.key === 'Enter' || e.key === ',') && inputValue.trim()) {
            e.preventDefault();
            const newTag = inputValue.trim();
            if (!tags.includes(newTag)) {
                onChange([...tags, newTag]);
            }
            setInputValue('');
        }
    };

    const removeTag = (tagToRemove: string) => {
        onChange(tags.filter(tag => tag !== tagToRemove));
    };

    return (
        <div className={className}>
            <div className="flex flex-wrap gap-2 mb-2">
                {tags.map(tag => (
                    <Badge
                        key={tag}
                        variant="secondary"
                        className="flex items-center gap-1"
                    >
                        {tag}
                        <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeTag(tag)}
                        />
                    </Badge>
                ))}
            </div>
            <Input
                id={id}
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type and press Enter to add tags"
            />
        </div>
    );
}


import { Tag, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { TagsFilterProps } from './types';

export const TagsFilter = ({
    allTags,
    selectedTags,
    toggleTag,
    clearTags
}: TagsFilterProps) => {
    return (
        <div className="flex flex-wrap gap-2">
            <div className="flex items-center mr-2">
                <Tag className="h-4 w-4 text-[#01AAE9] mr-1" />
                <span className="text-sm font-medium text-white">Tags:</span>
            </div>
            {allTags.slice(0, 8).map(tag => (
                <Badge
                    key={tag}
                    variant={selectedTags.includes(tag) ? "default" : "outline"}
                    className={`cursor-pointer ${selectedTags.includes(tag)
                        ? "bg-[#01AAE9] text-white hover:bg-[#01AAE9]/80"
                        : "hover:bg-[#01AAE9]/10 border-[#01AAE9]/20"
                        }`}
                    onClick={() => toggleTag(tag)}
                >
                    {tag}
                </Badge>
            ))}
            {selectedTags.length > 0 && (
                <Badge
                    variant="outline"
                    className="cursor-pointer hover:bg-red-500/10 border-red-500/20 text-red-400"
                    onClick={clearTags}
                >
                    Clear Filters
                    <X className="ml-1 h-3 w-3" />
                </Badge>
            )}
        </div>
    );
};


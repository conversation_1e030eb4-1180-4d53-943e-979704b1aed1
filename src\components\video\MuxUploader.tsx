import { useState, useRef } from 'react';
import { uploadToMux } from '@/lib/mux';

interface MuxUploaderProps {
    onUploadComplete?: (result: { assetId: string; playbackId: string }) => void;
    onUploadError?: (error: string) => void;
    onUploadProgress?: (progress: number) => void;
    className?: string;
    accept?: string;
    maxSizeGB?: number;
}

export function MuxUploader({
    onUploadComplete,
    onUploadError,
    onUploadProgress,
    className = '',
    accept = 'video/*',
    maxSizeGB = 5,
}: MuxUploaderProps) {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [uploading, setUploading] = useState(false);
    const [progress, setProgress] = useState(0);
    const [dragOver, setDragOver] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const maxSizeBytes = maxSizeGB * 1024 * 1024 * 1024;

    const validateFile = (file: File): string | null => {
        if (!file.type.startsWith('video/')) {
            return 'Please select a video file';
        }

        if (file.size > maxSizeBytes) {
            return `File size must be less than ${maxSizeGB}GB`;
        }

        return null;
    };

    const handleFileSelect = (file: File) => {
        const error = validateFile(file);
        if (error) {
            onUploadError?.(error);
            return;
        }

        setSelectedFile(file);
    };

    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            handleFileSelect(file);
        }
    };

    const handleDrop = (event: React.DragEvent) => {
        event.preventDefault();
        setDragOver(false);

        const file = event.dataTransfer.files[0];
        if (file) {
            handleFileSelect(file);
        }
    };

    const handleDragOver = (event: React.DragEvent) => {
        event.preventDefault();
        setDragOver(true);
    };

    const handleDragLeave = (event: React.DragEvent) => {
        event.preventDefault();
        setDragOver(false);
    };

    const handleUpload = async () => {
        if (!selectedFile) return;

        setUploading(true);
        setProgress(0);

        try {
            const result = await uploadToMux(selectedFile, (progress) => {
                setProgress(progress);
                onUploadProgress?.(progress);
            });

            if (result) {
                onUploadComplete?.(result);
                setSelectedFile(null);
                setProgress(0);
            } else {
                throw new Error('Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            onUploadError?.(error instanceof Error ? error.message : 'Upload failed');
        } finally {
            setUploading(false);
        }
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Removed unused formatDuration function

    return (
        <div className={`space-y-4 ${className}`}>
            {/* File Drop Zone */}
            <div
                className={`
                    border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
                    ${dragOver
                        ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                    }
                    ${uploading ? 'pointer-events-none opacity-50' : ''}
                `}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onClick={() => fileInputRef.current?.click()}
            >
                <input
                    ref={fileInputRef}
                    type="file"
                    accept={accept}
                    onChange={handleFileInputChange}
                    className="hidden"
                    disabled={uploading}
                />

                <div className="space-y-2">
                    <div className="text-4xl">🎬</div>
                    <div className="text-lg font-medium text-gray-900 dark:text-white">
                        {selectedFile ? selectedFile.name : 'Drop your video here'}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedFile
                            ? `${formatFileSize(selectedFile.size)} • Click to change`
                            : `or click to browse • Max ${maxSizeGB}GB`
                        }
                    </div>
                </div>
            </div>

            {/* Upload Progress */}
            {uploading && (
                <div className="space-y-2">
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                        <span>Uploading to Mux...</span>
                        <span>{Math.round(progress)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${progress}%` }}
                        />
                    </div>
                </div>
            )}

            {/* Upload Button */}
            {selectedFile && !uploading && (
                <div className="flex justify-center">
                    <button
                        onClick={handleUpload}
                        className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                    >
                        Upload to Mux
                    </button>
                </div>
            )}

            {/* File Info */}
            {selectedFile && (
                <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                    <div>File: {selectedFile.name}</div>
                    <div>Size: {formatFileSize(selectedFile.size)}</div>
                    <div>Type: {selectedFile.type}</div>
                </div>
            )}
        </div>
    );
}


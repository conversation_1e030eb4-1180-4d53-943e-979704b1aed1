import React from 'react';
import { Button } from '@/components/ui/button';
import { GlassCard } from '@/components/ui/glass-card';

interface AISuggestionProps {
    suggestion: string;
    actionText: string;
    onAction: () => void;
}

export const AISuggestion: React.FC<AISuggestionProps> = ({
    suggestion,
    actionText,
    onAction,
}) => {
    return (
        <GlassCard className="p-6">
            <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                    <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            className="w-6 h-6 text-blue-500"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0012 18.469c-1.006 0-1.94-.45-2.567-1.175l-.547-.547z"
                            />
                        </svg>
                    </div>
                </div>
                <div className="flex-1">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                        {suggestion}
                    </p>
                    <Button
                        onClick={onAction}
                        variant="ghost"
                        className="mt-2 text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                        {actionText} →
                    </Button>
                </div>
            </div>
        </GlassCard>
    );
};

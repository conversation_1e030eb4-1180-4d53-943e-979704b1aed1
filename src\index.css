@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 215 30% 10%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 196 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217 33% 18%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217 33% 18%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 212.7 26.8% 83.9%;

    --radius: 0.5rem;

    --sortmy-dark: 11 17 33;
    --sortmy-darker: 7 12 23;
    --sortmy-gray: 30 41 59;
    --sortmy-blue: 14 150 213;
  }

  .dark {
    --background: 11 17 33;
    --foreground: 241 245 249;
    --border: 30 41 59;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-sortmy-dark text-foreground antialiased;
    /* Background image moved to a class so it can be toggled */
  }

  .default-bg {
    background-image:
      radial-gradient(circle at 40% 20%, rgba(14, 150, 213, 0.05) 0%, transparent 35%),
      radial-gradient(circle at 80% 70%, rgba(14, 150, 213, 0.03) 0%, transparent 25%);
  }
}

/* Toast Animations */
@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.sonner-toast {
  animation: slide-in 0.2s ease-out;
}

.sonner-toast[data-removing="true"] {
  animation: slide-out 0.2s ease-in forwards;
}

@layer components {
  .gradient-text {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-[#0E96D5] to-[#0E96D5];
  }

  .card-glow {
    @apply hover:shadow-[0_0_20px_rgba(14,150,213,0.3)] transition-shadow duration-300;
  }
}

@layer utilities {
  .neural-bg {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230E96D5' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
}

/* Public Profile View */
.public-profile-container {
  min-height: 100vh;
  background-color: var(--sortmy-dark);
}

.public-profile-view .dashboard-sidebar {
  display: none;
}

.public-profile-view .dashboard-content {
  width: 100%;
  padding-left: 0;
}

body.public-profile-view {
  overflow-x: hidden;
}

/* Hide dashboard layout elements in public profile view */
.public-profile-view .dashboard-layout {
  display: none;
}

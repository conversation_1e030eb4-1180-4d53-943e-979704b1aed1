import { MissionService, MissionTracker } from '@/services/missionService';

// Debug utility to help troubleshoot mission issues
export const debugMissions = {
  // Test adding a tool to library
  async testAddTool(userId: string) {
    console.log('🔧 Testing tool addition for user:', userId);
    try {
      await MissionTracker.addToolToLibrary(userId);
      console.log('✅ Tool addition tracked successfully');
    } catch (error) {
      console.error('❌ Error tracking tool addition:', error);
    }
  },

  // Test daily login
  async testDailyLogin(userId: string) {
    console.log('🔑 Testing daily login for user:', userId);
    try {
      await MissionTracker.dailyLogin(userId);
      console.log('✅ Daily login tracked successfully');
    } catch (error) {
      console.error('❌ Error tracking daily login:', error);
    }
  },

  // Test portfolio creation
  async testPortfolioCreation(userId: string) {
    console.log('📁 Testing portfolio creation for user:', userId);
    try {
      await MissionTracker.createFirstPortfolio(userId);
      console.log('✅ Portfolio creation tracked successfully');
    } catch (error) {
      console.error('❌ Error tracking portfolio creation:', error);
    }
  },

  // Get user missions with detailed logging
  async getUserMissionsDebug(userId: string) {
    console.log('📋 Getting missions for user:', userId);
    try {
      const missions = await MissionService.getUserMissions(userId);
      console.log('✅ Retrieved missions:', missions);
      
      // Log each mission's details
      missions.forEach(mission => {
        console.log(`Mission: ${mission.title} (${mission.id})`);
        console.log(`  Progress: ${mission.progress}/${mission.maxProgress}`);
        console.log(`  Completed: ${mission.isCompleted}`);
        console.log(`  Claimed: ${mission.isClaimed}`);
        console.log(`  Activity Type: ${mission.activityType}`);
        console.log(`  Expires: ${mission.expiresAt}`);
        console.log('---');
      });
      
      return missions;
    } catch (error) {
      console.error('❌ Error getting missions:', error);
      return [];
    }
  },

  // Force refresh missions
  async forceRefreshMissions(userId: string) {
    console.log('🔄 Force refreshing missions for user:', userId);
    try {
      // Clear any cached data
      localStorage.removeItem(`missions_${userId}`);
      
      // Get fresh missions
      const missions = await this.getUserMissionsDebug(userId);
      console.log('✅ Missions refreshed');
      return missions;
    } catch (error) {
      console.error('❌ Error refreshing missions:', error);
      return [];
    }
  },

  // Test all mission types
  async testAllMissions(userId: string) {
    console.log('🧪 Testing all mission types for user:', userId);
    
    await this.testDailyLogin(userId);
    await this.testAddTool(userId);
    await this.testPortfolioCreation(userId);
    
    // Wait a bit then check missions
    setTimeout(async () => {
      await this.getUserMissionsDebug(userId);
    }, 1000);
  }
};

// Make it available globally for debugging
(window as any).debugMissions = debugMissions;

console.log('🐛 Mission debug utilities loaded. Use window.debugMissions in console.');

---
applyTo: '**'
---

# Coding Standards
- Use TypeScript whenever possible with `strict: true`.
- Prefer functional programming patterns: pure functions, immutability, composition.
- No `any`, no implicit `any`, no `@ts-ignore`.
- Destructure objects and arrays cleanly.
- Follow Airbnb Style Guide unless project uses a different linter config.
- Format all code using <PERSON><PERSON><PERSON> with default settings.
- Use `async/await` over promises and `.then()`.

# Testing
- Always write unit tests for new functions using `Vitest` (or `Jest` if specified).
- Use test-first or test-driven structure when generating new modules.
- Include edge cases and error conditions in tests.

# Error Handling
- Never throw raw errors; use a consistent error class pattern (e.g. `AppError`).
- All async functions must handle potential exceptions using `try/catch` or error boundaries.

# API & Data
- When writing API calls, validate all inputs and sanitize outputs.
- Use Axios with interceptors or fetch with custom wrappers.
- Follow REST or GraphQL conventions based on the file’s context.
- Use DTOs for request/response if working in typed environments.

# Architecture & Patterns
- Follow the existing folder structure and naming conventions.
- Use dependency injection where relevant.
- Respect domain-driven design (DDD) if present in the codebase.
- Use hooks cleanly in React (no side effects in render, memoize handlers, etc).

# Domain Knowledge (Adjust to your use case)
- This app manages personal knowledge, tasks, and thought organization.
- Do not suggest social media integration, analytics, or unrelated CRUD scaffolding.
- Suggest features that improve personal productivity, context-awareness, and searchability.
- Use examples relevant to productivity tools (e.g. tagging, search, filters, temporal queries).

# Language Use
- Write clear, concise, and correct code comments. Avoid verbose or obvious comments.
- Explain “why”, not just “what”, when adding inline documentation.

# Preferences
- Keep third-party dependencies minimal. Avoid overengineering.
- Use open standards and portable code over framework-specific hacks.
- Prefer composition over inheritance.
- Avoid magic numbers, deep nesting, and large functions.

# Misc
- When unsure, ask clarifying questions or insert a TODO comment instead of guessing.
- No filler, placeholders, or pseudocode unless explicitly asked for.


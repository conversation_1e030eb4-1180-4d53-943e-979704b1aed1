import { defineConfig, UserConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { componentTagger } from 'lovable-tagger'

const config: UserConfig = {
  plugins: [
    react(),
    componentTagger()
  ],
  publicDir: 'public',
  define: {
    'process.env': process.env
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    port: 8080,
    headers: {
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'same-site',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Origin',
      'Access-Control-Allow-Credentials': 'true'
    },
    proxy: {
      '/google-drive': {
        target: 'https://drive.google.com',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/google-drive/, '')
      },
      '/firestore.googleapis.com': {
        target: 'https://firestore.googleapis.com',
        changeOrigin: true,
        secure: true,
        ws: true
      }
    },
    cors: true
  },
  optimizeDeps: {
    exclude: ['@firebase/app', '@firebase/firestore']
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          firebase: ['firebase/app', 'firebase/firestore', 'firebase/auth', 'firebase/analytics']
        }
      }
    }
  }
}

export default defineConfig(({ mode }) => {
  if (mode !== 'development') {
    config.plugins = config.plugins?.filter(p => p !== componentTagger())
  }
  return config
})

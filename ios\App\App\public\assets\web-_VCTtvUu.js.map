{"version": 3, "file": "web-_VCTtvUu.js", "sources": ["../../node_modules/@capacitor/network/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nfunction translatedConnection() {\n    const connection = window.navigator.connection ||\n        window.navigator.mozConnection ||\n        window.navigator.webkitConnection;\n    let result = 'unknown';\n    const type = connection ? connection.type || connection.effectiveType : null;\n    if (type && typeof type === 'string') {\n        switch (type) {\n            // possible type values\n            case 'bluetooth':\n            case 'cellular':\n                result = 'cellular';\n                break;\n            case 'none':\n                result = 'none';\n                break;\n            case 'ethernet':\n            case 'wifi':\n            case 'wimax':\n                result = 'wifi';\n                break;\n            case 'other':\n            case 'unknown':\n                result = 'unknown';\n                break;\n            // possible effectiveType values\n            case 'slow-2g':\n            case '2g':\n            case '3g':\n                result = 'cellular';\n                break;\n            case '4g':\n                result = 'wifi';\n                break;\n            default:\n                break;\n        }\n    }\n    return result;\n}\nexport class NetworkWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.handleOnline = () => {\n            const connectionType = translatedConnection();\n            const status = {\n                connected: true,\n                connectionType: connectionType,\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        this.handleOffline = () => {\n            const status = {\n                connected: false,\n                connectionType: 'none',\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        if (typeof window !== 'undefined') {\n            window.addEventListener('online', this.handleOnline);\n            window.addEventListener('offline', this.handleOffline);\n        }\n    }\n    async getStatus() {\n        if (!window.navigator) {\n            throw this.unavailable('Browser does not support the Network Information API');\n        }\n        const connected = window.navigator.onLine;\n        const connectionType = translatedConnection();\n        const status = {\n            connected,\n            connectionType: connected ? connectionType : 'none',\n        };\n        return status;\n    }\n}\nconst Network = new NetworkWeb();\nexport { Network };\n//# sourceMappingURL=web.js.map"], "names": ["translatedConnection", "connection", "result", "type", "NetworkWeb", "WebPlugin", "status", "connected", "connectionType", "Network"], "mappings": "uCACA,SAASA,GAAuB,CAC5B,MAAMC,EAAa,OAAO,UAAU,YAChC,OAAO,UAAU,eACjB,OAAO,UAAU,iBACrB,IAAIC,EAAS,UACb,MAAMC,EAAOF,EAAaA,EAAW,MAAQA,EAAW,cAAgB,KACxE,GAAIE,GAAQ,OAAOA,GAAS,SACxB,OAAQA,EAAI,CAER,IAAK,YACL,IAAK,WACDD,EAAS,WACT,MACJ,IAAK,OACDA,EAAS,OACT,MACJ,IAAK,WACL,IAAK,OACL,IAAK,QACDA,EAAS,OACT,MACJ,IAAK,QACL,IAAK,UACDA,EAAS,UACT,MAEJ,IAAK,UACL,IAAK,KACL,IAAK,KACDA,EAAS,WACT,MACJ,IAAK,KACDA,EAAS,OACT,KAGhB,CAEI,OAAOA,CACX,CACO,MAAME,UAAmBC,CAAU,CACtC,aAAc,CACV,MAAO,EACP,KAAK,aAAe,IAAM,CAEtB,MAAMC,EAAS,CACX,UAAW,GACX,eAHmBN,EAAsB,CAI5C,EACD,KAAK,gBAAgB,sBAAuBM,CAAM,CACrD,EACD,KAAK,cAAgB,IAAM,CACvB,MAAMA,EAAS,CACX,UAAW,GACX,eAAgB,MACnB,EACD,KAAK,gBAAgB,sBAAuBA,CAAM,CACrD,EACG,OAAO,OAAW,MAClB,OAAO,iBAAiB,SAAU,KAAK,YAAY,EACnD,OAAO,iBAAiB,UAAW,KAAK,aAAa,EAEjE,CACI,MAAM,WAAY,CACd,GAAI,CAAC,OAAO,UACR,MAAM,KAAK,YAAY,sDAAsD,EAEjF,MAAMC,EAAY,OAAO,UAAU,OAC7BC,EAAiBR,EAAsB,EAK7C,MAJe,CACX,UAAAO,EACA,eAAgBA,EAAYC,EAAiB,MAChD,CAET,CACA,CACK,MAACC,EAAU,IAAIL", "x_google_ignoreList": [0]}
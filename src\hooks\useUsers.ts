import { useState, useCallback } from 'react';
import { User } from '@/types';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export function useUsers() {
    const [loading, setLoading] = useState(false);

    const getUserById = useCallback(async (userId: string): Promise<User | null> => {
        try {
            setLoading(true);
            const userDoc = await getDoc(doc(db, 'users', userId));
            if (!userDoc.exists()) return null;
            return { id: userDoc.id, ...userDoc.data() } as User;
        } catch (error) {
            console.error('Error fetching user:', error);
            return null;
        } finally {
            setLoading(false);
        }
    }, []);

    return {
        loading,
        getUserById
    };
}

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Target, Zap, Clock, CheckCircle, ArrowRight } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import NeuCard from '../ui/NeuCard';
import NeonButton from '../ui/NeonButton';
import HoverEffect from '../ui/HoverEffect';
import AnimatedTooltip from '../ui/AnimatedTooltip';
import { MissionService } from '@/services/missionService';
import { Mission } from '@/types/gamification';

interface MissionWithIcon extends Mission {
  icon: React.ComponentType<{ className?: string }>;
}

const DailyMissions = () => {
  const { user } = useAuth();
  const [dailyMissions, setDailyMissions] = useState<MissionWithIcon[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get mission icon based on activity type
  const getMissionIcon = (activityType: string): React.ComponentType<{ className?: string }> => {
    switch (activityType) {
      case 'DAILY_LOGIN': return CheckCircle;
      case 'ADD_TOOL_TO_LIBRARY': return Target;
      case 'CREATE_POST': return Target;
      case 'LIKE_POST': return Target;
      case 'STAY_ONLINE': return Clock;
      case 'CREATE_TOOLKIT': return Target;
      default: return Target;
    }
  };

  const loadDailyMissions = async () => {
    if (!user?.uid) return;
    
    setIsLoading(true);
    try {
      const missions = await MissionService.getUserMissions(user.uid);
      
      // Filter only daily missions and add icons
      const dailyMissionsWithIcons = missions
        .filter(mission => mission.category === 'daily')
        .slice(0, 3) // Show only top 3 daily missions
        .map(mission => ({
          ...mission,
          icon: getMissionIcon(mission.activityType)
        }));
      
      setDailyMissions(dailyMissionsWithIcons);
    } catch (error) {
      console.error('Error loading daily missions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadDailyMissions();
  }, [user?.uid]);

  if (isLoading) {
    return (
      <NeuCard variant="elevated" color="purple" className="border border-sortmy-blue/20">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <Target className="w-5 h-5 mr-2 text-sortmy-blue" />
            <span className="text-white font-bold">Daily Missions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-sortmy-gray/10 rounded-lg p-4 border border-sortmy-blue/10 animate-pulse">
                <div className="h-4 bg-gray-600 rounded mb-2"></div>
                <div className="h-3 bg-gray-700 rounded mb-3 w-3/4"></div>
                <div className="h-6 bg-gray-600 rounded w-20"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </NeuCard>
    );
  }

  if (dailyMissions.length === 0) {
    return (
      <NeuCard variant="elevated" color="purple" className="border border-sortmy-blue/20">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <Target className="w-5 h-5 mr-2 text-sortmy-blue" />
            <span className="text-white font-bold">Daily Missions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-sortmy-gray/10 rounded-lg p-4 border border-sortmy-blue/10 text-center">
            <p className="text-gray-300 mb-4">No daily missions available right now.</p>
            <HoverEffect effect="lift" color="blue">
              <Link to="/missions">
                <NeonButton variant="gradient" size="sm">View All Missions</NeonButton>
              </Link>
            </HoverEffect>
          </div>
        </CardContent>
      </NeuCard>
    );
  }

  return (
    <NeuCard variant="elevated" color="purple" className="border border-sortmy-blue/20">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center">
            <Target className="w-5 h-5 mr-2 text-sortmy-blue" />
            <span className="text-white font-bold">Daily Missions</span>
          </div>
          <Link to="/missions" className="text-sortmy-blue hover:text-blue-300 transition-colors">
            <ArrowRight className="w-4 h-4" />
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {dailyMissions.map((mission) => {
            const IconComponent = mission.icon;
            const progressPercentage = Math.min((mission.progress / mission.maxProgress) * 100, 100);
            
            return (
              <div 
                key={mission.id} 
                className={`bg-sortmy-gray/10 rounded-lg p-4 border transition-all duration-200 ${
                  mission.isCompleted 
                    ? 'border-green-500/30 bg-green-500/5' 
                    : 'border-sortmy-blue/10 hover:border-sortmy-blue/30'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center">
                    <IconComponent className={`w-4 h-4 mr-2 ${
                      mission.isCompleted ? 'text-green-400' : 'text-sortmy-blue'
                    }`} />
                    <h3 className="font-medium text-white text-sm">{mission.title}</h3>
                  </div>
                  {mission.isCompleted && (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  )}
                </div>
                
                <p className="text-xs text-gray-300 mb-3">{mission.description}</p>
                
                {/* Progress Bar */}
                <div className="mb-3">
                  <div className="flex justify-between text-xs text-gray-400 mb-1">
                    <span>{mission.progress}/{mission.maxProgress}</span>
                    <span>{Math.round(progressPercentage)}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full transition-all duration-300 ${
                        mission.isCompleted ? 'bg-green-400' : 'bg-sortmy-blue'
                      }`}
                      style={{ width: `${progressPercentage}%` }}
                    />
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <AnimatedTooltip content="XP reward for completing this mission" position="top">
                    <div className="flex items-center">
                      <Zap className="w-3 h-3 mr-1 text-sortmy-blue" />
                      <span className="text-[#03ABEE] text-xs font-medium">{mission.xpReward} XP</span>
                    </div>
                  </AnimatedTooltip>
                  
                  {mission.isCompleted && !mission.isClaimed && (
                    <span className="text-xs text-yellow-400 font-medium">Ready to Claim!</span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        
        {/* View All Missions Button */}
        <div className="mt-4 pt-3 border-t border-sortmy-blue/10">
          <HoverEffect effect="lift" color="blue">
            <Link to="/missions" className="w-full">
              <NeonButton variant="gradient" size="sm" className="w-full">
                <span className="flex items-center justify-center text-white">
                  View All Missions <ArrowRight className="w-4 h-4 ml-1" />
                </span>
              </NeonButton>
            </Link>
          </HoverEffect>
        </div>
      </CardContent>
    </NeuCard>
  );
};

export default DailyMissions;

# Portfolio System Documentation

## Overview
The Portfolio System in SortMind Zenith provides a robust platform for users to showcase their work, projects, and achievements. It features modern media handling, auto-save functionality, and enhanced performance optimizations.

## Core Components

### 1. Enhanced Portfolio Form (`src/components/portfolio/EnhancedPortfolioForm`)
- Main form component for portfolio creation and editing
- Features:
  - Auto-save drafts every 30 seconds
  - Media upload integration
  - Form validation
  - Error handling

### 2. Portfolio Media Uploader (`src/components/portfolio/PortfolioMediaUploader`)
- Handles all media upload operations
- Features:
  - Image compression and optimization
  - Video upload via Livepeer
  - Progress tracking
  - Error recovery

### 3. PortfolioItemCard (`src/components/portfolio/PortfolioItemCard`)
- Individual portfolio item display component
- Features:
  - Responsive card layout
  - Media preview handling
  - Interactive animations
  - Action buttons (edit, delete, share)
  - Stats display (views, likes)
  - Tags and categories
```typescript
interface PortfolioItemCardProps {
  item: PortfolioItem;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  showActions?: boolean;
  isPublic?: boolean;
}
```

### 4. LightBox (`src/components/portfolio/LightBox`)
- Advanced media viewer component
- Features:
  - Full-screen media display
  - Image and video support
  - Gesture controls
  - Zoom capabilities
  - Slideshow functionality
  - Keyboard navigation
```typescript
interface LightBoxProps {
  media: Media[];
  initialIndex?: number;
  onClose: () => void;
  showThumbnails?: boolean;
  allowDownload?: boolean;
}
```

### 5. Draft Manager (`src/components/portfolio/DraftManager`)
- Manages draft saving and recovery
- Features:
  - Automatic saving
  - Draft versioning
  - Recovery options
  - Clean-up of old drafts

## Service Layer

### 1. Portfolio Storage Service
```typescript
// services/portfolioStorageService.ts
- uploadMedia(file: File): Promise<string>
- deleteMedia(path: string): Promise<void>
- optimizeImage(file: File): Promise<File>
- handleVideoUpload(file: File): Promise<string>
```

### 2. Cleanup Service
```typescript
// services/scheduledCleanupService.ts
- setupAutomaticCleanup()
- setupBrowserCleanup()
- cleanupOrphanedFiles()
- manageDrafts()
```

## Key Routes

### Portfolio Routes
```typescript
/dashboard/portfolio          // Portfolio list view
/dashboard/portfolio/add     // Create new portfolio
/dashboard/portfolio/edit/:id // Edit existing portfolio
/portfolio/:username         // Public portfolio view
```

## Migration Notes

### Recent Updates
- Moved from Google Drive to Firebase Storage
- Enhanced Livepeer integration for videos
- Added auto-save functionality
- Implemented draft management
- Added automated cleanup services

### Cleanup Process
- Automated file cleanup initialization in App.tsx
- Browser localStorage cleanup
- Scheduled maintenance tasks

## Security Implementation

### Firebase Rules
```javascript
// storage.rules
match /portfolioMedia/{userId}/{fileName} {
  allow read: if true;
  allow write: if request.auth.uid == userId;
}

// firestore.rules
match /portfolios/{portfolioId} {
  allow read: if true;
  allow write: if request.auth.uid == resource.data.userId;
}
```

## Type Definitions

### Media Types
```typescript
interface Media {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnailUrl?: string;
  description?: string;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    size?: number;
    mimeType?: string;
  };
}

interface LightBoxControls {
  next: () => void;
  previous: () => void;
  close: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  toggleAutoplay: () => void;
}

interface MediaViewerState {
  currentIndex: number;
  isPlaying: boolean;
  zoomLevel: number;
  isFullscreen: boolean;
}
```

## Feature Matrix

### Free Tier
- Basic portfolio creation
- Standard media upload
- Simple customization

### Premium Tier (SortMyAI+)
- Advanced portfolio customization
- Priority media processing
- Extended storage
- Analytics and insights
- Custom domains

## Error Handling

### Common Scenarios
1. Media Upload Failures
   - Automatic retry mechanism
   - User feedback
   - Fallback options

2. Draft Recovery
   - Automatic save points
   - Manual recovery options
   - Version history

3. Data Validation
   - Input sanitization
   - Format verification
   - Size limitations

## Performance Optimizations

### Media Handling
- Image compression before upload
- Lazy loading of media
- Progressive image loading
- Video streaming optimization

### Data Management
- Efficient database queries
- Indexed search
- Cached responses
- Background cleanup

## Testing Guidelines

### Unit Tests
```typescript
// Example test structure
describe('PortfolioForm', () => {
  it('should handle media upload', async () => {
    // Test implementation
  });
  
  it('should auto-save drafts', async () => {
    // Test implementation
  });
});
```

### Integration Tests
- Form submission flow
- Media upload pipeline
- Draft management
- Error scenarios

## User Interface Guidelines

### Media Upload
- Support drag and drop
- Progress indicators
- Preview capabilities
- Edit options

### Form Design
- Responsive layout
- Inline validation
- Autosave indicators
- Error messages

## Maintenance Tasks

### Regular Cleanup
- Remove orphaned media files
- Clean old drafts
- Update cache
- Optimize storage

### Monitoring
- Track upload success rates
- Monitor storage usage
- Track user engagement
- Performance metrics

## Development Workflow

### Adding New Features
1. Create feature branch
2. Implement changes
3. Add tests
4. Update documentation
5. Create pull request

### Debugging Tools
- Browser console logging
- Firebase debugging
- Storage monitoring
- Performance profiling

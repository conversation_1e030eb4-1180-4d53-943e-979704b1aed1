{"version": 3, "file": "web-D2G9elIF.js", "sources": ["../../node_modules/@capacitor/splash-screen/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class SplashScreenWeb extends WebPlugin {\n    async show(_options) {\n        return undefined;\n    }\n    async hide(_options) {\n        return undefined;\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["SplashScreenWeb", "WebPlugin", "_options"], "mappings": "uCACO,MAAMA,UAAwBC,CAAU,CAC3C,MAAM,KAAKC,EAAU,CAEzB,CACI,MAAM,KAAKA,EAAU,CAEzB,CACA", "x_google_ignoreList": [0]}
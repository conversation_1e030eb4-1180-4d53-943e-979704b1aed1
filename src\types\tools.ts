import type { AITool } from '@/types';

export type ImageSizeType = 'contain' | 'cover' | '85%' | '75%' | '50%';
export type ImagePositionType = 'center' | 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';

// Base interface for common tool properties
export interface BaseTool {
    id: string;
    name: string;
    description: string;
    tags: string[];
    category?: string;
    created_at: string;
    updated_at?: string;
    user_id?: string;
    source: 'tools_collection' | 'user_tooltracker' | 'library' | 'user';
    toolkit_id?: string;
}

// Interface for image settings used by tools and toolkits
export interface ImageSettings {
    size: ImageSizeType;
    position: ImagePositionType;
    padding: number;
    scale: number;
    rotate: number;
    aspectRatio?: number;
    zoom?: number;
    crop?: {
        x: number;
        y: number;
        width: number;
        height: number;
        unit: '%';
    };
}

// Regular tool interface
export interface Tool extends BaseTool {
    // Primary fields (snake_case)
    logo_url?: string;       // Primary field for logo URL
    website?: string;        // Primary field for website URL
    description: string;     // Required
    use_case?: string;      // Optional fields
    pricing?: string;
    excels_at?: string;
    status?: string;
    image_settings?: ImageSettings;
    icon_url?: string;      // For toolkit icons

    // Legacy/compatibility fields (camelCase)
    logoUrl?: string;       // @deprecated Use logo_url
    logoLink?: string;      // @deprecated Use logo_url
    websiteLink?: string;   // @deprecated Use website
    link?: string;          // @deprecated Use website
    useCase?: string;       // @deprecated Use use_case
    excelsAt?: string;      // @deprecated Use excels_at
    imageSettings?: ImageSettings; // @deprecated Use image_settings
}

// User tool type that extends BaseTool
export interface UserTool extends BaseTool {
    logo_url?: string;
    link?: string;
    toolkit_id?: string;
    image_settings?: ImageSettings;
}

// Toolkit interface that matches Firestore fields
export interface Toolkit {
    id: string;
    name: string;
    description: string;
    user_id: string;     // Creator's user ID
    tools: string[];     // Array of tool IDs
    image_url?: string;  // Image URL
    tags: string[];
    category?: string;
    created_at: string;
    updated_at?: string;
    image_settings?: ImageSettings;
    is_published?: boolean;
    published_at?: string;
    is_shared?: boolean;
    shared_with?: string[];
    portfolioId?: string;
}

// Toolkit interface that extends BaseTool
export interface Toolkit extends BaseTool {
    logo_url?: string;
    logoUrl?: string;
    created_by?: string;
    is_published?: boolean;
    tools: string[];
    image_settings?: ImageSettings;
}

export interface ToolkitPortfolioItem {
    id: string;
    userId: string;
    type: 'toolkit';
    title: string;
    description: string;
    media_url: string;
    media_type: 'image';
    content_type: 'toolkit';
    tools: Tool[];
    tags: string[];
    likes: number;
    views: number;
    is_public: boolean;
    status: 'published';
    created_at: string;
    updated_at: string;
    toolkitId: string;
}

// Toolkit notification type
export interface ToolkitNotification {
    id: string;
    userId: string;
    type: 'toolkit_share' | 'toolkit_publish';
    toolkitId: string;
    createdAt: string;
    read: boolean;
}

export interface ToolkitPreviewProps {
    toolkitId: string;
    tools: UserTool[];
}

// Update ToolGridProps to include toolkit support
export interface ToolGridProps {
    tools: Tool[];
    isLoading?: boolean;
    isLoadingTools?: boolean;  // Add this for backward compatibility
    error?: any;
    onDelete?: (tool: Tool) => void;
    onAdd?: (tool: Tool | AITool) => Promise<void>;  // Update to match the function signature
    onEdit?: (tool: Tool) => Promise<void>;  // Update to match the function signature
    isAdmin?: boolean;
    savingToolIds?: string[];
    emptyMessage?: string;
    showToolkits?: boolean;
    toolkits?: Toolkit[];
    onToolkitAdd?: (toolkit: Toolkit) => void;
    onToolkitEdit?: (toolkit: Toolkit) => void;
    onToolkitDelete?: (toolkit: Toolkit) => void;
}

export interface ToolkitFormData {
    name: string;
    description: string;
    logo_url?: string;
    logoUrl?: string;
    tags: string[];
    tools: string[];
    user_id: string;
    is_published?: boolean;
    source?: 'tools_collection' | 'user_tooltracker' | 'library' | 'user';
}

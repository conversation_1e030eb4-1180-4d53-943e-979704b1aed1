import { useRef } from 'react';
import { Search, FileText, Bookmark, Hash, Info } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ToolSearchProps } from './types';

export const ToolSearch = ({
    searchQuery,
    setSearchQuery,
    suggestions,
    showSuggestions,
    setShowSuggestions,
    handleSelectSuggestion,
    selectedSuggestionIndex,
    handleKeyDown
}: ToolSearchProps) => {
    const searchInputRef = useRef<HTMLInputElement>(null);
    const suggestionsRef = useRef<HTMLDivElement>(null);

    return (
        <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4 z-10" />
            <Input
                ref={searchInputRef}
                placeholder="Search tools by name, description, or tag..."
                className="pl-10 bg-sortmy-darker/50 border-[#01AAE9]/20 focus:border-[#01AAE9]/40 focus:ring-[#01AAE9]/10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onFocus={() => setShowSuggestions(true)}
                onKeyDown={handleKeyDown}
            />

            {showSuggestions && searchQuery.length >= 2 && (
                <div
                    ref={suggestionsRef}
                    className="absolute z-50 w-full mt-1 bg-sortmy-darker border border-[#01AAE9]/20 rounded-md shadow-lg max-h-60 overflow-auto"
                    style={{ boxShadow: '0 4px 20px rgba(1, 170, 233, 0.2)' }}
                >
                    <div className="p-2">
                        {suggestions.length > 0 ? (
                            suggestions.map((suggestion, index) => (
                                <div
                                    key={`${suggestion.text}-${suggestion.type}-${index}`}
                                    className={`
                    flex items-center p-2 rounded-md cursor-pointer
                    ${selectedSuggestionIndex === index ? 'bg-[#01AAE9]/20' : 'hover:bg-[#01AAE9]/10'}
                  `}
                                    onClick={() => handleSelectSuggestion(suggestion)}
                                >
                                    {suggestion.type === 'name' && <Search className="h-4 w-4 mr-2 text-[#01AAE9]" />}
                                    {suggestion.type === 'description' && <FileText className="h-4 w-4 mr-2 text-[#01AAE9]" />}
                                    {suggestion.type === 'category' && <Bookmark className="h-4 w-4 mr-2 text-[#01AAE9]" />}
                                    {suggestion.type === 'tag' && <Hash className="h-4 w-4 mr-2 text-[#01AAE9]" />}

                                    <div className="flex-1">
                                        <span className="text-sm text-white">{suggestion.text}</span>
                                    </div>

                                    <Badge
                                        variant="outline"
                                        className={`
                      text-xs
                      ${suggestion.source === 'user' ? 'bg-green-500/10 text-green-400 border-green-500/20' :
                                                suggestion.source === 'library' ? 'bg-blue-500/10 text-blue-400 border-blue-500/20' :
                                                    'bg-purple-500/10 text-purple-400 border-purple-500/20'}
                    `}
                                    >
                                        {suggestion.source === 'user' ? 'Your Tool' :
                                            suggestion.source === 'library' ? 'Library' : 'Both'}
                                    </Badge>
                                </div>
                            ))
                        ) : (
                            <div className="p-3 text-center text-gray-400">
                                <Info className="h-4 w-4 mx-auto mb-2 opacity-50" />
                                <p className="text-sm">No suggestions found for "{searchQuery}"</p>
                                <p className="text-xs mt-1">Try a different search term or browse by tags</p>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};


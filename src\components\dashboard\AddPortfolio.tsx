import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { addDoc, collection, doc, updateDoc, arrayUnion, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { PortfolioForm } from '@/components/portfolio/PortfolioForm';
import PostTypeSelector from '@/components/portfolio/PostTypeSelector';
import { useToast } from '@/hooks/use-toast';
import { MuxUploader } from '@/components/video/MuxUploader';
import { MuxPlayer } from '@/components/video/MuxPlayer';
import { Card } from '@/components/ui/card';
import { MissionTracker } from '@/services/missionService';

type PostType = 'video' | 'link' | 'image';

interface VideoMetadata {
  title?: string;
  description?: string;
  tags?: string[];
  playbackId?: string;
  playbackUrl?: string;
  assetId?: string;
}

export default function AddPortfolio() {
  const [postType, setPostType] = useState<PostType | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handlePostTypeSelect = (type: PostType) => {
    setPostType(type);
  };

  const handleSubmit = async (data: any) => {
    console.log('🎯 AddPortfolio: handleSubmit called with data:', data);

    if (!user) {
      console.log('❌ AddPortfolio: No user found');
      toast({
        title: 'Error',
        description: 'You must be logged in to add a portfolio item.',
        variant: 'destructive'
      });
      return;
    }

    try {
      console.log('🎯 AddPortfolio: Starting submission process');
      setIsSubmitting(true);

      // Create unique ID for the portfolio item
      const timestamp = Date.now();
      const portfolioId = `portfolio-${user.id}-${timestamp}`;

      // Create a new portfolio item with proper video URLs
      const portfolioData = {
        ...data,
        id: portfolioId,
        userId: user.id,
        media_url: videoMetadata?.playbackUrl || videoUrl, // Use full playback URL
        media_urls: videoMetadata?.playbackUrl ? [videoMetadata.playbackUrl] : undefined,
        playbackId: videoMetadata?.playbackId, // Store playback ID separately
        media_type: postType === 'video' ? 'video' : 'image',
        content_type: postType === 'video' ? 'reel' : 'post',
        status: 'published',
        views: 0,
        likes: 0,
        comments: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('🎯 AddPortfolio: Adding to portfolio collection with data:', portfolioData);

      // Add to portfolio collection
      const portfolioRef = collection(db, 'portfolio');
      const newPortfolioRef = await addDoc(portfolioRef, portfolioData);
      console.log('🎯 AddPortfolio: Portfolio added with ID:', newPortfolioRef.id);

      // Update user's portfolio array
      const userRef = doc(db, 'users', user.id);
      await updateDoc(userRef, {
        portfolios: arrayUnion(newPortfolioRef.id),
        // Also update the portfolio items array if it exists
        gdrive_portfolio_items: arrayUnion(portfolioData)
      });
      console.log('🎯 AddPortfolio: User document updated');

      // Track mission progress for portfolio creation
      try {
        console.log('🎯 AddPortfolio: Tracking post creation mission for user:', user.id);
        // Track post creation mission (this is what we want for Content Creator mission)
        await MissionTracker.createPost(user.id);
        console.log('🎯 AddPortfolio: Post creation mission tracked successfully');

        // Check if this is the user's first portfolio item for the milestone mission
        const userRef = doc(db, 'users', user.id);
        const userDoc = await getDoc(userRef);
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const portfolioCount = userData.portfolios?.length || 0;
          if (portfolioCount === 1) {
            // This is their first portfolio item
            await MissionTracker.createFirstPortfolio(user.id);
          }
        }
      } catch (missionError) {
        console.warn('Error tracking portfolio mission:', missionError);
        // Don't fail the portfolio creation if mission tracking fails
      }

      console.log('🎯 AddPortfolio: Portfolio creation completed successfully');

      toast({
        title: 'Success',
        description: 'Portfolio item added successfully!'
      });

      navigate('/portfolio');
    } catch (error) {
      console.error('❌ AddPortfolio: Error adding portfolio:', error);
      toast({
        title: 'Error',
        description: 'Failed to add portfolio item. Please try again.',
        variant: 'destructive'
      });
    } finally {
      console.log('🎯 AddPortfolio: Setting isSubmitting to false');
      setIsSubmitting(false);
    }
  };

  const renderContent = () => {
    if (!postType) {
      return <PostTypeSelector onSelect={handlePostTypeSelect} />;
    }

    if (postType === 'video' && !videoUrl) {
      return (
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Upload Video</h2>
          <MuxUploader
            onUploadComplete={(result) => {
              const url = `https://stream.mux.com/${result.playbackId}.m3u8`;
              setVideoUrl(url);
              setVideoMetadata({
                playbackId: result.playbackId,
                assetId: result.assetId,
                playbackUrl: url
              });
              toast({
                title: "Success",
                description: "Video uploaded successfully!"
              });
            }}
            onUploadError={(error) => {
              toast({
                title: "Error",
                description: error,
                variant: "destructive"
              });
            }}
          />
        </Card>
      );
    }

    return (
      <>
        {videoUrl && postType === 'video' && (
          <Card className="p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Video Preview</h2>
            <MuxPlayer
              src={videoUrl}
              playbackId={videoMetadata?.playbackId}
              muted={false}
              autoPlay={false}
            />
          </Card>
        )}
        <PortfolioForm
          user={user!}
          onSubmit={handleSubmit}
          isLoading={isSubmitting}
          skipFirebaseUpdate={true}
          initialData={{
            content_type: postType === 'video' ? 'reel' : 'post',
            media_url: videoUrl,
            title: videoMetadata?.title || '',
            description: videoMetadata?.description || '',
            tools_used: videoMetadata?.tags || [],
            project_url: postType === 'link' ? '' : undefined
          }}
        />
      </>
    );
  };

  return (
    <div className="min-h-screen bg-sortmy-dark p-6">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Add New Project</h1>
        <div className="bg-sortmy-darker rounded-lg p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}

import React, { useState, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { X, Play, Pause, Volume2, VolumeX, Video, MessageSquare, Eye } from 'lucide-react';
import { PortfolioItem } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import { ImageItem } from './ImageItem';
import LikeButton from '@/components/interactions/LikeButton';
import CommentSection from '@/components/interactions/CommentSection';
import LikesModal from '@/components/interactions/LikesModal';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/firebase';
import { trackView } from '@/services/analyticsService';
import { MuxPlayer } from '@/components/video/MuxPlayer';
import { isMuxUrl, extractMuxPlaybackId } from '@/lib/mux';
import { collection, query, where, onSnapshot, getDocs, addDoc, deleteDoc, doc, limit } from 'firebase/firestore';
import { Separator } from './separator';

// Helper function to get interaction stats
async function getPostInteractionStats(postId: string, userId?: string) {
  const likesRef = collection(db, 'likes');
  const likesSnap = await getDocs(query(likesRef, where('postId', '==', postId)));
  const likes = likesSnap.size;
  let userHasLiked = false;

  if (userId) {
    userHasLiked = !(
      await getDocs(query(likesRef, where('postId', '==', postId), where('userId', '==', userId), limit(1)))
    ).empty;
  }

  return {
    likes,
    comments: 0, // Implement comment counting if needed
    userHasLiked,
  };
}

interface LightboxProps {
  item: PortfolioItem | null;
  isOpen: boolean;
  onClose: () => void;
}

export const Lightbox: React.FC<LightboxProps> = ({ item, isOpen, onClose }) => {
  const { user } = useAuth();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isMuted, setIsMuted] = useState(true);
  const [shouldPlay, setShouldPlay] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [likeCount, setLikeCount] = useState(0);
  const [commentCount, setCommentCount] = useState(0);
  const [userHasLiked, setUserHasLiked] = useState(false);
  const [showLikesModal, setShowLikesModal] = useState(false);
  const [hasTrackedView, setHasTrackedView] = useState(false);

  // Add real-time listener for likes
  useEffect(() => {
    if (!item?.id || !isOpen) return;

    const likesRef = collection(db, 'likes');
    const q = query(likesRef, where('postId', '==', item.id));
    const unsubscribe = onSnapshot(q, (snapshot) => {
      setLikeCount(snapshot.size);
      if (user) {
        setUserHasLiked(snapshot.docs.some(doc => doc.data().userId === (user.id || user.uid)));
      } else {
        setUserHasLiked(false);
      }
    });

    return () => unsubscribe();
  }, [item?.id, isOpen, user]);

  // Update handleLikePost to use the same logic as PortfolioItemCard
  const handleLikePost = async (postId: string, userId: string, liked: boolean) => {
    const likesRef = collection(db, 'likes');
    const q = query(likesRef, where('userId', '==', userId), where('postId', '==', postId));
    const snapshot = await getDocs(q);

    if (liked) {
      // Like: create if not exists
      if (snapshot.empty) {
        await addDoc(likesRef, {
          userId,
          postId,
          createdAt: new Date().toISOString()
        });
      }
    } else {
      // Unlike: remove if exists
      if (!snapshot.empty) {
        await deleteDoc(doc(db, 'likes', snapshot.docs[0].id));
      }
    }
  };

  // Reset image index and fetch interaction stats when item changes
  useEffect(() => {
    setCurrentImageIndex(0);

    // Fetch interaction stats when item changes
    const fetchInteractionStats = async () => {
      if (item && isOpen) {
        try {
          // Use default values if item.id is not defined
          if (!item.id) {
            console.warn('Portfolio item has no ID, using default interaction values');
            setLikeCount(item.likes || 0);
            setCommentCount(item.comments || 0);
            setUserHasLiked(false);
            return;
          }

          const stats = await getPostInteractionStats(item.id, user?.uid);
          setLikeCount(stats.likes);
          setCommentCount(stats.comments);
          setUserHasLiked(stats.userHasLiked);
        } catch (error) {
          console.error('Error fetching interaction stats:', error);
          // Use default values on error
          setLikeCount(item.likes || 0);
          setCommentCount(item.comments || 0);
          setUserHasLiked(false);
        }
      }
    };

    fetchInteractionStats();
  }, [item, isOpen, user?.uid, item?.likes, item?.comments]);
  // Extract Google Drive file ID if present
  const getGoogleDriveFileId = (url: string): string | null => {
    if (!url) return null;
    const fileIdMatch = url.match(/\/d\/([^\/]+)/);
    return fileIdMatch ? fileIdMatch[1] : null;
  };

  // Custom play/pause for both HTML5 and MuxPlayer
  const togglePlay = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShouldPlay((prev) => !prev);
  };

  // Custom mute/unmute for both HTML5 and MuxPlayer
  const toggleMute = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMuted((prev) => {
      const nextMuted = !prev;
      if (videoRef.current) {
        videoRef.current.muted = nextMuted;
      }
      return nextMuted;
    });
  };

  // Track view when lightbox opens
  useEffect(() => {
    const trackViewIfNeeded = async () => {
      if (isOpen && item && user && !hasTrackedView) {
        try {
          await trackView(item.id, 'portfolio', {
            ...user,
            uid: user.uid || user.id,
            username: user.username || '',
            xp: user.xp || 0,
            level: user.level || 1,
            streak_days: user.streak_days || 0,
            email: user.email || '',
            last_login: new Date().toISOString()
          });
          setHasTrackedView(true);
        } catch (error) {
          console.error('Error tracking lightbox view:', error);
        }
      }
    };

    trackViewIfNeeded();
  }, [isOpen, item, user]);

  // Reset view tracking when item changes
  useEffect(() => {
    setHasTrackedView(false);
  }, [item?.id]);

  // Handle multiple images if available
  const images = item?.media_urls ? item.media_urls : item?.media_url ? [item.media_url] : [];

  // Test direct playback of a Mux URL for debugging (this is temporary)
  const testMuxPlayback = (url: string) => {
    console.log('Testing video playback with URL:', url);
    if (isMuxUrl(url)) {
      const playbackId = extractMuxPlaybackId(url);
      console.log('Extracted Mux playback ID:', playbackId);
      return `Mux: ${playbackId}`;
    } else if (url.includes('livepeercdn.com') || url.includes('lp-playback.studio')) {
      console.log('Detected LivePeer URL');
      return 'LivePeer';
    } else if (url.includes('.m3u8')) {
      console.log('Detected HLS stream');
      return 'HLS';
    }
    return 'Standard';
  };

  if (!isOpen || !item) return null;

  // Create a portal to render the lightbox at the root level of the DOM
  return ReactDOM.createPortal(
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/90 backdrop-blur-md overflow-hidden"
      onClick={onClose}
      style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <div
        className="relative w-[95vw] max-w-5xl max-h-[90vh] bg-sortmy-darker border border-sortmy-blue/20 rounded-lg overflow-hidden flex flex-col md:flex-row shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-4 right-4 z-10 p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300 text-white"
          onClick={onClose}
        >
          <X className="w-5 h-5" />
        </button>

        {/* Media section */}
        <div className="relative w-full md:w-3/5 h-[40vh] md:h-[90vh] bg-sortmy-darker/50 border-r border-sortmy-blue/10">
          {images.length > 0 ? (
            <div className="w-full h-full">
              {/* Always check URL for video patterns, not just media_type */}
              {(() => {
                const url = images[currentImageIndex];
                // Google Drive Video
                if (url.includes('drive.google.com')) {
                  const fileId = getGoogleDriveFileId(url);
                  if (fileId) {
                    const isReel = item.content_type === 'reel' || item.content_type === 'both';
                    return (
                      <div className="relative w-full h-full">
                        <iframe
                          src={`https://drive.google.com/file/d/${fileId}/preview`}
                          allow="autoplay; fullscreen"
                          className="w-full h-full border-0"
                        />
                        {isReel && (
                          <div className="absolute top-4 left-4 z-10 bg-blue-500/80 text-white px-3 py-1 rounded text-sm font-medium flex items-center gap-1">
                            <Video className="w-4 h-4" />
                            Reel
                          </div>
                        )}
                      </div>
                    );
                  }
                  return (
                    <div className="w-full h-full flex items-center justify-center bg-sortmy-gray/20">
                      <p className="text-sm text-gray-400">Video preview not available</p>
                    </div>
                  );
                }
                // Mux Video
                if (isMuxUrl(url)) {
                  return (
                    <div className="relative w-full h-full flex items-center justify-center">
                      <div className="absolute top-1 left-1 z-50 text-xs bg-black/60 text-white p-1 rounded">
                        {testMuxPlayback(url)}
                      </div>
                      <MuxPlayer
                        src={url}
                        muted={isMuted}
                        autoPlay={false}
                        loop={item.content_type === 'reel' || item.content_type === 'both'}
                        controls={false}
                        className="w-full h-full object-contain"
                        playing={shouldPlay}
                      />
                      <div className="absolute bottom-4 left-4 z-20 flex items-center gap-2 pointer-events-auto">
                        <button onClick={togglePlay} className="p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300">
                          {shouldPlay ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                        <button onClick={toggleMute} className="p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300">
                          {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                        </button>
                      </div>
                      {(item.content_type === 'reel' || item.content_type === 'both') && (
                        <div className="absolute top-4 left-4 z-20 bg-blue-500/80 text-white px-3 py-1 rounded text-sm font-medium flex items-center gap-1 pointer-events-auto">
                          <Video className="w-4 h-4" />
                          Reel
                        </div>
                      )}
                    </div>
                  );
                }
                // HLS/LivePeer Video
                if (url.includes('.m3u8') || url.includes('livepeercdn.com') || url.includes('lp-playback.studio')) {
                  return (
                    <div className="relative w-full h-full flex items-center justify-center">
                      <div className="absolute top-1 left-1 z-50 text-xs bg-black/60 text-white p-1 rounded">
                        {testMuxPlayback(url)}
                      </div>
                      <MuxPlayer
                        src={url}
                        muted={isMuted}
                        autoPlay={false}
                        loop={item.content_type === 'reel' || item.content_type === 'both'}
                        controls={false}
                        className="w-full h-full object-contain"
                        playing={shouldPlay}
                      />
                      <div className="absolute bottom-4 left-4 z-20 flex items-center gap-2 pointer-events-auto">
                        <button onClick={togglePlay} className="p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300">
                          {shouldPlay ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                        <button onClick={toggleMute} className="p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300">
                          {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                        </button>
                      </div>
                      {(item.content_type === 'reel' || item.content_type === 'both') && (
                        <div className="absolute top-4 left-4 z-20 bg-blue-500/80 text-white px-3 py-1 rounded text-sm font-medium flex items-center gap-1 pointer-events-auto">
                          <Video className="w-4 h-4" />
                          Reel
                        </div>
                      )}
                    </div>
                  );
                }
                // HTML5 Video fallback
                if (url.match(/\.(mp4|webm|ogg)$/i)) {
                  return (
                    <div className="relative w-full h-full">
                      <video
                        ref={videoRef}
                        src={url}
                        className="w-full h-full object-cover"
                        muted={isMuted}
                        loop={item.content_type === 'reel' || item.content_type === 'both'}
                        playsInline
                        controls={false}
                        autoPlay={shouldPlay}
                      />
                      <div className="absolute bottom-4 left-4 z-20 flex items-center gap-2 pointer-events-auto">
                        <button onClick={togglePlay} className="p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300">
                          {shouldPlay ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                        <button onClick={toggleMute} className="p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300">
                          {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                        </button>
                      </div>
                      {(item.content_type === 'reel' || item.content_type === 'both') && (
                        <div className="absolute top-4 left-4 z-20 bg-blue-500/80 text-white px-3 py-1 rounded text-sm font-medium flex items-center gap-1 pointer-events-auto">
                          <Video className="w-4 h-4" />
                          Reel
                        </div>
                      )}
                    </div>
                  );
                }
                // Otherwise, treat as image
                return (
                  <ImageItem
                    src={url}
                    alt={item.title}
                    className="w-full h-full"
                  />
                );
              })()}
            </div>
          ) : null}
        </div>

        {/* Info section */}
        <div className="relative w-full md:w-2/5 h-[40vh] md:h-[90vh] bg-sortmy-darker p-4 flex flex-col">
          {/* Item title and close button for mobile */}
          <div className="flex items-center justify-between mb-4 md:hidden">
            <h2 className="text-xl font-bold text-white truncate">{item.title}</h2>
            <button
              className="p-2 rounded-full bg-sortmy-darker/80 hover:bg-sortmy-blue/20 border border-sortmy-blue/30 transition-all duration-300"
              onClick={onClose}
            >
              <X className="w-5 h-5 text-white" />
            </button>
          </div>

          {/* Item title and metadata */}
          <div className="hidden md:block mb-2">
            <h2 className="text-2xl font-bold text-white">{item.title}</h2>
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <span>{formatDistanceToNow(new Date(item.created_at), { addSuffix: true })}</span>
              <Separator />
              <span>{item.media_type === 'video' ? 'Video' : 'Image'}</span>
              <Separator />
              <span>{likeCount} {likeCount === 1 ? 'Like' : 'Likes'}</span>
              <Separator />
              <span>{commentCount} {commentCount === 1 ? 'Comment' : 'Comments'}</span>
            </div>
          </div>

          {/* Reel badge for reels content type */}
          {(item.content_type === 'reel' || item.content_type === 'both') && (
            <div className="absolute top-4 left-4 z-10 bg-blue-500/80 text-white px-3 py-1 rounded text-sm font-medium flex items-center gap-1">
              <Video className="w-4 h-4" />
              Reel
            </div>
          )}

          {/* Like and comment section */}
          <div className="mt-auto pt-4 border-t border-sortmy-blue/10">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <LikeButton
                  postId={item.id}
                  user={user ? { id: user.uid } : null}
                  initialLiked={userHasLiked}
                  initialLikeCount={likeCount}
                  likePost={handleLikePost}
                  onLikeChange={async (liked, newCount) => {
                    setUserHasLiked(liked);
                    setLikeCount(newCount);
                  }}
                />
                <button
                  onClick={() => setShowLikesModal(true)}
                  className="flex items-center gap-1 text-sm text-sortmy-blue hover:underline"
                >
                  <Eye className="w-4 h-4" />
                  {likeCount} {likeCount === 1 ? 'Like' : 'Likes'}
                </button>
              </div>
              <button
                onClick={() => setCommentCount((prev) => prev + 1)} // Dummy increment for testing
                className="flex items-center gap-1 text-sm text-sortmy-blue hover:underline"
              >
                <MessageSquare className="w-4 h-4" />
                {commentCount} {commentCount === 1 ? 'Comment' : 'Comments'}
              </button>
            </div>
          </div>

          {/* Comment section - always show for now */}
          <div className="mt-4 flex-1 overflow-auto">
            <CommentSection postId={item.id} initialCommentCount={item.comments || 0} />
          </div>

          {/* Likes modal - controlled by state */}
          {showLikesModal && (
            <LikesModal
              isOpen={showLikesModal}
              onClose={() => setShowLikesModal(false)}
              postId={item.id}
              likeCount={likeCount}
            />
          )}
        </div>
      </div>
    </div>,
    document.body
  );
};

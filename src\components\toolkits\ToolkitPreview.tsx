import { Tool, Toolkit } from '@/types/tools';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Edit, Trash, Share, Layers, ExternalLink } from 'lucide-react';

interface ToolkitPreviewProps {
    toolkit: Toolkit;
    tools: Tool[];
    onEdit?: (e: React.MouseEvent, toolkit: Toolkit) => void;
    onDelete?: (e: React.MouseEvent, toolkit: Toolkit) => void;
    onShare?: (e: React.MouseEvent, toolkit: Toolkit) => void;
    isOwner?: boolean;
}

export const ToolkitPreview: React.FC<ToolkitPreviewProps> = ({
    toolkit,
    tools,
    onEdit,
    onDelete,
    onShare,
    isOwner
}) => {
    const toolIds = toolkit.tools || [];
    // Use a Map to deduplicate tools by ID
    const toolMap = new Map();
    tools.forEach(tool => {
        if (toolIds.includes(tool.id) && !toolMap.has(tool.id)) {
            toolMap.set(tool.id, tool);
        }
    });
    const toolkitTools = Array.from(toolMap.values());

    return (
        <div className="group relative overflow-hidden rounded-xl bg-gradient-to-br from-gray-900/60 to-gray-800/60 border border-gray-700/50 backdrop-blur-sm hover:border-purple-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10">
            {/* Gradient overlay on hover */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <div className="relative p-6">
                {/* Header Section */}
                <div className="flex items-start gap-4 mb-4">
                    {/* Logo */}
                    <div className="relative">
                        {toolkit.logo_url ? (
                            <div className="w-14 h-14 rounded-xl overflow-hidden bg-gradient-to-br from-purple-500/20 to-pink-500/20 p-1">
                                <img
                                    src={toolkit.logo_url}
                                    alt={toolkit.name}
                                    className="w-full h-full rounded-lg object-cover"
                                />
                            </div>
                        ) : (
                            <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center">
                                <Layers className="w-7 h-7 text-purple-400" />
                            </div>
                        )}
                        {/* Tool count badge */}
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                            {toolkitTools.length}
                        </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-bold text-white mb-1 truncate group-hover:text-purple-300 transition-colors">
                            {toolkit.name}
                        </h3>
                        <p className="text-sm text-gray-400 line-clamp-2 leading-relaxed">
                            {toolkit.description || 'No description provided'}
                        </p>

                        {/* Stats */}
                        <div className="flex items-center gap-3 mt-2 text-xs text-gray-500">
                            <span className="flex items-center gap-1">
                                <Layers className="w-3 h-3" />
                                {toolkitTools.length} tools
                            </span>
                            {toolkit.tags && toolkit.tags.length > 0 && (
                                <span className="flex items-center gap-1">
                                    <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                                    {toolkit.tags.length} tags
                                </span>
                            )}
                        </div>
                    </div>

                    {/* Action Buttons */}
                    {isOwner && (
                        <div className="flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 hover:bg-blue-500/20 hover:text-blue-400"
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onEdit?.(e, toolkit);
                                }}
                            >
                                <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 hover:bg-green-500/20 hover:text-green-400"
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onShare?.(e, toolkit);
                                }}
                            >
                                <Share className="w-4 h-4" />
                            </Button>
                            <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 hover:bg-red-500/20 hover:text-red-400"
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    onDelete?.(e, toolkit);
                                }}
                            >
                                <Trash className="w-4 h-4" />
                            </Button>
                        </div>
                    )}
                </div>

                {/* Tools Preview */}
                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                            Tools in this kit
                        </span>
                        <ExternalLink className="w-3 h-3 text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>

                    <ScrollArea className="h-20">
                        <div className="flex flex-wrap gap-1.5">
                            {toolkitTools.length > 0 ? (
                                toolkitTools.map(tool => (
                                    <Badge
                                        key={tool.id}
                                        variant="outline"
                                        className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 border-purple-500/20 text-purple-300 hover:bg-purple-500/20 transition-colors text-xs px-2 py-1"
                                    >
                                        {tool.name}
                                    </Badge>
                                ))
                            ) : (
                                <span className="text-xs text-gray-500 italic">No tools added yet</span>
                            )}
                        </div>
                    </ScrollArea>
                </div>

                {/* Tags */}
                {toolkit.tags && toolkit.tags.length > 0 && (
                    <div className="mt-4 pt-3 border-t border-gray-700/50">
                        <div className="flex flex-wrap gap-1">
                            {toolkit.tags.slice(0, 3).map((tag, index) => (
                                <span
                                    key={index}
                                    className="text-xs px-2 py-1 bg-gray-700/50 text-gray-400 rounded-md"
                                >
                                    {tag}
                                </span>
                            ))}
                            {toolkit.tags.length > 3 && (
                                <span className="text-xs px-2 py-1 bg-gray-700/50 text-gray-400 rounded-md">
                                    +{toolkit.tags.length - 3} more
                                </span>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};


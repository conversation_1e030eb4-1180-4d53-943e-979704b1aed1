export interface Course {
    id: string;
    title: string;
    description: string;
    thumbnail: string; // URL to course thumbnail
    videoUrl?: string; // Optional video URL
    modules: Module[];
    authorId: string;
    authorName: string;
    createdAt: string;
    updatedAt: string;
    estimatedDuration?: number; // In minutes
    totalLessons?: number;
    isDraft?: boolean;
}

export interface Module {
    id: string;
    courseId: string;
    title: string;
    order: number;
    description?: string;
    lessons: Lesson[];
}

export type LessonType = 'text' | 'text-image' | 'video-text' | 'mcq';

export interface Lesson {
    id: string;
    moduleId: string;
    type: LessonType;
    title: string;
    order: number;
    content: string; // Text content
    imageUrl?: string; // URL for text-image type
    videoUrl?: string; // URL or embed code for video-text type
    questions?: MCQQuestion[]; // Questions for MCQ type
}

export interface MCQQuestion {
    id: string;
    question: string;
    options: string[];
    correctOption: number;
    explanation?: string;
    imageUrl?: string;
}

export interface QuizAttempt {
    userId: string;
    lessonId: string;
    contentBlockId: string;
    selectedOption: number;
    isCorrect: boolean;
    timestamp: string;
}

export interface UserProgress {
    userId: string;
    courseId: string;
    status: 'not-started' | 'in-progress' | 'completed';
    completedLessons: { [lessonId: string]: boolean };
    completedModules: { [moduleId: string]: boolean };
    quizScores: { [quizId: string]: number };
    earnedXp: number;
    quizAttempts: QuizAttempt[];
    lastAccessedModuleId?: string;
    lastAccessedLessonId?: string;
    currentLesson: {
        moduleId: string;
        lessonId: string;
    };
    updatedAt: string;
    lastAccessedAt: string;
}

export interface CourseStats {
    totalStudents: number;
    completionRate: number;
    averageRating: number;
    totalCompletions: number;
    createdAt: string;
    updatedAt: string;
}

import { type FC, useEffect, useState, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { GlassCard } from '@/components/ui/glass-card';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useCourseCreation, CreationStep } from '@/hooks/useCourseCreation';
import { MediaUpload } from '@/components/course/MediaUpload';
import type { Course, LessonType, MCQQuestion } from '@/types/course';

// Simple notification fallback
const notify = (msg: string) => window.alert(msg);

const CourseEditor: FC = () => {
    const { courseId } = useParams();
    const navigate = useNavigate();
    const {
        state,
        setState,
        course,
        saveCourseDetails,
        saveModule,
        saveLesson,
        loading,
        error
    } = useCourseCreation(courseId);

    // Local state for editing
    const [localCourse, setLocalCourse] = useState<Course | null>(course);
    const [saving, setSaving] = useState(false);
    const [validation, setValidation] = useState<{ title?: string; description?: string }>({});
    const [newModuleTitle, setNewModuleTitle] = useState('');
    const [editingModuleTitles, setEditingModuleTitles] = useState<Record<string, string>>({});
    const [editingLessonTitles, setEditingLessonTitles] = useState<Record<string, string>>({});
    const [editingLessonContents, setEditingLessonContents] = useState<Record<string, string>>({});

    useEffect(() => {
        setLocalCourse(course);
    }, [course]);

    // Memoize current step label
    const stepLabels = ['Course Details', 'Modules', 'Lessons'];
    const currentStepLabel = stepLabels[state.step] || '';

    // Validate course details before save
    const validateCourseDetails = useCallback(() => {
        const errors: { title?: string; description?: string } = {};
        if (!localCourse?.title) errors.title = 'Title is required.';
        if (!localCourse?.description) errors.description = 'Description is required.';
        setValidation(errors);
        return Object.keys(errors).length === 0;
    }, [localCourse]);

    // Handle course field changes
    const handleCourseInputChange = useCallback((field: keyof Course, value: string) => {
        setLocalCourse(prev => prev ? { ...prev, [field]: value } : prev);
    }, []);

    // Save course details
    const handleCourseDetailsSubmit = useCallback(async () => {
        if (!validateCourseDetails()) return;
        setSaving(true);
        // Ensure we never pass null to saveCourseDetails
        const success = await saveCourseDetails(localCourse || {});
        setSaving(false);
        if (success) {
            notify('Course details saved.');
            setState(prev => ({ ...prev, step: CreationStep.ModuleCreation }));
        } else {
            notify('Failed to save course details.');
        }
    }, [localCourse, saveCourseDetails, setState, validateCourseDetails]);

    // Module logic
    const handleModuleTitleChange = useCallback((moduleId: string, value: string) => {
        setEditingModuleTitles(prev => ({ ...prev, [moduleId]: value }));
    }, []);
    const handleModuleTitleSave = useCallback(async (moduleId: string) => {
        const title = editingModuleTitles[moduleId];
        if (!title || !title.trim()) {
            notify('Module title required.');
            return;
        }
        setSaving(true);
        const module = course?.modules.find(m => m.id === moduleId);
        const success = await saveModule({ ...module, title });
        setSaving(false);
        if (success) {
            notify('Module updated.');
        } else {
            notify('Failed to update module.');
        }
    }, [editingModuleTitles, saveModule, course]);
    const handleAddModule = useCallback(async () => {
        if (!newModuleTitle.trim()) {
            notify('Module title required.');
            return;
        }
        setSaving(true);
        const success = await saveModule({ title: newModuleTitle });
        setSaving(false);
        if (success) {
            notify('Module added.');
            setNewModuleTitle('');
        } else {
            notify('Failed to add module.');
        }
    }, [newModuleTitle, saveModule]);

    // Lesson logic
    const handleLessonTitleChange = useCallback((lessonId: string, value: string) => {
        setEditingLessonTitles(prev => ({ ...prev, [lessonId]: value }));
    }, []);
    const handleLessonTitleSave = useCallback(async (moduleId: string, lessonId: string) => {
        const title = editingLessonTitles[lessonId];
        if (!title || !title.trim()) {
            notify('Lesson title required.');
            return;
        }
        setSaving(true);
        const module = course?.modules.find(m => m.id === moduleId);
        const lesson = module?.lessons.find(l => l.id === lessonId);
        const success = await saveLesson(moduleId, { ...lesson, title });
        setSaving(false);
        if (!success) notify('Failed to update lesson.');
    }, [editingLessonTitles, saveLesson, course]);
    const handleAddLesson = useCallback(async (moduleId: string) => {
        setSaving(true);
        const success = await saveLesson(moduleId, {
            title: 'New Lesson',
            type: 'text',
            content: '',
            moduleId
        });
        setSaving(false);
        if (!success) notify('Failed to add lesson.');
    }, [saveLesson]);
    const handleLessonContentChange = useCallback((lessonId: string, value: string) => {
        setEditingLessonContents(prev => ({ ...prev, [lessonId]: value }));
    }, []);
    const handleLessonContentSave = useCallback(async (moduleId: string, lessonId: string) => {
        const content = editingLessonContents[lessonId];
        if (content === undefined) return;
        setSaving(true);
        const module = course?.modules.find(m => m.id === moduleId);
        const lesson = module?.lessons.find(l => l.id === lessonId);
        const success = await saveLesson(moduleId, { ...lesson, content });
        setSaving(false);
        if (!success) notify('Failed to update lesson content.');
    }, [editingLessonContents, saveLesson, course]);

    // Render course details step
    const renderCourseDetailsStep = () => (
        <div className="space-y-6">
            <div className="mb-4">
                <h2 className="text-2xl font-semibold mb-1">Course Details</h2>
                <p className="text-gray-400 text-sm">Fill in the basic information for your course. Fields marked * are required.</p>
            </div>
            <div className="flex flex-col gap-2">
                <label className="font-medium">Title *</label>
                <Input
                    placeholder="Course Title"
                    value={localCourse?.title || ''}
                    onChange={e => handleCourseInputChange('title', e.target.value)}
                    disabled={saving}
                    aria-invalid={!!validation.title}
                    aria-describedby="course-title-error"
                />
                {validation.title && <span id="course-title-error" className="text-red-500 text-xs">{validation.title}</span>}
            </div>
            <div className="flex flex-col gap-2">
                <label className="font-medium">Description *</label>
                <Textarea
                    placeholder="Course Description"
                    value={localCourse?.description || ''}
                    onChange={e => handleCourseInputChange('description', e.target.value)}
                    disabled={saving}
                    aria-invalid={!!validation.description}
                    aria-describedby="course-description-error"
                />
                {validation.description && <span id="course-description-error" className="text-red-500 text-xs">{validation.description}</span>}
            </div>
            {localCourse?.id ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <MediaUpload
                        label="Thumbnail"
                        type="image"
                        courseId={localCourse.id}
                        value={localCourse?.thumbnail || ''}
                        onChange={url => handleCourseInputChange('thumbnail', url)}
                    />
                    <MediaUpload
                        label="Intro Video"
                        type="video"
                        allowYouTube
                        courseId={localCourse.id}
                        value={localCourse?.videoUrl || ''}
                        onChange={url => handleCourseInputChange('videoUrl', url)}
                    />
                </div>
            ) : (
                <div className="text-sm text-muted-foreground border border-dashed rounded p-4 bg-muted/10">
                    Please enter a course title and description, then click <b>Create Course & Continue</b> to enable media uploads.
                </div>
            )}
            <div className="flex gap-4 mt-4">
                <Button onClick={handleCourseDetailsSubmit} disabled={saving} aria-label="Save course details">
                    {state.courseId ? 'Update Details' : 'Create Course & Continue'}
                </Button>
                <Button variant="secondary" onClick={() => navigate('/admin/courses')} disabled={saving} aria-label="Cancel">
                    Cancel
                </Button>
            </div>
        </div>
    );

    // Render module step
    const renderModuleStep = () => (
        <div className="space-y-6">
            <div className="flex justify-between items-center mb-2">
                <h2 className="text-xl font-semibold">Course Modules</h2>
                <Button onClick={() => setState(prev => ({ ...prev, step: CreationStep.CourseDetails }))} aria-label="Edit course details">
                    Edit Course Details
                </Button>
            </div>
            <div className="space-y-2">
                {course?.modules.map(module => (
                    <GlassCard key={module.id} className="p-4 flex flex-col gap-2 border border-sortmy-blue/30">
                        <div className="flex gap-4 items-center">
                            <Input
                                className="flex-1"
                                placeholder="Module Title"
                                value={editingModuleTitles[module.id] ?? module.title}
                                onChange={e => handleModuleTitleChange(module.id, e.target.value)}
                                aria-label="Module title"
                                onBlur={() => handleModuleTitleSave(module.id)}
                            />
                            <Button
                                onClick={() => handleModuleTitleSave(module.id)}
                                disabled={saving}
                                aria-label="Save module title"
                                variant="outline"
                            >
                                Save
                            </Button>
                            <Button variant="destructive" disabled title="Delete not implemented">
                                Delete
                            </Button>
                            <Button onClick={() => setState(prev => ({ ...prev, step: CreationStep.LessonCreation, selectedModuleId: module.id }))} aria-label="Manage lessons">
                                Manage Lessons
                            </Button>
                        </div>
                    </GlassCard>
                ))}
            </div>
            <div className="flex gap-2 items-center mt-2">
                <Input
                    placeholder="New Module Title"
                    value={newModuleTitle}
                    onChange={e => setNewModuleTitle(e.target.value)}
                    onKeyDown={e => { if (e.key === 'Enter') handleAddModule(); }}
                    aria-label="New module title"
                />
                <Button onClick={handleAddModule} disabled={saving} aria-label="Add module">Add Module</Button>
            </div>
            <Button variant="secondary" onClick={() => navigate('/admin/courses')} aria-label="Finish and exit">
                Finish & Exit
            </Button>
        </div>
    );

    // Render lesson step
    const renderLessonStep = () => {
        const currentModule = course?.modules.find(m => m.id === state.selectedModuleId);
        if (!currentModule) return null;
        return (
            <div className="space-y-6">
                <div className="flex justify-between items-center mb-2">
                    <h2 className="text-xl font-semibold">Module: {currentModule.title}</h2>
                    <Button onClick={() => setState(prev => ({ ...prev, step: CreationStep.ModuleCreation }))} aria-label="Back to modules">
                        Back to Modules
                    </Button>
                </div>
                <div className="space-y-2">
                    {currentModule.lessons.map(lesson => (
                        <GlassCard key={lesson.id} className="p-4 space-y-4 border border-sortmy-blue/20">
                            <div className="flex gap-2 items-center">
                                <Input
                                    placeholder="Lesson Title"
                                    value={editingLessonTitles[lesson.id] ?? lesson.title}
                                    onChange={e => handleLessonTitleChange(lesson.id, e.target.value)}
                                    aria-label="Lesson title"
                                    onBlur={() => handleLessonTitleSave(currentModule.id, lesson.id)}
                                />
                                <Button
                                    onClick={() => handleLessonTitleSave(currentModule.id, lesson.id)}
                                    disabled={saving}
                                    aria-label="Save lesson title"
                                    variant="outline"
                                >
                                    Save
                                </Button>
                                <Button variant="destructive" disabled title="Delete not implemented">
                                    Delete
                                </Button>
                            </div>
                            <Select
                                value={lesson.type}
                                onValueChange={value => saveLesson(currentModule.id, { ...lesson, type: value as LessonType })}
                                aria-label="Lesson type"
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select lesson type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="text">Text Only</SelectItem>
                                    <SelectItem value="text-image">Text with Image</SelectItem>
                                    <SelectItem value="video-text">Video with Text</SelectItem>
                                    <SelectItem value="mcq">Multiple Choice Quiz</SelectItem>
                                </SelectContent>
                            </Select>
                            <div className="space-y-4">
                                {(lesson.type === 'text' || lesson.type === 'text-image' || lesson.type === 'video-text') && (
                                    <>
                                        <Textarea
                                            placeholder="Lesson Content"
                                            value={editingLessonContents[lesson.id] ?? lesson.content}
                                            onChange={e => handleLessonContentChange(lesson.id, e.target.value)}
                                            onBlur={() => handleLessonContentSave(currentModule.id, lesson.id)}
                                            aria-label="Lesson content"
                                        />
                                        <Button
                                            onClick={() => handleLessonContentSave(currentModule.id, lesson.id)}
                                            disabled={saving}
                                            aria-label="Save lesson content"
                                            variant="outline"
                                        >
                                            Save
                                        </Button>
                                    </>
                                )}
                                {lesson.type === 'text-image' && (
                                    <MediaUpload
                                        label="Lesson Image"
                                        type="image"
                                        courseId={course?.id}
                                        lessonId={lesson.id}
                                        value={lesson.imageUrl || ''}
                                        onChange={url => saveLesson(currentModule.id, { ...lesson, imageUrl: url })}
                                    />
                                )}
                                {lesson.type === 'video-text' && (
                                    <MediaUpload
                                        label="Lesson Video"
                                        type="video"
                                        allowYouTube
                                        courseId={course?.id}
                                        lessonId={lesson.id}
                                        value={lesson.videoUrl || ''}
                                        onChange={url => saveLesson(currentModule.id, { ...lesson, videoUrl: url })}
                                    />
                                )}
                                {lesson.type === 'mcq' && (
                                    <div className="space-y-4">
                                        {lesson.questions?.map((question, qIndex) => (
                                            <div key={qIndex} className="space-y-2">
                                                <Input
                                                    placeholder="Question"
                                                    value={question.question}
                                                    onChange={e => {
                                                        const updatedQuestions = [...(lesson.questions || [])];
                                                        updatedQuestions[qIndex] = { ...updatedQuestions[qIndex], question: e.target.value };
                                                        saveLesson(currentModule.id, { ...lesson, questions: updatedQuestions });
                                                    }}
                                                    aria-label={`Question ${qIndex + 1}`}
                                                />
                                                {question.options.map((option, oIndex) => (
                                                    <div key={oIndex} className="flex gap-2">
                                                        <Input
                                                            placeholder={`Option ${oIndex + 1}`}
                                                            value={option}
                                                            onChange={e => {
                                                                const updatedQuestions = [...(lesson.questions || [])];
                                                                updatedQuestions[qIndex].options[oIndex] = e.target.value;
                                                                saveLesson(currentModule.id, { ...lesson, questions: updatedQuestions });
                                                            }}
                                                            aria-label={`Option ${oIndex + 1}`}
                                                        />
                                                        <Button
                                                            variant="secondary"
                                                            onClick={() => {
                                                                const updatedQuestions = [...(lesson.questions || [])];
                                                                updatedQuestions[qIndex].correctOption = oIndex;
                                                                saveLesson(currentModule.id, { ...lesson, questions: updatedQuestions });
                                                            }}
                                                            aria-label={`Mark option ${oIndex + 1} as correct`}
                                                        >
                                                            {question.correctOption === oIndex ? '✓ Correct' : 'Mark Correct'}
                                                        </Button>
                                                    </div>
                                                ))}
                                            </div>
                                        ))}
                                        <Button onClick={() => {
                                            const newQuestion: MCQQuestion = {
                                                id: `question-${Date.now()}`,
                                                question: '',
                                                options: ['', '', '', ''],
                                                correctOption: 0
                                            };
                                            saveLesson(currentModule.id, { ...lesson, questions: [...(lesson.questions || []), newQuestion] });
                                        }} aria-label="Add question">
                                            Add Question
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </GlassCard>
                    ))}
                </div>
                <Button onClick={() => handleAddLesson(currentModule.id)} disabled={saving} aria-label="Add lesson">
                    Add Lesson
                </Button>
                <div className="flex gap-4 mt-4">
                    <Button onClick={() => setState(prev => ({ ...prev, step: CreationStep.ModuleCreation }))} aria-label="Back to modules">
                        Back to Modules
                    </Button>
                    <Button variant="secondary" onClick={() => navigate('/admin/courses')} aria-label="Finish and exit">
                        Finish & Exit
                    </Button>
                </div>
            </div>
        );
    };

    if (loading) return <div className="flex items-center justify-center h-64 text-lg">Loading...</div>;
    if (error) return <div className="text-red-500">Error: {error}</div>;

    // Main render
    return (
        <div className="container mx-auto p-4">
            <GlassCard className="p-6 max-w-3xl mx-auto">
                <div className="mb-6 flex items-center gap-4">
                    <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold">
                            {courseId ? 'Edit Course' : 'Create New Course'}
                        </span>
                        <span className="text-sm px-2 py-1 bg-sortmy-blue/10 text-sortmy-blue rounded-full font-semibold">
                            {currentStepLabel}
                        </span>
                    </div>
                    <div className="flex-1 h-px bg-sortmy-blue/10 mx-4" />
                    <span className="text-xs text-gray-400">Step {state.step + 1} of 3</span>
                </div>
                {/* Stepper UI - fixed JSX structure */}
                <div className="mb-8">
                    <div className="flex gap-2">
                        {stepLabels.map((label, idx) => (
                            <div key={label} className={`flex-1 flex flex-col items-center ${state.step === idx ? 'font-bold text-sortmy-blue' : 'text-gray-400'}`}>
                                <div className={`w-8 h-8 flex items-center justify-center rounded-full border-2 ${state.step === idx ? 'border-sortmy-blue bg-sortmy-blue/10' : 'border-gray-300 bg-gray-800'}`}>{idx + 1}</div>
                                <span className="text-xs mt-1">{label}</span>
                            </div>
                        ))}
                    </div>
                </div>
                {/* Render the current step */}
                {state.step === CreationStep.CourseDetails && renderCourseDetailsStep()}
                {state.step === CreationStep.ModuleCreation && renderModuleStep()}
                {state.step === CreationStep.LessonCreation && renderLessonStep()}
            </GlassCard>
        </div>
    );
};

export default CourseEditor;

import { useState, useEffect } from 'react';
import {
  Target,
  CheckCircle,
  Clock,
  Gift,
  Sparkles,
  Trophy,
  Flame,
  BookOpen,
  Plus,
  Share,
  Heart,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Expand
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { XPService } from '@/services/xpService';
import { MissionService, Mission, MissionTracker } from '@/services/missionService';
import { ActivityType } from '@/utils/xpSystem';
import { useNavigate } from 'react-router-dom';

interface MissionWithIcon extends Mission {
  icon: React.ReactNode;
}

const XPMissions = () => {
  const { user, refreshUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [missions, setMissions] = useState<MissionWithIcon[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedMilestone, setSelectedMilestone] = useState<MissionWithIcon | null>(null);
  const [panelPosition, setPanelPosition] = useState<'top' | 'bottom'>('top');

  // Get icon for mission based on activity type
  const getMissionIcon = (activityType: ActivityType): React.ReactNode => {
    switch (activityType) {
      case 'DAILY_LOGIN': return <Flame className="w-4 h-4" />;
      case 'ADD_TOOL_TO_LIBRARY': return <Plus className="w-4 h-4" />;
      case 'CREATE_FIRST_PORTFOLIO': return <Trophy className="w-4 h-4" />;
      case 'COMPLETE_LESSON': return <BookOpen className="w-4 h-4" />;
      case 'CREATE_TOOLKIT': return <Target className="w-4 h-4" />;
      case 'LIKE_POST': return <Heart className="w-4 h-4" />;
      case 'SHARE_TOOL': return <Share className="w-4 h-4" />;
      case 'STAY_ONLINE': return <Clock className="w-4 h-4" />;
      default: return <Target className="w-4 h-4" />;
    }
  };

  // Load missions from service
  const loadMissions = async () => {
    if (!user?.uid) {
      console.log('XPMissions: No user found');
      return;
    }

    console.log('XPMissions: Loading missions for user:', user.uid);
    setIsLoading(true);
    try {
      const userMissions = await MissionService.getUserMissions(user.uid);
      console.log('XPMissions: Loaded missions:', userMissions);
      console.log('XPMissions: Mission IDs:', userMissions.map(m => m.id));
      console.log('XPMissions: Time Keeper found:', userMissions.find(m => m.id === 'time_keeper_daily') ? 'YES' : 'NO');

      if (userMissions && userMissions.length > 0) {
        const missionsWithIcons: MissionWithIcon[] = userMissions.map(mission => ({
          ...mission,
          icon: getMissionIcon(mission.activityType)
        }));
        setMissions(missionsWithIcons);
        console.log('XPMissions: Set missions with icons:', missionsWithIcons);

        // Log completed missions for debugging
        const completedMissions = missionsWithIcons.filter(m => m.isCompleted && !m.isClaimed);
        console.log('XPMissions: Completed unclaimed missions:', completedMissions);
      } else {
        console.log('XPMissions: No missions returned from service');
        // Set empty array to show "no missions" state
        setMissions([]);
      }
    } catch (error) {
      console.error('XPMissions: Error loading missions:', error);
      // Set empty array to show error state
      setMissions([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      // Check for duplicate localStorage entries before loading
      clearDuplicateLocalStorage();
      loadMissions();
    }
  }, [isOpen, user?.uid]);

  // Track daily login and start time tracking when user is available
  useEffect(() => {
    if (user?.uid) {
      // Track daily login when user is available
      MissionTracker.dailyLogin(user.uid);
      // Start time tracking for stay online mission
      MissionTracker.startTimeTracking(user.uid);
    }

    // Cleanup time tracking when component unmounts or user changes
    return () => {
      if (user?.uid) {
        MissionTracker.stopTimeTracking(user.uid);
      }
    };
  }, [user?.uid]);

  // Detect panel position based on screen space
  useEffect(() => {
    if (isOpen && typeof window !== 'undefined') {
      const updatePosition = () => {
        const windowHeight = window.innerHeight;
        const buttonElement = document.querySelector('[data-xp-missions-button]');

        if (buttonElement) {
          const buttonRect = buttonElement.getBoundingClientRect();
          const spaceAbove = buttonRect.top;
          const spaceBelow = windowHeight - buttonRect.bottom;

          // If there's more space above and not enough space above for the panel, position below
          if (spaceAbove < 400 && spaceBelow > 200) {
            setPanelPosition('bottom');
          } else {
            setPanelPosition('top');
          }
        }
      };

      updatePosition();
      window.addEventListener('resize', updatePosition);
      return () => window.removeEventListener('resize', updatePosition);
    }
  }, [isOpen]);

  // Auto-refresh missions every 30 seconds when panel is open
  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      loadMissions();
    }, 30000);

    return () => clearInterval(interval);
  }, [isOpen, user?.uid]);

  const handleClaimReward = async (mission: MissionWithIcon) => {
    if (!user?.uid || !mission.isCompleted || mission.isClaimed) return;

    setIsLoading(true);
    try {
      console.log('XPMissions: Claiming reward for mission:', mission.id, mission.title);

      // Mark mission as claimed in the service
      const success = await MissionService.claimMission(user.uid, mission.id);
      if (!success) {
        throw new Error('Failed to claim mission');
      }

      console.log('XPMissions: Mission claimed successfully, awarding XP...');

      // Award XP through the service
      const result = await XPService.awardXP(
        user.uid,
        mission.activityType,
        `Claimed: ${mission.title}`,
        { missionId: mission.id }
      );

      // Update mission as claimed locally for immediate UI feedback
      setMissions(prev => prev.map(m =>
        m.id === mission.id ? { ...m, isClaimed: true } : m
      ));

      // Wait a moment for Firestore to update, then refresh user data
      setTimeout(async () => {
        await refreshUser();
        console.log('XPMissions: User data refreshed after XP award');
      }, 1000);

      // Refresh missions from server to ensure consistency
      setTimeout(() => {
        loadMissions();
      }, 500);

      console.log('XPMissions: Mission claimed, user data refreshed, and UI updated');

      // Show success toast
      toast({
        title: "XP Claimed! 🎉",
        description: `+${mission.xpReward} XP - ${mission.title}`,
      });

      // Show level up toast if applicable
      if (result.leveledUp) {
        toast({
          title: "Level Up! 🚀",
          description: `Congratulations! You've reached Level ${result.newLevel}!`,
        });
      }

    } catch (error) {
      console.error('Error claiming XP reward:', error);
      toast({
        title: "Error",
        description: "Failed to claim reward. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getCategoryColor = (category: MissionWithIcon['category']) => {
    switch (category) {
      case 'daily': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'weekly': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'achievement': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'milestone': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Filter missions by category
  const filteredMissions = missions.filter(mission =>
    selectedCategory === 'all' || mission.category.toLowerCase() === selectedCategory.toLowerCase()
  );



  // Pagination constants
  const MISSIONS_PER_PAGE = 3;
  const totalPages = Math.ceil(filteredMissions.length / MISSIONS_PER_PAGE);
  const startIndex = currentSlide * MISSIONS_PER_PAGE;
  const endIndex = startIndex + MISSIONS_PER_PAGE;
  const currentMissions = filteredMissions.slice(startIndex, endIndex);

  // Navigation functions
  const nextSlide = () => {
    if (totalPages > 1) {
      setCurrentSlide((prev) => (prev + 1) % totalPages);
    }
  };

  const prevSlide = () => {
    if (totalPages > 1) {
      setCurrentSlide((prev) => (prev - 1 + totalPages) % totalPages);
    }
  };

  // Reset slide when category changes
  useEffect(() => {
    setCurrentSlide(0);
  }, [selectedCategory]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        prevSlide();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        nextSlide();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, totalPages]);

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(missions.map(m => m.category.toLowerCase())))];

  const unclaimedCount = missions.filter(m => m.isCompleted && !m.isClaimed).length;

  // Test mission generation and tracking
  const testMissionGeneration = async () => {
    console.log('=== TESTING MISSION GENERATION ===');
    const allMissions = MissionService.generateAllMissions();
    console.log('Generated missions:', allMissions.length);
    console.log('Mission IDs:', allMissions.map(m => m.id));
    console.log('Daily missions:', allMissions.filter(m => m.category === 'daily').map(m => ({ id: m.id, title: m.title, activityType: m.activityType })));
    console.log('Milestone missions:', allMissions.filter(m => m.category === 'milestone').map(m => ({ id: m.id, title: m.title, activityType: m.activityType })));

    // Check specific missions
    console.log('LIKE_POST missions:', allMissions.filter(m => m.activityType === 'LIKE_POST'));
    console.log('CREATE_POST missions:', allMissions.filter(m => m.activityType === 'CREATE_POST'));
    console.log('ADD_TOOL_TO_LIBRARY missions:', allMissions.filter(m => m.activityType === 'ADD_TOOL_TO_LIBRARY'));
    console.log('CREATE_TOOLKIT missions:', allMissions.filter(m => m.activityType === 'CREATE_TOOLKIT'));

    // Test tracking
    if (user?.uid) {
      console.log('=== TESTING MISSION TRACKING ===');

      console.log('Testing Add Tool to Library...');
      MissionTracker.addToolToLibrary(user.uid);

      console.log('Testing Like Post...');
      MissionTracker.likePost(user.uid);

      console.log('Testing Create Post...');
      MissionTracker.createPost(user.uid);

      console.log('Testing Stay Online...');
      MissionTracker.stayOnline(user.uid);

      console.log('Testing Create Toolkit...');
      MissionTracker.createToolkit(user.uid);

      console.log('=== TESTING SPECIFIC MISSIONS ===');
      // Test specific mission tracking
      console.log('Testing LIKE_POST activity directly...');
      await MissionService.trackActivity(user.uid, 'LIKE_POST');

      console.log('Testing CREATE_POST activity directly...');
      await MissionService.trackActivity(user.uid, 'CREATE_POST');

      console.log('Testing ADD_TOOL_TO_LIBRARY activity directly...');
      await MissionService.trackActivity(user.uid, 'ADD_TOOL_TO_LIBRARY');

      console.log('Testing CREATE_TOOLKIT activity directly...');
      await MissionService.trackActivity(user.uid, 'CREATE_TOOLKIT');

      console.log('=== REFRESHING MISSIONS ===');
      setTimeout(() => {
        loadMissions();
      }, 1000);
    }

    console.log('=== END TEST ===');
  };

  // Debug function to manually trigger daily login
  const debugDailyLogin = async () => {
    if (!user?.uid) return;
    console.log('XPMissions: Manually triggering daily login...');
    console.log('XPMissions: Current missions before trigger:', missions);
    try {
      await MissionTracker.dailyLogin(user.uid);
      console.log('XPMissions: Daily login triggered, refreshing missions...');
      setTimeout(() => {
        loadMissions();
      }, 1000);
    } catch (error) {
      console.error('XPMissions: Error triggering daily login:', error);
    }
  };

  // Debug function to check localStorage
  const debugLocalStorage = () => {
    if (!user?.uid) return;
    console.log('XPMissions: Checking localStorage for mission data...');
    const dailyLoginKey = `mission_${user.uid}_daily_login`;
    const localData = localStorage.getItem(dailyLoginKey);
    console.log('XPMissions: Daily login localStorage data:', localData);
    if (localData) {
      try {
        const parsed = JSON.parse(localData);
        console.log('XPMissions: Parsed localStorage data:', parsed);
      } catch (e) {
        console.error('XPMissions: Error parsing localStorage data:', e);
      }
    }
  };

  // Debug function to manually complete daily login
  const forceCompleteDailyLogin = () => {
    if (!user?.uid) return;
    console.log('XPMissions: Force completing daily login mission...');
    const dailyLoginKey = `mission_${user.uid}_daily_login`;
    const localData = {
      userId: user.uid,
      missionId: 'daily_login',
      progress: 1,
      isCompleted: true,
      isClaimed: false,
      completedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    localStorage.setItem(dailyLoginKey, JSON.stringify(localData));
    console.log('XPMissions: Force completed daily login, refreshing...');
    setTimeout(() => {
      loadMissions();
    }, 500);
  };

  // Debug function to check XP and level calculation
  const debugXPAndLevel = async () => {
    if (!user?.uid) return;
    console.log('XPMissions: Debugging XP and level...');

    try {
      const progress = await XPService.getUserXPProgress(user.uid);
      console.log('XPMissions: Current XP progress:', progress);

      const { calculateLevel } = await import('@/utils/xpSystem');
      const calculatedLevel = calculateLevel(progress.totalXP);

      alert(`Current XP: ${progress.totalXP}\nStored Level: ${progress.level}\nCalculated Level: ${calculatedLevel}\nXP for next level: ${progress.xpForNextLevel}`);
    } catch (error) {
      console.error('XPMissions: Error checking XP and level:', error);
      alert('❌ Error checking XP and level: ' + error);
    }
  };

  // Debug function to test Firestore permissions
  const testFirestorePermissions = async () => {
    if (!user?.uid) return;
    console.log('XPMissions: Testing Firestore permissions...');

    try {
      // First test if rules deployment is working
      console.log('XPMissions: Testing rules deployment...');
      const deploymentTest = await MissionService.testRulesDeployment();

      if (!deploymentTest) {
        alert('❌ Rules deployment test FAILED - Rules may not be deployed correctly');
        return;
      }

      console.log('XPMissions: Rules deployment OK, testing mission permissions...');
      const result = await MissionService.testFirestorePermissions(user.uid);
      console.log('XPMissions: Firestore permissions test result:', result);

      if (result) {
        alert('✅ Firestore permissions test PASSED');
      } else {
        alert('❌ Firestore permissions test FAILED - Check console for details');
      }
    } catch (error) {
      console.error('XPMissions: Error testing Firestore permissions:', error);
      alert('❌ Firestore permissions test ERROR: ' + error);
    }
  };

  // Debug function to clear old localStorage and trigger fresh daily login
  const clearAndRetriggerDailyLogin = () => {
    if (!user?.uid) return;
    console.log('XPMissions: Clearing old localStorage and retriggering daily login...');

    // Clear old localStorage data
    const dailyLoginKey = `mission_${user.uid}_daily_login`;
    localStorage.removeItem(dailyLoginKey);
    console.log('XPMissions: Cleared localStorage key:', dailyLoginKey);

    // Trigger daily login mission
    MissionTracker.dailyLogin(user.uid);
    console.log('XPMissions: Triggered daily login mission');

    // Refresh missions after a short delay
    setTimeout(() => {
      loadMissions();
    }, 1000);
  };

  // Function to clear all duplicate localStorage entries for today
  const clearDuplicateLocalStorage = () => {
    if (!user?.uid) return;

    const today = new Date().toISOString().split('T')[0];
    const dailyLoginKey = `mission_${user.uid}_daily_login`;

    try {
      const existingData = localStorage.getItem(dailyLoginKey);
      if (existingData) {
        const parsed = JSON.parse(existingData);
        const completedDate = parsed.completedAt ? new Date(parsed.completedAt).toISOString().split('T')[0] : null;

        // If already completed today and claimed, don't allow re-completion
        if (completedDate === today && parsed.isClaimed) {
          console.log('XPMissions: Daily login already completed and claimed today, preventing duplicates');
          return true; // Already done today
        }
      }
    } catch (error) {
      console.warn('Error checking localStorage:', error);
    }

    return false; // Not done today
  };

  // Handle milestone mission click to show detailed view
  const handleMilestoneClick = (mission: MissionWithIcon) => {
    if ((mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily') && mission.milestoneInfo) {
      setSelectedMilestone(mission);
    }
  };

  // Close milestone detail view
  const closeMilestoneDetail = () => {
    setSelectedMilestone(null);
  };

  return (
    <div className="relative">
      {/* Trigger Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="h-5 w-5 p-0 hover:bg-sortmy-blue/10 relative"
        data-xp-missions-button
      >
        <Target className="h-4 w-4 text-sortmy-blue" />
        {unclaimedCount > 0 && (
          <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-[8px] font-bold text-white">{unclaimedCount}</span>
          </div>
        )}
      </Button>

      {/* Missions Panel */}
      {isOpen && (
        <div className={`absolute right-0 w-80 max-h-[70vh] bg-sortmy-darker/95 backdrop-blur-md border border-sortmy-blue/20 rounded-lg shadow-xl z-50 overflow-hidden
                      max-sm:w-[calc(100vw-2rem)] max-sm:max-w-sm md:w-80 ${panelPosition === 'top'
            ? 'bottom-[calc(100%+0.5rem)]'
            : 'top-[calc(100%+0.5rem)]'
          }`}>
          <div className="p-4 border-b border-sortmy-blue/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-sortmy-blue" />
                <h3 className="font-semibold text-white">XP Missions</h3>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/missions')}
                  className="h-6 w-6 p-0 text-gray-400 hover:text-white"
                  title="Expand to full page"
                >
                  <Expand className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={debugXPAndLevel}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-purple-400"
                  title="Debug: Check XP and level calculation"
                >
                  📊
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={testFirestorePermissions}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-blue-400"
                  title="Debug: Test Firestore permissions"
                >
                  🔐
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={debugDailyLogin}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-green-400"
                  title="Debug: Trigger daily login"
                >
                  🔧
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={debugLocalStorage}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-yellow-400"
                  title="Debug: Check localStorage"
                >
                  💾
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={forceCompleteDailyLogin}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-purple-400"
                  title="Debug: Force complete daily login"
                >
                  ⚡
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAndRetriggerDailyLogin}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-green-400"
                  title="Debug: Clear old data and retrigger daily login"
                >
                  🔄
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={testMissionGeneration}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-blue-400"
                  title="Debug: Test mission generation and tracking"
                >
                  🧪
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => user?.uid && MissionTracker.addToolToLibrary(user.uid)}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-green-400"
                  title="Test: Add Tool to Library"
                >
                  ➕
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (user?.uid) {
                      console.log('🎯 TEST: Manually triggering like post mission');
                      MissionTracker.likePost(user.uid);
                      // Refresh missions to see update
                      setTimeout(() => loadMissions(), 500);
                    }
                  }}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-pink-400"
                  title="Test: Like Post Mission"
                >
                  ❤️
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => user?.uid && MissionTracker.createToolkit(user.uid)}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-yellow-400"
                  title="Test: Create Toolkit"
                >
                  📦
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (user?.uid) {
                      console.log('🎯 TEST: Manually triggering create post mission');
                      MissionTracker.createPost(user.uid);
                      // Refresh missions to see update
                      setTimeout(() => loadMissions(), 500);
                    }
                  }}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-purple-400"
                  title="Test: Create Post Mission"
                >
                  📝
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (user?.uid) {
                      console.log('🔍 CURRENT LOCALSTORAGE DATA:');
                      const missionKeys = [];
                      for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && key.includes('mission_')) {
                          const value = localStorage.getItem(key);
                          console.log(`${key}: ${value}`);
                          missionKeys.push(key);
                        }
                      }
                      console.log(`Total mission keys: ${missionKeys.length}`);
                    }
                  }}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-blue-400"
                  title="Show localStorage data"
                >
                  🔍
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (user?.uid) {
                      // Clear all mission progress
                      const keysToRemove = [];
                      for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && (key.startsWith(`mission_${user.uid}_`) || key.startsWith(`total_`))) {
                          keysToRemove.push(key);
                        }
                      }
                      keysToRemove.forEach(key => localStorage.removeItem(key));

                      refreshUser();
                      loadMissions();
                      alert(`Cleared ${keysToRemove.length} mission and progress data items`);


                    }
                  }}
                  disabled={isLoading}
                  className="h-6 w-6 p-0 text-red-400"
                  title="Debug: Clear all mission and progress data"
                >
                  🗑️
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={loadMissions}
                  disabled={isLoading}
                  className="h-6 w-6 p-0"
                  title="Refresh missions"
                >
                  <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="h-6 w-6 p-0"
                >
                  ×
                </Button>
              </div>
            </div>
          </div>

          {/* Category Filter Tabs */}
          <div className="px-4 py-2 border-b border-sortmy-blue/10">
            <div className="flex items-center justify-between mb-2">
              <div className="flex gap-2 overflow-x-auto">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className={`text-xs whitespace-nowrap ${selectedCategory === category
                      ? 'bg-sortmy-blue text-white'
                      : 'text-gray-400 hover:text-white'
                      }`}
                  >
                    {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
                  </Button>
                ))}
              </div>
              {filteredMissions.length > 0 && (
                <div className="text-xs text-gray-400 whitespace-nowrap ml-2">
                  {filteredMissions.length} mission{filteredMissions.length !== 1 ? 's' : ''}
                </div>
              )}
            </div>
          </div>

          <ScrollArea className="max-h-[45vh] min-h-[200px]">
            <div className="p-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-sm text-gray-400">Loading missions...</div>
                </div>
              ) : filteredMissions.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Target className="w-8 h-8 text-gray-400 mb-2" />
                  <div className="text-sm text-gray-400 mb-1">
                    {selectedCategory === 'all' ? 'No missions available' : `No ${selectedCategory} missions`}
                  </div>
                  <div className="text-xs text-gray-500">Check back later for new challenges!</div>
                </div>
              ) : (
                <>
                  {/* Slider Navigation Header */}
                  {totalPages > 1 && (
                    <div className="flex items-center justify-between mb-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={prevSlide}
                        className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <div className="flex gap-1">
                        {Array.from({ length: totalPages }, (_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full transition-colors ${i === currentSlide ? 'bg-sortmy-blue' : 'bg-gray-600'
                              }`}
                          />
                        ))}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={nextSlide}
                        className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  {/* Current Missions */}
                  <div className="transition-all duration-300 ease-in-out">
                    {currentMissions.map((mission) => (
                      <div
                        key={mission.id}
                        className={`bg-sortmy-gray/20 rounded-lg p-3 border border-sortmy-blue/10 mb-3 last:mb-0 transform transition-all duration-200 hover:scale-105 hover:border-sortmy-blue/20 ${mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily' ? 'cursor-pointer' : ''
                          }`}
                        onClick={() => (mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily') ? handleMilestoneClick(mission) : undefined}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="text-sortmy-blue">
                              {mission.icon}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h4 className="text-sm font-medium text-white">
                                  {mission.title}
                                </h4>
                                {(mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily') && (
                                  <span className="text-xs text-sortmy-blue">📋 Click for details</span>
                                )}
                              </div>
                              <p className="text-xs text-gray-400">
                                {mission.description}
                              </p>
                              {mission.milestoneInfo?.nextTarget && (
                                <p className="text-xs text-sortmy-blue/70 mt-1">
                                  Next: {mission.milestoneInfo.nextTarget} items ({mission.milestoneInfo.nextXP} XP)
                                </p>
                              )}
                            </div>
                          </div>
                          <Badge
                            variant="outline"
                            className={`text-xs ${getCategoryColor(mission.category)}`}
                          >
                            {mission.category}
                          </Badge>
                        </div>

                        {/* Progress Bar */}
                        <div className="mb-2">
                          <div className="flex justify-between text-xs text-gray-400 mb-1">
                            <span>Progress: {mission.progress}/{mission.maxProgress}</span>
                            <span>{mission.xpReward} XP</span>
                          </div>
                          <div className="w-full bg-sortmy-gray/30 rounded-full h-1.5">
                            <div
                              className="bg-sortmy-blue h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${(mission.progress / mission.maxProgress) * 100}%` }}
                            />
                          </div>
                        </div>

                        {/* Action Button */}
                        <div className="flex justify-end">
                          {mission.isClaimed ? (
                            <Badge variant="outline" className="text-green-400 border-green-500/30">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Claimed
                            </Badge>
                          ) : mission.isCompleted ? (
                            <Button
                              size="sm"
                              onClick={() => handleClaimReward(mission)}
                              disabled={isLoading}
                              className="bg-sortmy-blue hover:bg-sortmy-blue/80 text-white text-xs h-6"
                            >
                              <Gift className="w-3 h-3 mr-1" />
                              Claim {mission.xpReward} XP
                            </Button>
                          ) : (
                            <Badge variant="outline" className="text-gray-400 border-gray-500/30">
                              <Clock className="w-3 h-3 mr-1" />
                              In Progress
                            </Badge>
                          )}
                        </div>

                        {/* Expiry Timer for Daily/Weekly missions */}
                        {mission.expiresAt && (
                          <div className="text-xs text-gray-500 mt-1">
                            Expires: {new Date(mission.expiresAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          </ScrollArea>
        </div>
      )
      }

      {/* Milestone Detail Modal */}
      {
        selectedMilestone && selectedMilestone.milestoneInfo && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-sortmy-dark border border-sortmy-blue/20 rounded-lg max-w-md w-full max-h-[80vh] overflow-hidden">
              {/* Header */}
              <div className="p-4 border-b border-sortmy-blue/20">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="text-sortmy-blue">
                      {selectedMilestone.icon}
                    </div>
                    <h3 className="text-lg font-semibold text-white">
                      {selectedMilestone.title}
                    </h3>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={closeMilestoneDetail}
                    className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                  >
                    ✕
                  </Button>
                </div>
              </div>

              {/* Content */}
              <ScrollArea className="max-h-[60vh]">
                <div className="p-4">
                  {/* Current Progress */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-white mb-2">Current Progress</h4>
                    <div className="bg-sortmy-gray/20 rounded-lg p-3">
                      <div className="flex justify-between text-sm text-gray-400 mb-2">
                        <span>Progress: {selectedMilestone.progress}/{selectedMilestone.maxProgress}</span>
                        <span className="text-sortmy-blue">{selectedMilestone.xpReward} XP</span>
                      </div>
                      <div className="w-full bg-sortmy-gray/30 rounded-full h-2">
                        <div
                          className="bg-sortmy-blue h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(selectedMilestone.progress / selectedMilestone.maxProgress) * 100}%` }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* All Milestones */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-2">All Milestones</h4>
                    <div className="space-y-2">
                      {selectedMilestone.milestoneInfo.detailedMilestones.map((milestone, index) => {
                        const isCompleted = selectedMilestone.progress >= milestone.target;
                        const isCurrent = !isCompleted && (index === 0 || selectedMilestone.progress >= selectedMilestone.milestoneInfo!.detailedMilestones[index - 1].target);

                        return (
                          <div
                            key={index}
                            className={`p-3 rounded-lg border ${isCompleted
                              ? 'bg-green-500/10 border-green-500/30'
                              : isCurrent
                                ? 'bg-sortmy-blue/10 border-sortmy-blue/30'
                                : 'bg-sortmy-gray/10 border-gray-500/20'
                              }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <span className={`text-xs px-2 py-1 rounded ${isCompleted
                                  ? 'bg-green-500/20 text-green-400'
                                  : isCurrent
                                    ? 'bg-sortmy-blue/20 text-sortmy-blue'
                                    : 'bg-gray-500/20 text-gray-400'
                                  }`}>
                                  {milestone.tier}
                                </span>
                                <span className={`text-sm ${isCompleted ? 'text-green-400' : isCurrent ? 'text-white' : 'text-gray-400'
                                  }`}>
                                  {milestone.description}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className={`text-sm font-medium ${isCompleted ? 'text-green-400' : isCurrent ? 'text-sortmy-blue' : 'text-gray-400'
                                  }`}>
                                  {milestone.xp} XP
                                </span>
                                {isCompleted && <CheckCircle className="w-4 h-4 text-green-400" />}
                                {isCurrent && <Clock className="w-4 h-4 text-sortmy-blue" />}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </div>
          </div>
        )
      }
    </div >
  );
};

export default XPMissions;

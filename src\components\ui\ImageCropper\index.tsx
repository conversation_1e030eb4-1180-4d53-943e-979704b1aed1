import { useState, useRef, useEffect } from 'react';
import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';

interface ImageSettings {
    size?: 'contain' | 'cover' | '85%' | '75%' | '50%';
    position?: 'center' | 'top' | 'bottom' | 'left' | 'right';
    padding?: number;
    crop?: Crop;
    scale?: number;
    rotate?: number;
}

interface ImageCropperProps {
    imageUrl: string;
    onCropComplete: (originalUrl: string, settings: ImageSettings) => void;
    initialSettings?: ImageSettings;
    onCancel: () => void;
}

const ImageCropper: React.FC<ImageCropperProps> = ({
    imageUrl,
    onCropComplete,
    initialSettings,
    onCancel,
}) => {
    const [crop, setCrop] = useState<Crop>(() => ({
        unit: '%', // Always use percentage
        width: initialSettings?.crop?.width || 100,
        height: initialSettings?.crop?.height || 100,
        x: initialSettings?.crop?.x || 0,
        y: initialSettings?.crop?.y || 0
    }));
    const [scale, setScale] = useState(initialSettings?.scale || 1);
    const [rotate, setRotate] = useState(initialSettings?.rotate || 0);
    const [position, setPosition] = useState<'center' | 'top' | 'bottom' | 'left' | 'right'>(
        initialSettings?.position || 'center'
    );
    const [padding, setPadding] = useState(initialSettings?.padding || 0);
    const [error, setError] = useState<string | null>(null);
    const [loadedImage, setLoadedImage] = useState<string | null>(null);
    const imgRef = useRef<HTMLImageElement>(null);

    // List of CORS proxies to try in order
    const corsProxies = [
        (url: string) => url, // Try original URL first
        (url: string) => `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`,
        (url: string) => `https://cors-anywhere.herokuapp.com/${url}`,
        (url: string) => `https://cors.bridged.cc/${url}`,
        (url: string) => `https://crossorigin.me/${url}`
    ];

    // Load and verify image with multiple fallbacks
    useEffect(() => {
        let mounted = true;
        let currentProxyIndex = 0;

        const loadImage = async () => {
            if (!mounted) return;

            try {
                // Skip proxy if it's a data URL
                if (imageUrl.startsWith('data:')) {
                    setLoadedImage(imageUrl);
                    return;
                }

                // Try each proxy in sequence
                while (currentProxyIndex < corsProxies.length) {
                    try {
                        const proxyUrl = corsProxies[currentProxyIndex](imageUrl);
                        const img = new Image();
                        img.crossOrigin = 'anonymous';

                        await new Promise((resolve, reject) => {
                            img.onload = () => {
                                // Test if the image can be used on canvas
                                const testCanvas = document.createElement('canvas');
                                const ctx = testCanvas.getContext('2d');
                                if (!ctx) {
                                    reject(new Error('Could not get canvas context'));
                                    return;
                                }

                                testCanvas.width = img.width;
                                testCanvas.height = img.height;

                                try {
                                    ctx.drawImage(img, 0, 0);
                                    // Try to access image data to verify it's not tainted
                                    testCanvas.toDataURL();
                                    resolve(img);
                                } catch (e) {
                                    reject(new Error('Canvas tainted'));
                                }
                            };
                            img.onerror = reject;
                            img.src = proxyUrl;
                        });

                        // If we get here, the image loaded successfully and is usable
                        if (mounted) {
                            setLoadedImage(proxyUrl);
                        }
                        return;
                    } catch (err) {
                        console.warn(`Proxy ${currentProxyIndex} failed:`, err);
                        currentProxyIndex++;
                    }
                }

                // If we get here, all proxies failed
                throw new Error('All CORS proxies failed');
            } catch (err) {
                console.error('Error loading image:', err);
                if (mounted) {
                    // Fall back to using the original URL if all else fails
                    setLoadedImage(imageUrl);
                }
            }
        };

        loadImage();

        return () => {
            mounted = false;
        };
    }, [imageUrl]);

    const handleSave = async () => {
        if (!imgRef.current) {
            setError('Image not loaded properly. Please try again.');
            return;
        }

        try {
            // Create a canvas with the original image
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                throw new Error('Could not get canvas context');
            }

            // Create a new Image instance to test canvas operations
            const testImg = new Image();
            testImg.crossOrigin = 'anonymous';

            await new Promise((resolve, reject) => {
                testImg.onload = resolve;
                testImg.onerror = reject;
                testImg.src = loadedImage || imageUrl;
            });

            // Set canvas size to match the image
            canvas.width = testImg.naturalWidth;
            canvas.height = testImg.naturalHeight;

            // Try to draw the image and test if the canvas is tainted
            try {
                ctx.drawImage(testImg, 0, 0);
                canvas.toDataURL(); // This will throw if canvas is tainted
            } catch (e) {
                // Canvas is tainted, fall back to using original URL with settings only
                console.warn('Canvas is tainted, falling back to settings-only mode');
                const settings: ImageSettings = {
                    size: crop.width <= 50 ? '50%' :
                        crop.width <= 75 ? '75%' :
                            crop.width <= 85 ? '85%' :
                                crop.width === 100 ? 'cover' : 'contain',
                    position,
                    padding,
                    crop,
                    scale,
                    rotate
                };
                onCropComplete(imageUrl, settings);
                return;
            }

            // If we reach here, canvas is not tainted
            const settings: ImageSettings = {
                size: crop.width <= 50 ? '50%' :
                    crop.width <= 75 ? '75%' :
                        crop.width <= 85 ? '85%' :
                            crop.width === 100 ? 'cover' : 'contain',
                position,
                padding,
                crop,
                scale,
                rotate
            };

            onCropComplete(imageUrl, settings);
        } catch (e) {
            console.error('Error saving settings:', e);
            // Fall back to settings-only mode if any error occurs
            const settings: ImageSettings = {
                size: crop.width <= 50 ? '50%' :
                    crop.width <= 75 ? '75%' :
                        crop.width <= 85 ? '85%' :
                            crop.width === 100 ? 'cover' : 'contain',
                position,
                padding,
                crop,
                scale,
                rotate
            };
            onCropComplete(imageUrl, settings);
        }
    };

    if (error) {
        return (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4">
                <div className="max-w-md w-full bg-sortmy-darker rounded-lg shadow-xl p-6">
                    <div className="text-red-400 text-center mb-4">{error}</div>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={onCancel}>Close</Button>
                        <Button onClick={() => window.location.reload()}>Retry</Button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4">
            <div className="max-w-4xl w-full bg-sortmy-darker rounded-lg shadow-xl overflow-hidden">
                <div className="p-4">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-white">Edit Image</h3>
                        <div className="space-x-2">
                            <Button variant="outline" onClick={onCancel}>
                                Cancel
                            </Button>
                            <Button onClick={handleSave}>
                                Save
                            </Button>
                        </div>
                    </div>

                    <div className="grid grid-cols-[2fr,1fr] gap-4">
                        <div className="relative bg-black/50 rounded-lg overflow-hidden">
                            <ReactCrop
                                crop={crop}
                                onChange={(c) => setCrop(c)}
                                className="max-h-[600px]"
                            >
                                {loadedImage && (
                                    <img
                                        ref={imgRef}
                                        src={loadedImage}
                                        alt="Crop preview"
                                        className="max-w-full transition-all duration-200"
                                        crossOrigin="anonymous"
                                        style={{
                                            transform: `scale(${scale}) rotate(${rotate}deg)`,
                                            transformOrigin: position,
                                            padding: `${padding}px`,
                                        }}
                                    />
                                )}
                            </ReactCrop>
                        </div>

                        <div className="space-y-6 p-4 bg-sortmy-dark/50 rounded-lg">
                            <div className="space-y-2">
                                <Label>Zoom</Label>
                                <Slider
                                    value={[scale * 100]}
                                    onValueChange={([value]) => setScale(value / 100)}
                                    min={50}
                                    max={200}
                                    step={1}
                                />
                                <div className="text-xs text-gray-400 text-right">{Math.round(scale * 100)}%</div>
                            </div>

                            <div className="space-y-2">
                                <Label>Rotation</Label>
                                <Slider
                                    value={[rotate]}
                                    onValueChange={([value]) => setRotate(value)}
                                    min={-180}
                                    max={180}
                                    step={1}
                                />
                                <div className="text-xs text-gray-400 text-right">{rotate}°</div>
                            </div>

                            <div className="space-y-2">
                                <Label>Padding</Label>
                                <Slider
                                    value={[padding]}
                                    onValueChange={([value]) => setPadding(value)}
                                    min={0}
                                    max={20}
                                    step={1}
                                />
                                <div className="text-xs text-gray-400 text-right">{padding}px</div>
                            </div>

                            <div className="space-y-2">
                                <Label>Position</Label>
                                <div className="grid grid-cols-3 gap-2">
                                    {(['top', 'center', 'bottom', 'left', 'right'] as const).map((pos) => (
                                        <Button
                                            key={pos}
                                            variant={position === pos ? 'default' : 'outline'}
                                            onClick={() => setPosition(pos)}
                                            className="text-xs"
                                        >
                                            {pos}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ImageCropper;


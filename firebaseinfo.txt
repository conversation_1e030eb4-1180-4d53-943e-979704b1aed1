// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyCSSBKFkrnBoK0b1Y3RmA97WdwcY9YLKcA",
  authDomain: "smai-og.firebaseapp.com",
  projectId: "smai-og",
  storageBucket: "smai-og.firebasestorage.app",
  messagingSenderId: "220186510992",
  appId: "1:220186510992:web:3d9e07c3df55d1f4ea7a15",
  measurementId: "G-4MR0WK595H"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
{
  "project_info": {
    "project_number": "220186510992",
    "project_id": "smai-og",
    "storage_bucket": "smai-og.firebasestorage.app"
  },
  "client": [
    {
      "client_info": {
        "mobilesdk_app_id": "1:220186510992:android:087c4c2569c7d402ea7a15",
        "android_client_info": {
          "package_name": "com.sortmyai.sortmyai"
        }
      },
      "oauth_client": [],
      "api_key": [
        {
          "current_key": "AIzaSyA9PrgqnVayDLgK7pgJC6uEAUb8y3ENfiM"
        }
      ],
      "services": {
        "appinvite_service": {
          "other_platform_oauth_client": []
        }
      }
    }
  ],
  "configuration_version": "1"
}id 'com.google.gms.google-services' version '4.4.2' apply false


plugins {
  id 'com.android.application'

  // Add the Google services Gradle plugin
  id 'com.google.gms.google-services'

  ...
}

dependencies {
  // Import the Firebase BoM
  implementation platform('com.google.firebase:firebase-bom:33.12.0')


  // TODO: Add the dependencies for Firebase products you want to use
  // When using the BoM, don't specify versions in Firebase dependencies
  implementation 'com.google.firebase:firebase-analytics'


  // Add the dependencies for any other desired Firebase products
  // https://firebase.google.com/docs/android/setup#available-libraries
}
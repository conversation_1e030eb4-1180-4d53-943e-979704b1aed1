import { useState, useRef } from 'react';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
// Removed unused Input import
import { useToast } from '@/hooks/use-toast';
import { Upload, X, Image as ImageIcon } from 'lucide-react';

interface ImageUploadDraft {
    fileName: string;
    fileSize: number;
    lastProgress: number;
    storageRef?: string;
    timestamp: number;
}

interface FirebaseImageUploaderProps {
    userId: string;
    onUploadComplete: (url: string, metadata?: { fileName: string; size: number }) => void;
    onError: (error: string) => void;
    maxSizeInMB?: number;
    allowedTypes?: string[];
    storagePath?: string; // Custom storage path
    className?: string;
}

export function FirebaseImageUploader({
    userId,
    onUploadComplete,
    onError,
    maxSizeInMB = 10,
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    storagePath = 'portfolio',
    className = ''
}: FirebaseImageUploaderProps) {
    const [uploading, setUploading] = useState(false);
    const [progress, setProgress] = useState(0);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [draft, setDraft] = useState<ImageUploadDraft | null>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();

    // Load draft on component mount
    useState(() => {
        const savedDraft = localStorage.getItem(`imageUploadDraft_${userId}`);
        if (savedDraft) {
            try {
                const parsedDraft = JSON.parse(savedDraft);
                // Only load drafts that are less than 1 hour old
                if (Date.now() - parsedDraft.timestamp < 60 * 60 * 1000) {
                    setDraft(parsedDraft);
                } else {
                    localStorage.removeItem(`imageUploadDraft_${userId}`);
                }
            } catch (e) {
                console.error('Error loading image draft:', e);
                localStorage.removeItem(`imageUploadDraft_${userId}`);
            }
        }
    });

    const saveDraft = (draftData: Partial<ImageUploadDraft>) => {
        if (!selectedFile) return;

        const newDraft: ImageUploadDraft = {
            fileName: selectedFile.name,
            fileSize: selectedFile.size,
            lastProgress: progress,
            timestamp: Date.now(),
            ...draftData
        };

        localStorage.setItem(`imageUploadDraft_${userId}`, JSON.stringify(newDraft));
        setDraft(newDraft);
    };

    const clearDraft = () => {
        localStorage.removeItem(`imageUploadDraft_${userId}`);
        setDraft(null);
    };

    const validateFile = (file: File): string | null => {
        if (!allowedTypes.includes(file.type)) {
            return `File type not supported. Allowed types: ${allowedTypes.join(', ')}`;
        }

        if (file.size > maxSizeInMB * 1024 * 1024) {
            return `File size too large. Maximum size: ${maxSizeInMB}MB`;
        }

        return null;
    };

    const compressImage = async (file: File): Promise<File> => {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions (max 1920x1080)
                let { width, height } = img;
                const maxWidth = 1920;
                const maxHeight = 1080;

                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                ctx?.drawImage(img, 0, 0, width, height);

                canvas.toBlob(
                    (blob) => {
                        if (blob) {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg',
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        } else {
                            resolve(file);
                        }
                    },
                    'image/jpeg',
                    0.8 // 80% quality
                );
            };

            img.src = URL.createObjectURL(file);
        });
    };

    const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        const validationError = validateFile(file);
        if (validationError) {
            onError(validationError);
            return;
        }

        setSelectedFile(file);
        clearDraft();

        // Create preview
        const preview = URL.createObjectURL(file);
        setPreviewUrl(preview);
    };

    const handleUpload = async () => {
        if (!selectedFile) return;

        try {
            setUploading(true);
            setProgress(10);

            // Compress image if it's large
            let fileToUpload = selectedFile;
            if (selectedFile.size > 2 * 1024 * 1024) { // 2MB
                fileToUpload = await compressImage(selectedFile);
                setProgress(20);
            }

            // Generate unique filename
            const timestamp = Date.now();
            const randomId = Math.random().toString(36).substring(2);
            const fileExtension = fileToUpload.name.split('.').pop();
            const fileName = `${timestamp}-${randomId}.${fileExtension}`;

            // Create storage reference
            const storageRef = ref(storage, `${storagePath}/${userId}/${fileName}`);

            // Save draft with storage reference
            saveDraft({ storageRef: storageRef.fullPath });
            setProgress(30);

            // Upload file with metadata
            const metadata = {
                contentType: fileToUpload.type,
                customMetadata: {
                    userId: userId,
                    originalName: selectedFile.name,
                    uploadedAt: new Date().toISOString(),
                    compressed: fileToUpload !== selectedFile ? 'true' : 'false'
                }
            };

            const uploadResult = await uploadBytes(storageRef, fileToUpload, metadata);
            setProgress(80);

            // Get download URL
            const downloadUrl = await getDownloadURL(uploadResult.ref);
            setProgress(100);

            // Clear draft and notify success
            clearDraft();
            onUploadComplete(downloadUrl, {
                fileName: fileName,
                size: fileToUpload.size
            });

            // Reset component state
            setSelectedFile(null);
            setPreviewUrl(null);
            setProgress(0);

            toast({
                title: 'Success',
                description: 'Image uploaded successfully!'
            });

        } catch (error) {
            console.error('Upload error:', error);
            onError(error instanceof Error ? error.message : 'Upload failed');
        } finally {
            setUploading(false);
        }
    };

    const handleRemoveFile = () => {
        setSelectedFile(null);
        setPreviewUrl(null);
        clearDraft();
        if (inputRef.current) {
            inputRef.current.value = '';
        }
    };

    return (
        <Card className={`p-4 ${className}`}>
            <div className="space-y-4">
                <input
                    type="file"
                    accept={allowedTypes.join(',')}
                    onChange={handleFileSelect}
                    className="hidden"
                    ref={inputRef}
                />

                {draft && (
                    <div className="text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                        Draft available: {draft.fileName} ({Math.round(draft.lastProgress)}% complete)
                    </div>
                )}

                {!selectedFile ? (
                    <Button
                        onClick={() => inputRef.current?.click()}
                        disabled={uploading}
                        variant="outline"
                        className="w-full h-32 border-dashed border-2 hover:border-blue-400"
                    >
                        <div className="flex flex-col items-center gap-2">
                            <Upload className="w-8 h-8 text-gray-400" />
                            <span>Click to select image</span>
                            <span className="text-xs text-gray-500">
                                Max {maxSizeInMB}MB • {allowedTypes.map(t => t.split('/')[1]).join(', ')}
                            </span>
                        </div>
                    </Button>
                ) : (
                    <div className="space-y-4">
                        {previewUrl && (
                            <div className="relative">
                                <img
                                    src={previewUrl}
                                    alt="Preview"
                                    className="w-full h-48 object-cover rounded-lg"
                                />
                                <Button
                                    onClick={handleRemoveFile}
                                    variant="destructive"
                                    size="sm"
                                    className="absolute top-2 right-2"
                                    disabled={uploading}
                                >
                                    <X className="w-4 h-4" />
                                </Button>
                            </div>
                        )}

                        <div className="text-sm text-gray-600">
                            <div className="flex items-center gap-2">
                                <ImageIcon className="w-4 h-4" />
                                <span>{selectedFile.name}</span>
                            </div>
                            <div className="text-xs text-gray-500">
                                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                        </div>

                        {(uploading || progress > 0) && (
                            <Progress value={progress} className="w-full" />
                        )}

                        <div className="flex gap-2">
                            <Button
                                onClick={handleUpload}
                                disabled={uploading}
                                className="flex-1"
                            >
                                {uploading ? 'Uploading...' : 'Upload Image'}
                            </Button>
                            <Button
                                onClick={handleRemoveFile}
                                variant="outline"
                                disabled={uploading}
                            >
                                Cancel
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    );
}


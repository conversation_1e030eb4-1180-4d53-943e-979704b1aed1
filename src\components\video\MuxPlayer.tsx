import { useEffect, useRef, useState } from 'react';
import Hls from 'hls.js';
import { getMuxPlaybackUrl, getMuxThumbnailUrl, extractMuxPlaybackId, isMuxUrl } from '@/lib/mux';

interface MuxPlayerProps {
    playbackId?: string;
    src?: string; // Direct Mux URL or playback ID
    poster?: string;
    title?: string;
    muted?: boolean;
    autoPlay?: boolean;
    loop?: boolean;
    controls?: boolean;
    className?: string;
    onPlay?: () => void;
    onPause?: () => void;
    onLoadStart?: () => void;
    onLoadedData?: () => void;
    onError?: (error: unknown) => void;
    playing?: boolean; // NEW: control play/pause from parent
}

export function MuxPlayer({
    playbackId,
    src,
    poster,
    muted = true,
    autoPlay = false,
    loop = false,
    controls = true,
    className = '',
    onPlay,
    onPause,
    onLoadStart,
    onLoadedData,
    onError,
    playing, // NEW
}: MuxPlayerProps) {
    const videoRef = useRef<HTMLVideoElement>(null);
    const hlsRef = useRef<Hls | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string>('');

    // Determine if the URL is for LivePeer
    const isLivePeerUrl = (url: string): boolean => {
        if (!url) return false;
        return url.includes('livepeercdn.com') ||
            url.includes('lp-playback.studio') ||
            url.includes('catalyst-vod-com');
    };

    // Determine the playback ID and URL
    const getPlaybackInfo = () => {
        let finalPlaybackId = playbackId;
        let finalSrc = src;

        console.log('MuxPlayer getPlaybackInfo:', { playbackId, src });

        // If src is provided, determine the type of video source
        if (src) {
            if (isMuxUrl(src)) {
                // This is a Mux URL, extract the playback ID
                finalPlaybackId = extractMuxPlaybackId(src) || undefined;
                finalSrc = src; // Keep the original Mux URL
                console.log('Detected Mux URL:', finalSrc);
            } else if (isLivePeerUrl(src) || src.includes('.m3u8')) {
                // This is a LivePeer URL or other HLS stream, use it directly
                finalSrc = src;
                finalPlaybackId = undefined;
                console.log('Detected LivePeer or HLS URL:', finalSrc);
            } else if (!src.includes('://')) {
                // If src doesn't contain a protocol, treat it as a Mux playback ID
                finalPlaybackId = src;
                finalSrc = getMuxPlaybackUrl(src);
                console.log('Treating as Mux playback ID:', finalPlaybackId);
            } else {
                // Any other URL, use directly
                finalSrc = src;
                console.log('Using direct URL:', finalSrc);
            }
        } else if (playbackId) {
            // If only playbackId is provided, generate a Mux URL
            finalSrc = getMuxPlaybackUrl(playbackId);
            console.log('Generated Mux URL from playback ID:', finalSrc);
        }

        console.log('Final playback info:', { playbackId: finalPlaybackId, src: finalSrc });
        return { playbackId: finalPlaybackId, src: finalSrc };
    };

    const { playbackId: resolvedPlaybackId, src: resolvedSrc } = getPlaybackInfo();

    // Generate poster if not provided
    const resolvedPoster = poster || (resolvedPlaybackId && !isLivePeerUrl(resolvedPlaybackId) ?
        getMuxThumbnailUrl(resolvedPlaybackId, {
            width: 1280,
            height: 720,
            fit_mode: 'crop',
            time: 1
        }) : undefined);

    useEffect(() => {
        const video = videoRef.current;
        if (!video || !resolvedSrc) return;

        setIsLoading(true);
        setHasError(false);
        setErrorMessage('');

        // Clean up previous HLS instance
        if (hlsRef.current) {
            hlsRef.current.destroy();
            hlsRef.current = null;
        }

        const setupHls = () => {
            if (!Hls.isSupported()) {
                console.error('HLS.js is not supported in this browser');
                setHasError(true);
                setErrorMessage('Your browser does not support HLS video streaming');
                setIsLoading(false);
                return;
            }

            try {
                const hls = new Hls({
                    enableWorker: false,
                    backBufferLength: 90,
                });

                hlsRef.current = hls;

                hls.loadSource(resolvedSrc);
                hls.attachMedia(video);

                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    console.log('HLS manifest parsed successfully');
                    setIsLoading(false);
                    onLoadedData?.();

                    if (autoPlay) {
                        video.play().catch(error => {
                            console.warn('Autoplay failed:', error);
                        });
                    }
                });

                hls.on(Hls.Events.ERROR, (_, data) => {
                    console.error('HLS error:', data);

                    if (data.fatal) {
                        setHasError(true);
                        setErrorMessage(`Video error: ${data.details || 'Unknown HLS error'}`);
                        setIsLoading(false);
                        onError?.(data);

                        // Try to recover using direct playback
                        hls.destroy();
                        video.src = resolvedSrc;
                        video.load();
                    }
                });
            } catch (error) {
                console.error('Error setting up HLS:', error);
                setHasError(true);
                setErrorMessage('Error setting up video player');
                setIsLoading(false);
                onError?.(error);

                // Fallback to direct video playback
                try {
                    video.src = resolvedSrc;
                    video.load();
                } catch (e) {
                    console.error('Fallback video playback failed:', e);
                }
            }
        };

        const setupDirectVideo = () => {
            try {
                video.src = resolvedSrc;
                video.load();

                video.addEventListener('loadeddata', () => {
                    console.log('Video loaded successfully');
                    setIsLoading(false);
                    onLoadedData?.();

                    if (autoPlay) {
                        video.play().catch(err => {
                            console.warn('Autoplay failed:', err);
                        });
                    }
                });

                video.addEventListener('error', (event) => {
                    console.error('Video error:', event);
                    setHasError(true);
                    setErrorMessage('Video failed to load. Please check the URL and try again.');
                    setIsLoading(false);
                    onError?.(new Error('Video load error'));
                });
            } catch (error) {
                console.error('Error setting up direct video:', error);
                setHasError(true);
                setErrorMessage('Error setting up video player');
                setIsLoading(false);
                onError?.(error);
            }
        };

        // If it's an HLS stream, use HLS.js for better compatibility
        if (resolvedSrc.includes('.m3u8') && Hls.isSupported()) {
            console.log('Setting up HLS video for', resolvedSrc);
            setupHls();
        } else {
            console.log('Setting up direct video for', resolvedSrc);
            setupDirectVideo();
        }

        return () => {
            if (hlsRef.current) {
                hlsRef.current.destroy();
                hlsRef.current = null;
            }

            // Clean up video element
            if (video) {
                video.pause();
                video.removeAttribute('src');
                video.load();
            }
        };
    }, [resolvedSrc, autoPlay, onLoadedData, onError]);

    // Control play/pause from parent
    useEffect(() => {
        const video = videoRef.current;
        if (!video || typeof playing === 'undefined') return;
        if (playing) {
            video.play().catch(() => { });
        } else {
            video.pause();
        }
    }, [playing]);

    // Handle video events
    useEffect(() => {
        const video = videoRef.current;
        if (!video) return;

        const handlePlay = () => onPlay?.();
        const handlePause = () => onPause?.();
        const handleLoadStart = () => {
            setIsLoading(true);
            onLoadStart?.();
        };

        video.addEventListener('play', handlePlay);
        video.addEventListener('pause', handlePause);
        video.addEventListener('loadstart', handleLoadStart);

        return () => {
            video.removeEventListener('play', handlePlay);
            video.removeEventListener('pause', handlePause);
            video.removeEventListener('loadstart', handleLoadStart);
        };
    }, [onPlay, onPause, onLoadStart]);

    if (!resolvedSrc) {
        return (
            <div className={`flex items-center justify-center bg-gray-900 text-white ${className}`}>
                <p>No video source provided</p>
            </div>
        );
    }

    return (
        <div className={`relative ${className}`}>
            <video
                ref={videoRef}
                className="w-full h-full"
                poster={resolvedPoster}
                muted={muted}
                loop={loop}
                controls={controls}
                playsInline
                preload="metadata"
                crossOrigin="anonymous"
            />

            {/* Loading overlay */}
            {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="flex items-center space-x-2 text-white">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                        <span>Loading video...</span>
                    </div>
                </div>
            )}

            {/* Error overlay */}
            {hasError && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
                    <div className="text-center text-white p-4">
                        <div className="text-red-400 mb-2">⚠️</div>
                        <p className="text-sm">{errorMessage}</p>
                        <button
                            onClick={() => window.location.reload()}
                            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                            Retry
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}
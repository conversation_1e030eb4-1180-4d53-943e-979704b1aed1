import { useState, useEffect, useRef } from 'react';
import { Tool, AITool } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import ToolCard from './ToolCard';
import NeonButton from '@/components/ui/NeonButton';
import { fetchUserTools, deleteUserTool } from '@/services/toolService';
import { useToast } from '@/hooks/use-toast';

const ToolTracker = () => {
  const [userTools, setUserTools] = useState<(Tool | AITool)[]>([]);
  const { user } = useAuth();
  const { toast } = useToast();
  const libraryRef = useRef<HTMLDivElement>(null);

  // Scroll to library section
  const handleEmptyStateClick = () => {
    libraryRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Load tools on mount
  useEffect(() => {
    const loadTools = async () => {
      if (!user) return;
      try {
        const tools = await fetchUserTools(user.id);
        setUserTools(tools);
      } catch (error) {
        console.error('Error loading tools:', error);
        toast({
          title: 'Error',
          description: 'Failed to load tools',
          variant: 'destructive'
        });
      }
    };

    loadTools();
  }, [user, toast]);

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold text-white">Tool Tracker</h1>

      {userTools.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">Add Your First Tool</h2>
          <p className="text-sm text-gray-400 mb-6">Browse our tool library to get started</p>
          <NeonButton variant="gradient" onClick={handleEmptyStateClick}>
            Browse Tools
          </NeonButton>
        </div>
      ) : (
        <div className="p-4 bg-sortmy-darker/40 border border-sortmy-blue/20 rounded-xl">
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-6 justify-items-center">            {userTools.map((tool) => (
            <ToolCard
              key={tool.id}
              tool={tool}
              isUserTool={true}
              onDelete={async (toolToDelete: Tool | AITool) => {
                if (!user) return;
                try {
                  await deleteUserTool(toolToDelete.id);
                  setUserTools(prev => prev.filter(t => t.id !== toolToDelete.id));
                  toast({
                    title: 'Success',
                    description: 'Tool removed successfully',
                  });
                } catch (error: any) {
                  console.error('Error deleting tool:', error);
                  toast({
                    title: 'Error',
                    description: 'Failed to remove tool',
                    variant: 'destructive'
                  });
                }
              }}
            />
          ))}
          </div>
        </div>
      )}

      <div ref={libraryRef} className="pt-8">
        {/* Tool library content */}
      </div>
    </div>
  );
};

export default ToolTracker;

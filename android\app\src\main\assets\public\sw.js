if(!self.define){let e,s={};const i=(i,n)=>(i=new URL(i+".js",n).href,s[i]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()})).then((()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e})));self.define=(n,r)=>{const l=e||("document"in self?document.currentScript.src:"")||location.href;if(s[l])return;let t={};const o=e=>i(e,l),a={module:{uri:l},exports:t,require:o};s[l]=Promise.all(n.map((e=>a[e]||o(e)))).then((e=>(r(...e),t)))}}define(["./workbox-74f2ef77"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/firebase-messaging-sw-BoHq4HdZ.js",revision:null},{url:"assets/main-8XFua-u6.js",revision:null},{url:"assets/main-nBR2mc0g.css",revision:null},{url:"assets/web-_VCTtvUu.js",revision:null},{url:"assets/web-D2G9elIF.js",revision:null},{url:"assets/web-Dfmdk10I.js",revision:null},{url:"assets/web-qcnismWP.js",revision:null},{url:"firebase-messaging-sw-loader.js",revision:"fa725ea355a6c2a941a491db4b9c8c3f"},{url:"firebase-messaging-sw.js",revision:"262d46c138d8eaabd7147c34b59ba8ed"},{url:"index.html",revision:"d44e297cdc5f75280da4ef29613f1686"},{url:"lovable-preview-fix.js",revision:"74bce8dca617158ab312799366d5a14b"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"manifest.webmanifest",revision:"19e913e61d2539e89f3702333a44a4c0"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/www\.gstatic\.com\/.*/i,new e.CacheFirst({cacheName:"google-static",plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:2592e3})]}),"GET")}));
//# sourceMappingURL=sw.js.map

import { useState, useEffect } from 'react';
import { Zap, ArrowLeft, CheckCircle, ChevronRight } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import GlassCard from "@/components/ui/GlassCard";
import { Module, Lesson } from "@/types/academy";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import LessonView from "@/components/academy/LessonView";
import { useToast } from "@/hooks/use-toast";

interface EnhancedModuleViewProps {
  moduleId: string;
  onBack?: () => void;
}

const EnhancedModuleView = ({ moduleId, onBack }: EnhancedModuleViewProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [module, setModule] = useState<Module | null>(null);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [currentLessonIndex, setCurrentLessonIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [completedLessons, setCompletedLessons] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const fetchModule = async () => {
      setLoading(true);
      try {
        // Fetch module data
        const moduleDoc = await getDoc(doc(db, 'modules', moduleId));
        if (moduleDoc.exists()) {
          const moduleData = { id: moduleDoc.id, ...moduleDoc.data() } as Module;
          setModule(moduleData);

          // Fetch lessons if not included in module
          if (!moduleData.lessons || moduleData.lessons.length === 0) {
            const lessonsQuery = query(
              collection(db, 'lessons'),
              where('moduleId', '==', moduleId),
              orderBy('order', 'asc')
            );
            const lessonsSnapshot = await getDocs(lessonsQuery);
            const lessonsData = lessonsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            } as Lesson));
            setLessons(lessonsData);
          } else {
            // Use lessons from module data
            setLessons(moduleData.lessons);
          }

          // TODO: Fetch user progress from Firebase
          // For now, we'll use a dummy implementation
          const dummyCompletedLessons: { [key: string]: boolean } = {};
          if (moduleData.lessons) {
            moduleData.lessons.forEach((lesson, index) => {
              dummyCompletedLessons[lesson.id] = index === 0 ? true : false;
            });
          }
          setCompletedLessons(dummyCompletedLessons);

          // Calculate initial progress
          updateProgressCalculation(dummyCompletedLessons, moduleData.lessons || []);
        } else {
          toast({
            title: "Module not found",
            description: "We couldn't find the requested module",
            variant: "destructive"
          });
          if (onBack) onBack();
        }
      } catch (error) {
        console.error("Error fetching module:", error);
        toast({
          title: "Error loading module",
          description: "There was a problem loading the module data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchModule();
  }, [moduleId, onBack, toast]);

  const updateProgressCalculation = (completed: { [key: string]: boolean }, allLessons: Lesson[]) => {
    if (allLessons.length === 0) return;

    const completedCount = Object.values(completed).filter(Boolean).length;
    const totalLessons = allLessons.length;
    const calculatedProgress = Math.round((completedCount / totalLessons) * 100);
    setProgress(calculatedProgress);
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/dashboard/academy');
    }
  };

  const handleNextLesson = () => {
    if (currentLessonIndex < (lessons.length - 1)) {
      setCurrentLessonIndex(prev => prev + 1);
      window.scrollTo(0, 0);
    } else {
      // Mark module as completed
      completeModule();
    }
  };

  const handlePreviousLesson = () => {
    if (currentLessonIndex > 0) {
      setCurrentLessonIndex(prev => prev - 1);
      window.scrollTo(0, 0);
    }
  };

  const handleLessonComplete = async (lessonId: string) => {
    try {
      // Mark lesson as completed in local state
      const updatedCompletedLessons = {
        ...completedLessons,
        [lessonId]: true
      };
      setCompletedLessons(updatedCompletedLessons);

      // Update progress calculation
      updateProgressCalculation(updatedCompletedLessons, lessons);

      // TODO: Update user progress in Firebase

      // Show toast notification
      toast({
        title: "Lesson completed",
        description: `You've completed this lesson${currentLessonIndex < (lessons.length - 1) ? ', move on to the next one!' : '!'}`,
      });
    } catch (error) {
      console.error("Error marking lesson as completed:", error);
    }
  };

  const completeModule = async () => {
    try {
      // TODO: Update user progress in Firebase
      // For now, just show a toast notification
      toast({
        title: "Module completed!",
        description: `Congratulations! You've earned ${module?.xpReward || 0} XP.`,
      });

      // Navigate back to academy
      handleBack();
    } catch (error) {
      console.error("Error completing module:", error);
    }
  };

  const handleSelectLesson = (index: number) => {
    // Only allow selecting completed lessons or the next available one
    const lessonIds = lessons.map(l => l.id);
    const selectedLessonId = lessonIds[index];
    const previousLessonId = index > 0 ? lessonIds[index - 1] : null;

    // Check if user can access this lesson
    if (index === 0 ||
      completedLessons[selectedLessonId] ||
      (previousLessonId && completedLessons[previousLessonId])) {
      setCurrentLessonIndex(index);
      window.scrollTo(0, 0);
    } else {
      toast({
        title: "Lesson locked",
        description: "Complete the previous lessons first to unlock this one",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-sortmy-blue border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Loading module content...</p>
        </div>
      </div>
    );
  }

  if (!module) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <p>Module not found. Please try another module.</p>
          <Button onClick={handleBack} className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Academy
          </Button>
        </div>
      </div>
    );
  }

  const currentLesson = lessons[currentLessonIndex];

  return (
    <div className="flex flex-col min-h-screen gap-6 p-4">
      <GlassCard className="p-6">
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="text-gray-400 hover:text-white"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back to Academy
              </Button>
            </div>
            <div className="flex items-center bg-sortmy-blue/10 px-3 py-1.5 rounded-full">
              <Zap className="w-4 h-4 text-sortmy-blue mr-1.5" />
              <span className="text-sm font-medium text-sortmy-blue">+{module.xpReward} XP</span>
            </div>
          </div>

          <div>
            <h1 className="text-2xl font-bold text-white mb-2">{module.title}</h1>
            <p className="text-gray-400">{module.description}</p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Module Progress</span>
              <span className="text-gray-400">{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </div>
      </GlassCard>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Lesson navigation sidebar */}
        <div className="md:col-span-1">
          <GlassCard className="p-4 sticky top-4">
            <h3 className="text-lg font-semibold mb-4">Lessons</h3>
            <div className="space-y-2">
              {lessons.map((lesson, index) => {
                // Determine if lesson is accessible
                const previousLessonId = index > 0 ? lessons[index - 1].id : null;
                const isAccessible = index === 0 ||
                  completedLessons[lesson.id] ||
                  (previousLessonId && completedLessons[previousLessonId]);

                return (
                  <div
                    key={lesson.id}
                    className={`
                      p-3 rounded-lg flex items-center justify-between cursor-pointer
                      ${index === currentLessonIndex ? 'bg-sortmy-blue/20 text-white' : 'hover:bg-sortmy-blue/10'}
                      ${!isAccessible ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                    onClick={() => isAccessible && handleSelectLesson(index)}
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 flex items-center justify-center rounded-full mr-2 
                        ${completedLessons[lesson.id] ? 'bg-green-500/20 text-green-400' : 'bg-sortmy-blue/20 text-sortmy-blue'}">
                        {completedLessons[lesson.id] ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <span>{index + 1}</span>
                        )}
                      </div>
                      <span className="text-sm truncate max-w-[150px]">{lesson.title}</span>
                    </div>
                    <span className="text-xs text-gray-400">
                      {lesson.duration || 5} min
                    </span>
                  </div>
                );
              })}
            </div>
          </GlassCard>
        </div>

        {/* Main lesson content */}
        <div className="md:col-span-3 space-y-6">
          {currentLesson ? (
            <>
              <LessonView
                lesson={currentLesson}
                onComplete={() => handleLessonComplete(currentLesson.id)}
                isCompleted={!!completedLessons[currentLesson.id]}
              />

              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={handlePreviousLesson}
                  disabled={currentLessonIndex === 0}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous Lesson
                </Button>

                <Button
                  onClick={handleNextLesson}
                  className={`${completedLessons[currentLesson.id] ? 'bg-sortmy-blue hover:bg-sortmy-blue/90' : 'bg-gray-700 hover:bg-gray-600'}`}
                  disabled={!completedLessons[currentLesson.id]}
                >
                  {currentLessonIndex < (lessons.length - 1) ? (
                    <>
                      Next Lesson
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </>
                  ) : (
                    <>
                      Complete Module
                      <CheckCircle className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </>
          ) : (
            <div className="flex justify-center items-center h-64">
              <p className="text-gray-400">No lessons available for this module.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedModuleView;
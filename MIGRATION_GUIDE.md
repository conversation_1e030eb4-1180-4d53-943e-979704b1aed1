# Portfolio System Migration Guide

## Quick Start - Using the New System

To start using the new enhanced portfolio system immediately, replace your existing portfolio components with the new ones:

### 1. Replace AddPortfolio Component

**Old way:**
```typescript
import AddPortfolio from '@/components/dashboard/AddPortfolio';
```

**New way:**
```typescript
import EnhancedAddPortfolio from '@/components/dashboard/EnhancedAddPortfolio';
```

### 2. Replace PortfolioForm Component

**Old way:**
```typescript
import { PortfolioForm } from '@/components/portfolio/PortfolioForm';
```

**New way:**
```typescript
import { EnhancedPortfolioForm } from '@/components/portfolio/EnhancedPortfolioForm';
```

### 3. Update Your Routes

In your routing configuration:

**Old:**
```typescript
<Route path="/add-portfolio" element={<AddPortfolio />} />
```

**New:**
```typescript
<Route path="/add-portfolio" element={<EnhancedAddPortfolio />} />
```

## Key Differences

### Enhanced Features
- ✅ **Firebase Storage** for images (replaces Google Drive)
- ✅ **Enhanced Livepeer** integration for videos
- ✅ **Auto-save drafts** every 30 seconds
- ✅ **Draft management** with recovery
- ✅ **Automated cleanup** of orphaned files
- ✅ **Better error handling** and user feedback
- ✅ **Image compression** and optimization
- ✅ **Archive system** for old items

### API Changes

**Old PortfolioForm Props:**
```typescript
interface PortfolioFormProps {
  user: User;
  initialData?: Partial<PortfolioFormData>;
  onSubmit: (data: PortfolioFormData) => Promise<void>;
  isLoading?: boolean;
  skipFirebaseUpdate?: boolean;
}
```

**New EnhancedPortfolioForm Props:**
```typescript
interface EnhancedPortfolioFormProps {
  user: User;
  initialData?: Partial<EnhancedPortfolioFormData>;
  onSubmit: (data: EnhancedPortfolioFormData & {
    media_urls: string[];
    media_metadata: any[];
  }) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}
```

## Step-by-Step Migration

### Phase 1: Deploy New Components (Recommended)

1. **Update your imports** to use the new components
2. **Test the new functionality** with new portfolio items
3. **Keep the old system** running for existing items

### Phase 2: Initialize Cleanup Services

Add to your app initialization (e.g., `App.tsx` or `main.tsx`):

```typescript
import { setupAutomaticCleanup, setupBrowserCleanup } from '@/services/scheduledCleanupService';

// In your app initialization
useEffect(() => {
  setupAutomaticCleanup();
  setupBrowserCleanup();
}, []);
```

### Phase 3: Update Firebase Rules (Required)

Deploy the updated Firebase rules:

1. **Storage rules** - Already updated in `storage.rules`
2. **Firestore rules** - Already updated in `firestore.rules`
3. **Indexes** - Already updated in `firestore.indexes.json`

Deploy these with:
```bash
firebase deploy --only firestore:rules,firestore:indexes,storage
```

### Phase 4: Optional - Migrate Existing Data

If you want to migrate existing Google Drive portfolio items to the new system:

```typescript
import { portfolioStorageService } from '@/services/portfolioStorageService';

// Example migration function (implement as needed)
const migrateUserPortfolio = async (userId: string) => {
  // 1. Fetch existing Google Drive items
  // 2. Download media from Google Drive
  // 3. Upload to Firebase Storage/Livepeer
  // 4. Update portfolio items with new URLs
  // 5. Clean up old Google Drive files
};
```

## Testing the New System

### 1. Test Image Upload
```typescript
// The new system automatically:
// - Compresses images over 2MB
// - Validates file types and sizes
// - Provides upload progress
// - Handles errors gracefully
```

### 2. Test Video Upload
```typescript
// Enhanced Livepeer integration:
// - Better error handling
// - Resume interrupted uploads
// - Automatic cleanup of failed uploads
// - Metadata preservation
```

### 3. Test Draft System
```typescript
// Auto-save functionality:
// - Saves every 30 seconds
// - Recovers from browser crashes
// - Cleans up after 7 days
// - Shows save status
```

## Troubleshooting

### Common Issues

1. **"GoogleDriveStorage is not defined"**
   - Solution: Use `EnhancedAddPortfolio` instead of `AddPortfolio`
   - Or ensure proper import: `import { GoogleDriveStorage } from '@/components/storage/GoogleDriveStorage';`

2. **Firebase Storage permission errors**
   - Solution: Deploy the updated `storage.rules`
   - Check user authentication

3. **Livepeer upload failures**
   - Solution: Check `VITE_LIVEPEER_API_KEY` environment variable
   - Verify Livepeer account limits

4. **Draft auto-save not working**
   - Solution: Check browser localStorage permissions
   - Verify user authentication

### Performance Optimization

1. **Image Compression**
   - Images over 2MB are automatically compressed
   - Maximum resolution: 1920x1080
   - Quality: 80% JPEG

2. **Storage Cleanup**
   - Runs automatically every 24 hours
   - Cleans up drafts older than 7 days
   - Removes orphaned files

3. **Livepeer Optimization**
   - Global CDN delivery
   - Automatic transcoding
   - Adaptive bitrate streaming

## Rollback Plan

If you need to rollback to the old system:

1. **Revert component imports**:
   ```typescript
   // Change back to:
   import AddPortfolio from '@/components/dashboard/AddPortfolio';
   import { PortfolioForm } from '@/components/portfolio/PortfolioForm';
   ```

2. **Keep Firebase rules** - They're backward compatible

3. **Disable cleanup services**:
   ```typescript
   // Comment out in your app initialization:
   // setupAutomaticCleanup();
   // setupBrowserCleanup();
   ```

## Support

- **New system issues**: Check the enhanced component documentation
- **Migration questions**: Review this guide and test in staging first
- **Performance issues**: Monitor the cleanup service logs
- **Storage costs**: Use the storage usage tracking in the new system

## Benefits Summary

### For Users
- ⚡ **Faster uploads** and loading times
- 🎥 **Better video quality** with Livepeer
- 💾 **Auto-save** prevents data loss
- 📱 **Better mobile** experience
- 🔒 **More reliable** storage

### For Developers
- 🧹 **Automated cleanup** reduces maintenance
- 📊 **Better monitoring** and error tracking
- 💰 **Cost optimization** with smart storage
- 🔧 **Easier debugging** with better logs
- 📈 **Improved scalability**

The new system is designed to be a drop-in replacement with significant improvements. Start with the new components for new features, and migrate existing functionality as needed.

import { useState, useEffect } from 'react';
import {
  Target,
  CheckCircle,
  Clock,
  Gift,
  Sparkles,
  Trophy,
  Flame,
  BookOpen,
  Plus,
  Share,
  Heart,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { XPService } from '@/services/xpService';
import { MissionService, Mission, MissionTracker } from '@/services/missionService';
import { ActivityType } from '@/utils/xpSystem';

interface MissionWithIcon extends Mission {
  icon: React.ReactNode;
}

const MissionsPage = () => {
  const { user, refreshUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [missions, setMissions] = useState<MissionWithIcon[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedMilestone, setSelectedMilestone] = useState<MissionWithIcon | null>(null);

  // Get icon for mission based on activity type
  const getMissionIcon = (activityType: ActivityType): React.ReactNode => {
    switch (activityType) {
      case 'DAILY_LOGIN': return <Flame className="w-6 h-6" />;
      case 'ADD_TOOL_TO_LIBRARY': return <Plus className="w-6 h-6" />;
      case 'CREATE_FIRST_PORTFOLIO': return <Trophy className="w-6 h-6" />;
      case 'COMPLETE_LESSON': return <BookOpen className="w-6 h-6" />;
      case 'CREATE_TOOLKIT': return <Target className="w-6 h-6" />;
      case 'LIKE_POST': return <Heart className="w-6 h-6" />;
      case 'SHARE_TOOL': return <Share className="w-6 h-6" />;
      case 'STAY_ONLINE': return <Clock className="w-6 h-6" />;
      default: return <Target className="w-6 h-6" />;
    }
  };

  // Load missions from service
  const loadMissions = async () => {
    if (!user?.uid) {
      console.log('MissionsPage: No user found');
      return;
    }

    console.log('MissionsPage: Loading missions for user:', user.uid);
    setIsLoading(true);
    try {
      const userMissions = await MissionService.getUserMissions(user.uid);
      console.log('MissionsPage: Loaded missions:', userMissions);

      if (userMissions && userMissions.length > 0) {
        const missionsWithIcons: MissionWithIcon[] = userMissions.map(mission => {
          let missionWithIcon = {
            ...mission,
            icon: getMissionIcon(mission.activityType)
          };

          // Add milestone info for milestone missions and daily milestone missions
          if (mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily') {
            const milestoneInfo = MissionService.getCurrentMilestoneInfo(mission.id, mission.progress);
            missionWithIcon = {
              ...missionWithIcon,
              milestoneInfo
            };
          }

          return missionWithIcon;
        });
        setMissions(missionsWithIcons);
        console.log('MissionsPage: Set missions with icons:', missionsWithIcons);

        // Log completed missions for debugging
        const completedMissions = missionsWithIcons.filter(m => m.isCompleted && !m.isClaimed);
        console.log('MissionsPage: Completed unclaimed missions:', completedMissions);
      } else {
        console.log('MissionsPage: No missions returned from service');
        setMissions([]);
      }
    } catch (error) {
      console.error('MissionsPage: Error loading missions:', error);
      setMissions([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user?.uid) {
      loadMissions();
      // Track daily login when user visits missions page
      MissionTracker.dailyLogin(user.uid);
      // Start time tracking for stay online mission
      MissionTracker.startTimeTracking(user.uid);
    }

    // Cleanup time tracking when component unmounts
    return () => {
      if (user?.uid) {
        MissionTracker.stopTimeTracking(user.uid);
      }
    };
  }, [user?.uid]);

  // Auto-refresh missions every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      loadMissions();
    }, 30000);

    return () => clearInterval(interval);
  }, [user?.uid]);

  // Claim mission reward
  const claimReward = async (mission: Mission) => {
    if (!user?.uid || !mission.isCompleted || mission.isClaimed) {
      return;
    }

    setIsLoading(true);
    try {
      console.log('MissionsPage: Claiming reward for mission:', mission.id, mission.title);

      // Mark mission as claimed in the service
      const success = await MissionService.claimMission(user.uid, mission.id);
      if (!success) {
        throw new Error('Failed to claim mission');
      }

      console.log('MissionsPage: Mission claimed successfully, awarding XP...');

      // Award XP through the service
      const result = await XPService.awardXP(
        user.uid,
        mission.activityType,
        `Completed mission: ${mission.title}`,
        { missionId: mission.id, xpReward: mission.xpReward }
      );

      if (result.success) {
        console.log('MissionsPage: XP awarded successfully:', result);

        toast({
          title: "Mission Completed! 🎉",
          description: `You earned ${mission.xpReward} XP for "${mission.title}"`,
          duration: 3000,
        });

        // Refresh user data and missions
        await refreshUser();
        await loadMissions();
      } else {
        throw new Error(result.error || 'Failed to award XP');
      }
    } catch (error) {
      console.error('MissionsPage: Error claiming reward:', error);
      toast({
        title: "Error",
        description: "Failed to claim reward. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get mission status color
  const getMissionStatusColor = (mission: Mission): string => {
    if (mission.isCompleted && mission.isClaimed) {
      return 'bg-green-500/20 text-green-400 border-green-500/30';
    } else if (mission.isCompleted && !mission.isClaimed) {
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    } else {
      return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Filter missions by category
  const filteredMissions = missions.filter(mission =>
    selectedCategory === 'all' || mission.category.toLowerCase() === selectedCategory.toLowerCase()
  );

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(missions.map(m => m.category.toLowerCase())))];

  // Pagination for grid view
  const MISSIONS_PER_PAGE = 6;
  const totalPages = Math.ceil(filteredMissions.length / MISSIONS_PER_PAGE);
  const startIndex = currentSlide * MISSIONS_PER_PAGE;
  const currentMissions = filteredMissions.slice(startIndex, startIndex + MISSIONS_PER_PAGE);

  const nextSlide = () => {
    if (currentSlide < totalPages - 1) {
      setCurrentSlide((prev) => (prev + 1) % totalPages);
    }
  };

  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide((prev) => (prev - 1 + totalPages) % totalPages);
    }
  };

  // Handle mission click for milestone details
  const handleMissionClick = (mission: MissionWithIcon) => {
    console.log('🎯 Mission clicked:', mission.id, 'milestoneInfo:', mission.milestoneInfo);
    if (mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily') {
      setSelectedMilestone(mission);
    }
  };

  // Close milestone detail view
  const closeMilestoneDetail = () => {
    setSelectedMilestone(null);
  };

  return (
    <div className="min-h-screen bg-sortmy-dark text-white">
      {/* Header */}
      <div className="bg-sortmy-darker border-b border-sortmy-blue/20 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(-1)}
                className="text-gray-400 hover:text-white"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div className="flex items-center gap-3">
                <Sparkles className="w-8 h-8 text-sortmy-blue" />
                <div>
                  <h1 className="text-2xl font-bold text-white">XP Missions</h1>
                  <p className="text-gray-400">Complete missions to earn XP and level up</p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={loadMissions}
                disabled={isLoading}
                className="text-gray-400 hover:text-white"
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto p-6">
        {/* Category Filter Tabs */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex gap-2 overflow-x-auto">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "ghost"}
                  size="sm"
                  onClick={() => {
                    setSelectedCategory(category);
                    setCurrentSlide(0);
                  }}
                  className={`capitalize whitespace-nowrap ${selectedCategory === category
                    ? 'bg-sortmy-blue text-white'
                    : 'text-gray-400 hover:text-white hover:bg-sortmy-blue/10'
                    }`}
                >
                  {category}
                </Button>
              ))}
            </div>
            {filteredMissions.length > 0 && (
              <div className="text-sm text-gray-400 whitespace-nowrap ml-4">
                {filteredMissions.length} mission{filteredMissions.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>
        </div>

        {/* Missions Grid */}
        <div className="space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-gray-400">Loading missions...</div>
            </div>
          ) : filteredMissions.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Target className="w-12 h-12 text-gray-400 mb-4" />
              <div className="text-lg text-gray-400 mb-2">
                {selectedCategory === 'all' ? 'No missions available' : `No ${selectedCategory} missions`}
              </div>
              <div className="text-sm text-gray-500">
                Check back later for new missions!
              </div>
            </div>
          ) : (
            <>
              {/* Mission Cards Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {currentMissions.map((mission) => (
                  <div
                    key={mission.id}
                    onClick={() => handleMissionClick(mission)}
                    className={`bg-sortmy-darker border rounded-lg p-6 transition-all duration-200 hover:scale-105 ${(mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily')
                      ? 'cursor-pointer hover:border-sortmy-blue/40'
                      : ''
                      } ${getMissionStatusColor(mission)}`}
                  >
                    {/* Mission Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="text-sortmy-blue">
                          {mission.icon}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-white">
                              {mission.title}
                            </h4>
                            {(mission.category === 'milestone' || mission.id === 'like_posts_daily' || mission.id === 'time_keeper_daily') && (
                              <span className="text-xs text-sortmy-blue">📋 Click for details</span>
                            )}
                          </div>
                          <Badge
                            variant="outline"
                            className={`text-xs ${mission.category === 'daily'
                              ? 'border-blue-500/30 text-blue-400'
                              : 'border-purple-500/30 text-purple-400'
                              }`}
                          >
                            {mission.category}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium text-sortmy-blue">
                          {mission.xpReward} XP
                        </div>
                      </div>
                    </div>

                    {/* Mission Description */}
                    <p className="text-sm text-gray-300 mb-4">
                      {mission.description}
                    </p>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>Progress</span>
                        <span>{mission.progress}/{mission.maxProgress}</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-sortmy-blue h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${Math.min((mission.progress / mission.maxProgress) * 100, 100)}%`
                          }}
                        />
                      </div>
                    </div>

                    {/* Mission Status */}
                    <div className="flex items-center justify-between">
                      {mission.isCompleted && mission.isClaimed ? (
                        <div className="flex items-center gap-2 text-green-400">
                          <CheckCircle className="w-4 h-4" />
                          <span className="text-sm">Claimed</span>
                        </div>
                      ) : mission.isCompleted && !mission.isClaimed ? (
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            claimReward(mission);
                          }}
                          disabled={isLoading}
                          className="bg-yellow-500 hover:bg-yellow-600 text-black font-medium"
                          size="sm"
                        >
                          <Gift className="w-4 h-4 mr-1" />
                          Claim Reward
                        </Button>
                      ) : (
                        <div className="flex items-center gap-2 text-gray-400">
                          <Clock className="w-4 h-4" />
                          <span className="text-sm">In Progress</span>
                        </div>
                      )}

                      {mission.expiresAt && (
                        <div className="text-xs text-gray-500">
                          Expires: {new Date(mission.expiresAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center gap-4 mt-8">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={prevSlide}
                    disabled={currentSlide === 0}
                    className="text-gray-400 hover:text-white disabled:opacity-50"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>

                  <div className="flex gap-2">
                    {Array.from({ length: totalPages }, (_, i) => (
                      <button
                        key={i}
                        onClick={() => setCurrentSlide(i)}
                        className={`w-2 h-2 rounded-full transition-all ${i === currentSlide ? 'bg-sortmy-blue' : 'bg-gray-600'
                          }`}
                      />
                    ))}
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={nextSlide}
                    disabled={currentSlide === totalPages - 1}
                    className="text-gray-400 hover:text-white disabled:opacity-50"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Milestone Detail Modal */}
      {selectedMilestone && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-sortmy-dark border border-sortmy-blue/20 rounded-lg max-w-md w-full max-h-[80vh] overflow-hidden">
            {/* Header */}
            <div className="p-4 border-b border-sortmy-blue/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="text-sortmy-blue">
                    {selectedMilestone.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-white">
                    {selectedMilestone.title}
                  </h3>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={closeMilestoneDetail}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                >
                  ✕
                </Button>
              </div>
            </div>

            {/* Content */}
            <ScrollArea className="max-h-[60vh]">
              <div className="p-4">
                {/* Current Progress */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-white mb-2">Current Progress</h4>
                  <div className="bg-sortmy-gray/20 rounded-lg p-3">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-300">{selectedMilestone.description}</span>
                      <span className="text-sm font-medium text-sortmy-blue">{selectedMilestone.xpReward} XP</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-sortmy-blue h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${Math.min((selectedMilestone.progress / selectedMilestone.maxProgress) * 100, 100)}%`
                        }}
                      />
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      {selectedMilestone.progress}/{selectedMilestone.maxProgress}
                    </div>
                  </div>
                </div>

                {/* All Milestones */}
                <div>
                  <h4 className="text-sm font-medium text-white mb-2">All Milestones</h4>
                  <div className="space-y-2">
                    {selectedMilestone.milestoneInfo?.detailedMilestones?.length > 0 ? (
                      selectedMilestone.milestoneInfo.detailedMilestones.map((milestone, index) => {
                        const isCompleted = selectedMilestone.progress >= milestone.target;
                        const isCurrent = milestone.target === selectedMilestone.milestoneInfo?.currentTarget;

                        return (
                          <div
                            key={index}
                            className={`flex items-center justify-between p-3 rounded-lg border ${isCompleted
                              ? 'bg-green-500/10 border-green-500/30 text-green-400'
                              : isCurrent
                                ? 'bg-sortmy-blue/10 border-sortmy-blue/30 text-sortmy-blue'
                                : 'bg-gray-500/10 border-gray-500/30 text-gray-400'
                              }`}
                          >
                            <div className="flex items-center gap-2">
                              {isCompleted ? (
                                <CheckCircle className="w-4 h-4" />
                              ) : isCurrent ? (
                                <Clock className="w-4 h-4" />
                              ) : (
                                <div className="w-4 h-4 rounded-full border-2 border-current" />
                              )}
                              <span className="text-sm">{milestone.description}</span>
                            </div>
                            <div className="text-sm font-medium">
                              {milestone.xp} XP
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-center text-gray-400 py-4">
                        <p className="mb-2">Milestone progression data is loading...</p>
                        <p className="text-xs">This mission tracks your progress towards multiple goals.</p>
                        <p className="text-xs">Current: {selectedMilestone.progress}/{selectedMilestone.maxProgress}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>
      )}
    </div>
  );
};

export default MissionsPage;

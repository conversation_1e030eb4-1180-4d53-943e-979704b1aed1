import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { db, storage } from '@/lib/firebase';
import { collection, addDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { X, Plus, ArrowLeft, PlusCircle } from 'lucide-react';
import GlassCard from '@/components/ui/GlassCard';
import NeonButton from '@/components/ui/NeonButton';
import ClickEffect from '@/components/ui/ClickEffect';
import { Badge } from '@/components/ui/badge';

const formSchema = z.object({
    name: z.string().min(2, { message: "Tool name must be at least 2 characters" }),
    description: z.string().min(10, { message: "Description must be at least 10 characters" }),
    useCase: z.string().min(10, { message: "Use case must be at least 10 characters" }),
    excelsAt: z.string().optional(),
    website: z.string().url({ message: "Please enter a valid URL" }),
    logoUrl: z.string().url({ message: "Please enter a valid logo URL" }).optional(),
    pricing: z.enum(['Free', 'Freemium', 'Paid', 'Subscription']),
    tags: z.array(z.string()).min(1, { message: "Add at least one tag" }),
});

const AdminAddLibraryTool = () => {
    const { user, isAdmin } = useAuth();
    const { toast } = useToast();
    const navigate = useNavigate();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [newTag, setNewTag] = useState('');
    const [logoFile, setLogoFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: "",
            description: "",
            useCase: "",
            excelsAt: "",
            website: "",
            logoUrl: "",
            pricing: 'Free',
            tags: [],
        },
    });

    // Add tag functionality
    const addTag = () => {
        if (newTag.trim() && !form.getValues().tags.includes(newTag.trim())) {
            form.setValue('tags', [...form.getValues().tags, newTag.trim()]);
            setNewTag('');
            form.clearErrors('tags');
        }
    };

    // Remove tag functionality
    const removeTag = (tagToRemove: string) => {
        const updatedTags = form.getValues().tags.filter(tag => tag !== tagToRemove);
        form.setValue('tags', updatedTags);
    };

    // Image upload handler
    const handleLogoUpload = async (file: File) => {
        if (!user) return null;
        setIsUploading(true);
        try {
            const storageRef = ref(storage, `ai-tool-logos/${user.uid}/${Date.now()}-${file.name}`);
            const result = await uploadBytes(storageRef, file);
            const url = await getDownloadURL(result.ref);
            form.setValue('logoUrl', url);
            setLogoFile(file);
            return url;
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to upload image',
                variant: 'destructive',
            });
            return null;
        } finally {
            setIsUploading(false);
        }
    };

    // Form submission handler
    const onSubmit = async (values: z.infer<typeof formSchema>) => {
        if (!user || !isAdmin) {
            toast({
                title: "Access Denied",
                description: "Only administrators can add tools to the library.",
                variant: "destructive",
            });
            return;
        }
        try {
            setIsSubmitting(true);
            // If a new logo file is selected, upload it
            let logoUrl = values.logoUrl;
            if (logoFile) {
                const uploadedUrl = await handleLogoUpload(logoFile);
                if (uploadedUrl) logoUrl = uploadedUrl;
            }
            // Add tool record to Firestore aiTools collection
            const aiToolsRef = collection(db, 'aiTools');
            await addDoc(aiToolsRef, {
                name: values.name,
                description: values.description,
                useCase: values.useCase,
                excelsAt: values.excelsAt || '',
                website: values.website,
                logoUrl: logoUrl || '',
                pricing: values.pricing,
                tags: values.tags,
                createdBy: user.uid,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            });
            toast({
                title: "Tool added to library",
                description: "The tool has been added to the AI tools library successfully.",
            });
            navigate('/dashboard/tools');
        } catch (error: any) {
            console.error('Error adding tool to library:', error);
            toast({
                title: "Error",
                description: error.message || "Failed to add tool to library. Please try again.",
                variant: "destructive",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    // If not admin, show access denied
    if (!isAdmin) {
        return (
            <div className="flex flex-col items-center justify-center h-96">
                <h1 className="text-2xl font-bold text-red-500 mb-4">Access Denied</h1>
                <p className="text-gray-400 mb-4">You need administrator privileges to access this page.</p>
                <NeonButton variant="cyan" onClick={() => navigate('/dashboard/tools')}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Tools
                </NeonButton>
            </div>
        );
    }

    return (
        <div className="space-y-8">
            <NeonButton variant="cyan" className="mb-4" onClick={() => navigate('/dashboard/tools')}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Tools
            </NeonButton>

            <GlassCard variant="bordered" className="border-sortmy-blue/20">
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <PlusCircle className="w-5 h-5 mr-2 text-sortmy-blue" />
                        <span className="bg-gradient-to-r from-sortmy-blue to-[#4d94ff] text-transparent bg-clip-text">
                            Add Tool to Library
                        </span>
                    </CardTitle>
                    <CardDescription>
                        Add a new AI tool to the public tool library
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-6">
                                    <FormField
                                        control={form.control}
                                        name="name"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Tool Name</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="e.g. ChatGPT" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="website"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Website URL</FormLabel>
                                                <FormControl>
                                                    <Input placeholder="https://example.com" {...field} />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="logoUrl"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Logo Image</FormLabel>
                                                <FormControl>
                                                    <div className="flex flex-col gap-2">
                                                        <Input
                                                            type="file"
                                                            accept="image/*"
                                                            onChange={async (e) => {
                                                                const file = e.target.files?.[0];
                                                                if (file) {
                                                                    setLogoFile(file);
                                                                    await handleLogoUpload(file);
                                                                }
                                                            }}
                                                            disabled={isUploading}
                                                        />
                                                        {field.value && (
                                                            <img src={field.value} alt="Logo Preview" className="w-24 h-24 object-contain rounded border mt-2" />
                                                        )}
                                                        <Input
                                                            placeholder="Or paste image URL..."
                                                            value={field.value || ''}
                                                            onChange={field.onChange}
                                                            disabled={isUploading}
                                                        />
                                                    </div>
                                                </FormControl>
                                                <FormDescription>
                                                    Upload or paste a logo image. Uploaded images are stored securely.
                                                </FormDescription>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="pricing"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Pricing Model</FormLabel>
                                                <FormControl>
                                                    <select
                                                        className="w-full p-2 rounded-md bg-sortmy-darker border border-sortmy-blue/20"
                                                        {...field}
                                                    >
                                                        <option value="Free">Free</option>
                                                        <option value="Freemium">Freemium</option>
                                                        <option value="Paid">Paid</option>
                                                        <option value="Subscription">Subscription</option>
                                                    </select>
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />
                                </div>

                                <div className="space-y-6">
                                    <FormField
                                        control={form.control}
                                        name="description"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Description</FormLabel>
                                                <FormControl>
                                                    <Textarea
                                                        placeholder="Brief description of the tool..."
                                                        className="min-h-[100px]"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="useCase"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Primary Use Case</FormLabel>
                                                <FormControl>
                                                    <Textarea
                                                        placeholder="What is this tool best used for..."
                                                        className="min-h-[100px]"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <FormField
                                        control={form.control}
                                        name="excelsAt"
                                        render={({ field }) => (
                                            <FormItem>
                                                <FormLabel>Excels At (Optional)</FormLabel>
                                                <FormControl>
                                                    <Textarea
                                                        placeholder="What makes this tool stand out..."
                                                        className="min-h-[100px]"
                                                        {...field}
                                                    />
                                                </FormControl>
                                                <FormMessage />
                                            </FormItem>
                                        )}
                                    />

                                    <div>
                                        <FormLabel>Tags</FormLabel>
                                        <div className="flex flex-wrap gap-2 mb-2">
                                            {form.getValues().tags.map((tag, index) => (
                                                <Badge
                                                    key={index}
                                                    variant="outline"
                                                    className="bg-sortmy-blue/10 text-sortmy-blue"
                                                >
                                                    {tag}
                                                    <button
                                                        type="button"
                                                        onClick={() => removeTag(tag)}
                                                        className="ml-2 hover:text-red-500"
                                                    >
                                                        <X className="h-3 w-3" />
                                                    </button>
                                                </Badge>
                                            ))}
                                        </div>
                                        <div className="flex gap-2">
                                            <Input
                                                value={newTag}
                                                onChange={(e) => setNewTag(e.target.value)}
                                                onKeyPress={(e) => {
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        addTag();
                                                    }
                                                }}
                                                placeholder="Type a tag..."
                                            />
                                            <NeonButton
                                                type="button"
                                                variant="cyan"
                                                onClick={addTag}
                                                disabled={!newTag.trim()}
                                            >
                                                <Plus className="h-4 w-4" />
                                            </NeonButton>
                                        </div>
                                        {form.formState.errors.tags && (
                                            <p className="text-sm font-medium text-red-500 mt-2">
                                                {form.formState.errors.tags.message}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-end">
                                <ClickEffect effect="ripple" color="blue">
                                    <NeonButton
                                        type="submit"
                                        disabled={isSubmitting}
                                        className="w-full md:w-auto"
                                    >
                                        <PlusCircle className="w-4 h-4 mr-2" />
                                        {isSubmitting ? 'Adding Tool...' : 'Add to Library'}
                                    </NeonButton>
                                </ClickEffect>
                            </div>
                        </form>
                    </Form>
                </CardContent>
            </GlassCard>
        </div>
    );
};

export default AdminAddLibraryTool;


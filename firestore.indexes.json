{"indexes": [{"collectionGroup": "tools", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "portfolio", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "portfolio", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "portfolio_drafts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "updated_at", "order": "DESCENDING"}]}, {"collectionGroup": "portfolio_drafts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "updated_at", "order": "ASCENDING"}]}, {"collectionGroup": "portfolio_archives", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "archived_at", "order": "DESCENDING"}]}, {"collectionGroup": "portfolio_archives", "queryScope": "COLLECTION", "fields": [{"fieldPath": "archived_at", "order": "ASCENDING"}]}, {"collectionGroup": "storage_cleanup_queue", "queryScope": "COLLECTION", "fields": [{"fieldPath": "scheduledFor", "order": "ASCENDING"}]}, {"collectionGroup": "storage_cleanup_queue", "queryScope": "COLLECTION", "fields": [{"fieldPath": "filePath", "order": "ASCENDING"}]}, {"collectionGroup": "comments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "comments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "postId", "order": "ASCENDING"}, {"fieldPath": "parentId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}], "fieldOverrides": []}
# SortMind Zenith - System Documentation

## System Overview
SortMind Zenith is a comprehensive platform for AI tool management, portfolio creation, and personal productivity enhancement. The platform is built using React with TypeScript and follows a modular architecture.

## Core Features

### 1. Authentication System
- Location: `src/contexts/AuthContext`
- Implements multi-provider authentication (email/password, Google, GitHub, Twitter)
- Protected routes handled by `src/components/ProtectedRoute`

### 2. AI Tool Management
- Main Component: `src/components/dashboard/CombinedToolTracker`
- Features:
  - Tool tracking and organization
  - Usage documentation
  - Library integration
  - Admin tool management (`AdminAddLibraryTool`)

### 3. Portfolio System
- Core Components:
  - `src/components/dashboard/Portfolio` - Main portfolio view
  - `src/components/dashboard/EnhancedAddPortfolio` - Portfolio creation
  - `src/pages/EditPortfolio` - Portfolio editing
- Features:
  - Firebase Storage integration for media
  - Livepeer video integration
  - Auto-save drafts
  - Automated cleanup services
  - Image optimization

### 4. Academy System
- Location: `src/pages/Academy`
- Features:
  - Course viewing (`CourseView`)
  - Course management (`CourseEditor`, `CourseManager`)
  - Admin tools for course creation

### 5. Social Features
- Explore section for discovering content and creators
- User interactions tracking
- Messaging system
- Achievement system

## Technical Architecture

### 1. Frontend Framework
- React 18 with TypeScript
- Vite for build tooling
- TailwindCSS for styling
- Radix UI for accessible components

### 2. State Management
- React Context for global state
- Key Contexts:
  - AuthContext - Authentication state
  - PortfolioContext - Portfolio management
  - BackgroundContext - UI state

### 3. Backend Services
- Firebase Integration:
  - Authentication
  - Firestore Database
  - Storage
  - Analytics
  - Performance Monitoring

### 4. File Structure
```
src/
├── components/     # Reusable React components
├── contexts/       # React context providers
├── hooks/         # Custom React hooks
├── lib/           # Utility functions
├── pages/         # Page components
├── services/      # Backend service integrations
├── styles/        # Global styles
├── types/         # TypeScript definitions
└── utils/         # Helper functions
```

## Core Components Documentation

### Dashboard Layout
- File: `src/components/DashboardLayout`
- Purpose: Main layout wrapper for authenticated sections
- Features: Navigation, user context, responsive design

### Portfolio Components
- Enhanced Portfolio Form (`src/components/portfolio/EnhancedPortfolioForm`)
  - Modern portfolio creation interface
  - Media upload integration
  - Auto-save functionality
- Portfolio Media Uploader (`src/components/portfolio/PortfolioMediaUploader`)
  - Handles image and video uploads
  - Optimization and compression
  - Firebase Storage integration

### Tool Management
- Combined Tool Tracker (`src/components/dashboard/CombinedToolTracker`)
  - Unified interface for personal and library tools
  - Usage tracking
  - Integration with AI services

### Analytics System
- File: `src/pages/Analytics`
- Features:
  - Usage analytics
  - Performance metrics
  - User insights

## Service Layer

### 1. Cleanup Services
- `services/scheduledCleanupService`
  - Automated file cleanup
  - Browser storage maintenance
  - Scheduled tasks

### 2. Storage Services
- `services/portfolioStorageService`
  - Media file management
  - Firebase Storage operations
  - Cache management

### 3. Security Rules
- `firebase.rules.json` - Firestore security rules
- `storage.rules` - Storage access control
- `firestore.indexes.json` - Database indexing

## Key Routes

```typescript
/                     # Landing page
/login               # Authentication
/dashboard           # Main dashboard
/dashboard/tools     # Tool management
/dashboard/portfolio # Portfolio management
/dashboard/explore   # Content discovery
/dashboard/academy   # Learning platform
/dashboard/analytics # Usage insights
```

## Subscription Features
- Free Tier:
  - Basic tool tracking
  - Simple portfolio
  - Limited storage

- SortMyAI+ (Premium):
  - Advanced portfolio customization
  - Smart suggestions
  - Usage analytics
  - Priority updates
  - Enhanced storage

## Development Guidelines

### 1. Code Standards
- TypeScript with strict mode
- Functional programming patterns
- Comprehensive error handling
- Component-based architecture

### 2. Testing
- Unit tests with Vitest/Jest
- Integration testing
- Performance monitoring

### 3. Security
- Firebase security rules
- Input validation
- Data sanitization
- Access control

## Deployment and Maintenance

### 1. Build Process
- Vite configuration
- Environment setup
- Build optimization

### 2. Monitoring
- Firebase Analytics integration
- Performance tracking
- Error logging

### 3. Updates
- Automated cleanup processes
- Regular maintenance tasks
- Cache management
- Storage optimization

## Support and Resources
- Error handling documentation
- Troubleshooting guides
- User support system
- Admin tools documentation

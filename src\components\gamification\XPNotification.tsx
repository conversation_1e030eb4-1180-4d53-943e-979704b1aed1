import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface XPNotificationProps {
  xpEarned: number;
  description: string;
  levelUp?: boolean;
  newLevel?: number;
  streakBonus?: number;
  show: boolean;
  onComplete?: () => void;
}

const XPNotification: React.FC<XPNotificationProps> = ({
  xpEarned,
  description,
  levelUp = false,
  newLevel,
  streakBonus,
  show,
  onComplete
}) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (show) {
      setVisible(true);
      const timer = setTimeout(() => {
        setVisible(false);
        setTimeout(() => onComplete?.(), 300); // Wait for exit animation
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [show, onComplete]);

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: -50, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -50, scale: 0.8 }}
          className="fixed top-20 right-4 z-50 max-w-sm"
        >
          <div className="bg-gradient-to-r from-sortmy-darker to-sortmy-blue/20 border border-sortmy-blue/30 rounded-lg p-4 shadow-lg backdrop-blur-sm">
            {levelUp ? (
              // Level Up Notification
              <div className="flex items-center space-x-3">
                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-2 rounded-full">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">Level Up! 🚀</h3>
                  <p className="text-sm text-gray-300">
                    Congratulations! You've reached Level {newLevel}!
                  </p>
                  <p className="text-xs text-sortmy-blue">+{xpEarned} XP</p>
                </div>
              </div>
            ) : (
              // Regular XP Notification
              <div className="flex items-center space-x-3">
                <div className="bg-gradient-to-r from-sortmy-blue to-blue-500 p-2 rounded-full">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">
                    +{xpEarned} XP Earned!
                    {streakBonus && streakBonus > 0 && (
                      <span className="text-orange-400"> (+{streakBonus} streak bonus)</span>
                    )}
                  </h3>
                  <p className="text-xs text-gray-300">{description}</p>
                </div>
              </div>
            )}

            {/* Animated progress bar */}
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 3, ease: 'linear' }}
              className="h-1 bg-gradient-to-r from-sortmy-blue to-blue-500 rounded-full mt-3"
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default XPNotification;

// Hook for managing XP notifications
export const useXPNotifications = () => {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    xpEarned: number;
    description: string;
    levelUp?: boolean;
    newLevel?: number;
    streakBonus?: number;
  }>>([]);

  const showXPNotification = (
    xpEarned: number,
    description: string,
    options?: {
      levelUp?: boolean;
      newLevel?: number;
      streakBonus?: number;
    }
  ) => {
    const id = Date.now().toString();
    const notification = {
      id,
      xpEarned,
      description,
      ...options
    };

    setNotifications(prev => [...prev, notification]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const NotificationContainer = () => (
    <div className="fixed top-20 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <XPNotification
          key={notification.id}
          xpEarned={notification.xpEarned}
          description={notification.description}
          levelUp={notification.levelUp}
          newLevel={notification.newLevel}
          streakBonus={notification.streakBonus}
          show={true}
          onComplete={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );

  return {
    showXPNotification,
    NotificationContainer
  };
};

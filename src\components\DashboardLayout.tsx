import { useEffect, useState } from 'react';
import { useBackground } from '@/contexts/BackgroundContext';
import { NavLink } from 'react-router-dom';
import {
  Home,
  MessageSquare,
  User,
  Settings,
  LayoutGrid,
  Briefcase,
  Globe,
  Sparkles,
  PlusCircle,
  Users,
  BookOpen,
  X,
  Menu,
} from 'lucide-react';
import SortMyAILogo from './ui/SortMyAILogo';
import { useMessageNotifications } from '@/contexts/MessageNotificationContext';
import NotificationBadge from '@/components/ui/notification-badge';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/contexts/AuthContext';
import { AuroraBackground } from './ui/AuroraBackground';
import { Notifications } from './dashboard/Notifications';
import XPMissions from './gamification/XPMissions';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const isMobile = useIsMobile();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const isPublicView = document.body.classList.contains('public-profile-view');
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';

  const mainSidebarItems = [
    { icon: <Home size={20} />, label: "Control Panel", path: "/dashboard" },
    { icon: <Briefcase size={20} />, label: "Tool Tracker", path: "/dashboard/tools" },
    { icon: <LayoutGrid size={20} />, label: "Portfolio", path: "/dashboard/portfolio" },
    { icon: <Globe size={20} />, label: "Explore", path: "/dashboard/explore" },
    { icon: <MessageSquare size={20} />, label: "Messages", path: "/dashboard/messages" },
    { icon: <BookOpen size={20} />, label: "Academy", path: "/dashboard/academy" },
    { icon: <User size={20} />, label: "Profile", path: "/dashboard/profile" },
    { icon: <Settings size={20} />, label: "Settings", path: "/dashboard/settings" },
  ];

  const adminSidebarItems = [
    { icon: <PlusCircle size={20} />, label: "Create Course", path: "/admin/courses" },
    { icon: <Users size={20} />, label: "Manage Users", path: "/admin/users" },
  ];

  const SidebarContent = () => {
    const { totalUnreadMessages } = useMessageNotifications();

    return (
      <div className="flex flex-col h-full">
        <div className="flex items-center mb-8 py-2">
          <div className="relative">
            <SortMyAILogo className="w-8 h-8 mr-2 text-sortmy-blue" />
          </div>
          <span className="text-xl font-bold tracking-tight">
            <span className="text-white">SortMy</span>
            <span className="text-sortmy-blue">AI</span>
          </span>
        </div>

        <div className="flex-1 flex flex-col space-y-1">
          {/* Main Navigation */}
          <div className="space-y-1">
            {mainSidebarItems.map((item, i) => (
              <NavLink
                key={i}
                to={item.path}
                className={({ isActive }) => `
                  flex items-center gap-3 py-3 px-4 rounded-md transition-all duration-300 relative
                  ${isActive
                    ? 'bg-sortmy-blue/10 text-sortmy-blue border-l-2 border-sortmy-blue pl-3 shadow-[0_0_10px_rgba(0,102,255,0.2)] hover:shadow-[0_0_15px_rgba(0,102,255,0.3)]'
                    : 'hover:bg-sortmy-blue/5 text-gray-300 hover:text-white hover:translate-x-1 hover:border-l hover:border-sortmy-blue/30'}
                `}
                onClick={() => isMobile && setIsMenuOpen(false)}
              >
                <div className="relative">
                  {item.icon}
                  {item.label === 'Messages' && totalUnreadMessages > 0 && (
                    <NotificationBadge count={totalUnreadMessages} />
                  )}
                </div>
                <span>{item.label}</span>
              </NavLink>
            ))}
          </div>

          {/* Admin Section */}
          {isAdmin && (
            <>
              <div className="mt-8 mb-2 px-4">
                <h3 className="text-sm font-semibold text-gray-400 uppercase">Admin</h3>
              </div>
              <div className="space-y-1">
                {adminSidebarItems.map((item, i) => (
                  <NavLink
                    key={i}
                    to={item.path}
                    className={({ isActive }) => `
                      flex items-center gap-3 py-3 px-4 rounded-md transition-all duration-300 relative
                      ${isActive
                        ? 'bg-purple-500/10 text-purple-400 border-l-2 border-purple-500 pl-3 shadow-[0_0_10px_rgba(147,51,234,0.2)] hover:shadow-[0_0_15px_rgba(147,51,234,0.3)]'
                        : 'hover:bg-purple-500/5 text-gray-300 hover:text-white hover:translate-x-1 hover:border-l hover:border-purple-500/30'}
                    `}
                    onClick={() => isMobile && setIsMenuOpen(false)}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </NavLink>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  // Use the background context
  const { backgroundType, setBackgroundType, setBackgroundIntensity } = useBackground();

  // Adjust background based on the current page
  useEffect(() => {
    const path = window.location.pathname;
    if (path.includes('/dashboard/achievements')) {
      setBackgroundIntensity('high');
    } else if (path.includes('/dashboard/academy') || path.includes('/dashboard/portfolio')) {
      setBackgroundIntensity('medium');
    } else {
      setBackgroundIntensity('low');
    }
  }, [window.location.pathname]);
  if (isPublicView) {
    return <>{children}</>;
  }

  return (
    <div className="flex h-screen bg-sortmy-dark text-white overflow-hidden relative dashboard-layout">
      {/* Background */}      {backgroundType === 'aurora' ? (
        <div className="absolute inset-0 overflow-hidden">
          <AuroraBackground intensity={50} className="z-0" />
        </div>
      ) : (
        <div className="fixed inset-0 bg-sortmy-dark z-0">
          <div className="absolute inset-0 bg-gradient-to-b from-[#0d001a] to-[#0a0a2e] opacity-80"></div>
          <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
        </div>
      )}

      {/* Scanline effect */}
      <div className="scanline-effect z-[1]"></div>

      {/* Mobile Menu Toggle */}
      {isMobile && (
        <Button
          variant="ghost"
          size="icon"
          className="fixed top-4 left-4 z-50"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </Button>
      )}

      {/* Desktop Sidebar */}
      {!isMobile && (
        <div className="border-r border-sortmy-blue/20 bg-sortmy-darker/70 backdrop-blur-md w-64 flex-shrink-0 p-4 z-10 shadow-[0_0_15px_rgba(0,102,255,0.1)]">
          {/* Subtle scanline effect */}
          <div className="absolute inset-0 pointer-events-none z-[-1] bg-scanline opacity-5"></div>
          <SidebarContent />
        </div>
      )}

      {/* Mobile Sidebar using Sheet component */}
      {isMobile && (
        <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
          <SheetContent side="left" className="w-[80%] border-r border-sortmy-blue/20 bg-sortmy-darker/70 backdrop-blur-md p-4 shadow-[0_0_15px_rgba(0,102,255,0.1)]">
            {/* Subtle scanline effect */}
            <div className="absolute inset-0 pointer-events-none z-[-1] bg-scanline opacity-5"></div>
            <SidebarContent />
          </SheetContent>
        </Sheet>
      )}

      {/* Removed old header and background toggle button as they're now in the new header */}

      {/* Main Content with padding adjustment for mobile */}
      <div className="flex-1 flex flex-col">
        <div className={`flex-1 p-4 overflow-y-auto ${isMobile ? 'pt-16' : ''} z-10 relative bg-sortmy-dark/20 backdrop-blur-[2px]`}>
          <div className="max-w-7xl mx-auto relative">
            {/* Subtle scanline effect */}
            <div className="absolute inset-0 pointer-events-none z-[-1] bg-scanline opacity-10"></div>
            {children}
          </div>
        </div>

        {/* Bottom notification bar */}
        <div className="px-4 py-1.5 bg-sortmy-darker/90 backdrop-blur-md border-t border-sortmy-blue/20 z-20">
          <div className="flex items-center justify-end max-w-7xl mx-auto gap-2">
            {/* XP Missions */}
            <div className="relative flex items-center">
              <XPMissions />
            </div>
            <div className="h-3 w-px bg-sortmy-blue/20" />
            <div className="relative flex items-center">
              {/* Style wrapper for Notifications to control its positioning */}
              <div className="[&_button]:h-5 [&_button]:w-5 [&_button]:hover:bg-sortmy-blue/10 [&_.notification-content]:!absolute [&_.notification-content]:!bottom-[calc(100%+0.5rem)] [&_.notification-content]:!top-auto [&_.notification-content]:!left-auto [&_.notification-content]:!right-0 [&_.notification-content]:!transform-none">
                <Notifications />
              </div>
            </div>
            <div className="h-3 w-px bg-sortmy-blue/20" />
            <Button
              variant="ghost"
              size="icon"
              className="h-5 w-5 hover:bg-sortmy-blue/10"
              onClick={() => setBackgroundType(backgroundType === 'aurora' ? 'simple' : 'aurora')}
              title="Toggle Background Style"
            >
              <Sparkles className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;


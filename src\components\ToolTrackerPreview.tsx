import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Brain, PlusCircle, Search } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";

const ToolTrackerPreview = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const tools = [
    {
      id: "1",
      name: "<PERSON><PERSON>",
      category: "AI Video",
      logo_url: "https://framerusercontent.com/images/nXLdc1LsU15dk6AODEpMgByLDw.png",
      website: "https://pika.art"
    },
    {
      id: "2",
      name: "ElevenLabs",
      category: "Voice Generation",
      logo_url: "https://storage.googleapis.com/eleven-public-prod/logos/eleven-labs-logo.png",
      website: "https://elevenlabs.io"
    },
    {
      id: "3",
      name: "Midjourney",
      category: "Image Generation",
      logo_url: "https://upload.wikimedia.org/wikipedia/commons/e/e6/Midjourney_Emblem.png",
      website: "https://midjourney.com"
    },
    {
      id: "4",
      name: "ChatGPT",
      category: "AI Assistant",
      logo_url: "https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/ChatGPT_logo.svg/1024px-ChatGPT_logo.svg.png",
      website: "https://chat.openai.com"
    },
    {
      id: "5",
      name: "DALL-E 3",
      category: "Image Generation",
      logo_url: "https://seeklogo.com/images/D/dall-e-logo-1DD62EE45A-seeklogo.com.png",
      website: "https://openai.com/dall-e-3"
    }
  ];

  // Using underscore prefix to indicate intentionally unused parameter
  const handleAddTool = (_toolId: string, toolName: string) => {
    if (!user) {
      toast({
        title: "Sign in required",
        description: "You need to sign in to add tools to your collection",
        variant: "default"
      });
      navigate('/signup');
      return;
    }

    toast({
      title: `${toolName} added`,
      description: "Tool has been added to your collection",
      variant: "success"
    });
  };

  // Add event listener for the addTool event
  React.useEffect(() => {
    const toolTrackerPreview = document.getElementById('tool-tracker-preview');

    const handleAddToolEvent = (event: any) => {
      const { toolId, toolName } = event.detail;
      handleAddTool(toolId, toolName);
    };

    if (toolTrackerPreview) {
      toolTrackerPreview.addEventListener('addTool', handleAddToolEvent);
    }

    return () => {
      if (toolTrackerPreview) {
        toolTrackerPreview.removeEventListener('addTool', handleAddToolEvent);
      }
    };
  }, [user, handleAddTool]);

  return (
    <section className="py-20 relative overflow-hidden bg-sortmy-dark">
      <div className="container mx-auto px-4">
        <div id="tool-tracker-preview" className="max-w-5xl mx-auto relative">
          {/* Brain background with moderate opacity */}
          <div className="absolute inset-0 flex items-center justify-center opacity-10 z-0 pointer-events-none">
            <Brain className="w-64 h-64 text-sortmy-blue" />
          </div>

          <div className="text-center mb-12 relative z-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Create your own personal library of <span className="text-sortmy-blue">AI tools</span></h2>
            <p className="text-xl text-gray-300 mb-8">
              Keep track of all your AI tools in one place, organize them by use case, and never forget that perfect tool again.
            </p>
          </div>

          {/* Search Bar */}
          <div className="relative w-full mb-8">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-[#01AAE9] h-4 w-4 z-10" />
            <input
              placeholder="Search tools by name, description, or tag..."
              className="pl-10 w-full py-3 bg-sortmy-dark/80 border border-[#01AAE9]/30 focus:border-[#01AAE9]/60 focus:outline-none focus:ring-2 focus:ring-[#01AAE9]/20 rounded-md text-white shadow-inner"
            />
          </div>

          {/* Your Tools - Empty State */}
          <div className="p-4 bg-sortmy-darker/70 border border-[#01AAE9]/30 rounded-xl mb-8 relative z-10 shadow-[0_0_30px_rgba(1,170,233,0.1)]">
            <h3 className="text-xl font-semibold text-white mb-2">Your Tools</h3>
            <div className="flex flex-col items-center justify-center py-2">
              <div className="w-12 h-12 rounded-full bg-sortmy-darker/80 flex items-center justify-center mb-2">
                <PlusCircle className="w-6 h-6 text-[#01AAE9]/50" />
              </div>
              <p className="text-gray-400 text-sm mb-1">You haven't added any tools yet</p>
              <p className="text-gray-500 text-xs mb-3">Browse the library below to add tools to your collection</p>
              <Link to={user ? "/dashboard/tools/add" : "/signup"}>
                <Button variant="outline" className="border-[#01AAE9]/30 text-[#01AAE9] hover:bg-[#01AAE9]/10 text-sm py-1">
                  <PlusCircle className="w-3 h-3 mr-2" />
                  Add Your First Tool
                </Button>
              </Link>
            </div>
          </div>

          {/* Tool Library Grid */}
          <div className="p-6 bg-sortmy-darker/70 border border-[#01AAE9]/30 rounded-xl mb-8 relative z-10 shadow-[0_0_30px_rgba(1,170,233,0.1)]">
            <h3 className="text-xl font-semibold text-white mb-4">Tool Library</h3>
            <div className="grid grid-cols-5 gap-8 justify-items-center">
              {tools.map(tool => (
                <ToolIcon key={tool.id} tool={tool} isLibraryTool={true} user={user} />
              ))}
            </div>
          </div>

          {/* Action Button */}
          <div className="text-center relative z-10 mt-6">
            <Button
              className="bg-[#01AAE9] hover:bg-[#01AAE9]/90 text-white group relative px-6 py-6 text-lg"
              onClick={() => {
                if (user) {
                  navigate('/dashboard/tools');
                } else {
                  toast({
                    title: "Sign in required",
                    description: "You need to sign in to access all tools",
                    variant: "default"
                  });
                  navigate('/signup');
                }
              }}
            >
              <span className="relative z-10 flex items-center">
                {user ? 'Explore All Tools' : 'Sign Up to Access Tools'}
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

// Simple ToolIcon component for the preview
const ToolIcon = ({
  tool,
  isLibraryTool = false,
  user: _user
}: {
  tool: { id: string, name: string, category: string, logo_url: string, website?: string },
  isLibraryTool?: boolean,
  user: any
}) => {
  const handleToolClick = () => {
    if (tool.website) {
      window.open(tool.website, '_blank', 'noopener,noreferrer');
    }
  };

  // Generate a consistent gradient based on the first letter of the tool name  // Generate a consistent gradient based on tool category
  const getGradient = (name: string) => {
    const category = tool.category?.toLowerCase() || '';

    // Map category to specific gradients for consistency
    switch (category) {
      case 'ai assistant':
      case 'chat':
        return "from-blue-500 to-cyan-400";  // Blue for AI/Chat tools
      case 'image generation':
      case 'image editing':
        return "from-purple-500 to-pink-500";  // Purple for Image tools
      case 'audio':
      case 'voice generation':
        return "from-emerald-500 to-teal-400";  // Green for Audio tools
      case 'video':
      case 'video editing':
        return "from-orange-500 to-amber-400";  // Orange for Video tools
      case 'writing':
      case 'content':
        return "from-indigo-500 to-blue-400";  // Indigo for Writing tools
      case 'development':
      case 'coding':
        return "from-rose-500 to-pink-400";  // Rose for Development tools
      case 'productivity':
      case 'automation':
        return "from-cyan-500 to-blue-400";  // Cyan for Productivity tools
      default:
        // Fallback to consistent gradient based on first letter
        const firstChar = name.charAt(0).toUpperCase();
        const charCode = firstChar.charCodeAt(0);
        const gradients = [
          "from-blue-500 to-indigo-400",
          "from-purple-500 to-blue-400",
          "from-indigo-500 to-purple-400"
        ];
        return gradients[charCode % gradients.length];
    }
  };

  const gradient = getGradient(tool.name);
  const firstLetter = tool.name.charAt(0).toUpperCase();

  return (
    <div className="flex flex-col items-center text-center group">
      {/* App Icon */}
      <div
        className={`w-16 h-16 rounded-2xl overflow-hidden bg-gradient-to-br ${gradient} mb-3 shadow-lg cursor-pointer relative transition-all duration-300 group-hover:scale-105 group-hover:shadow-xl group-hover:shadow-blue-500/20`}
        onClick={handleToolClick}
      >
        {tool.logo_url ? (
          <img
            src={tool.logo_url}
            alt={`${tool.name} logo`}
            className="w-full h-full object-cover" onError={(e) => {
              // If image fails to load, show the first letter of the tool name in a centered container
              e.currentTarget.style.display = 'none';
              e.currentTarget.parentElement!.innerHTML = `<div class="w-full h-full flex items-center justify-center"><span class="text-2xl font-bold text-white">${firstLetter}</span></div>`;
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <span className="text-2xl font-bold text-white">{firstLetter}</span>
          </div>
        )}

        {/* Subtle shine effect */}
        <div className="absolute inset-0 bg-gradient-to-tr from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      {/* App Name */}
      <span className="text-sm font-medium text-white truncate max-w-[90px]">
        {tool.name}
      </span>

      {/* Add button for library tools */}
      {isLibraryTool && (
        <button
          className="mt-2 bg-[#01AAE9] hover:bg-[#01AAE9]/90 text-white text-xs px-3 py-1 rounded-md flex items-center gap-1"
          onClick={(e) => {
            e.stopPropagation();
            // Access the handleAddTool function from the parent component
            const toolTrackerPreview = document.getElementById('tool-tracker-preview');
            if (toolTrackerPreview) {
              const event = new CustomEvent('addTool', {
                detail: { toolId: tool.id, toolName: tool.name }
              });
              toolTrackerPreview.dispatchEvent(event);
            }
          }}
        >
          <PlusCircle className="w-3 h-3 mr-1" />
          <span>Add</span>
        </button>
      )}
    </div>
  );
};

export default ToolTrackerPreview;

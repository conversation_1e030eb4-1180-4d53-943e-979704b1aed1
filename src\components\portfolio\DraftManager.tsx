import { useState, useEffect, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { portfolioStorageService } from '@/services/portfolioStorageService';
import { 
    Save, 
    Trash2, 
    Eye, 
    Clock, 
    FileText, 
    Image as ImageIcon, 
    Video,
    AlertCircle,
    CheckCircle
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface PortfolioDraft {
    id?: string;
    userId: string;
    title: string;
    description: string;
    media_url?: string;
    media_urls?: string[];
    media_type?: 'image' | 'video' | 'audio';
    content_type: 'post' | 'reel' | 'both';
    tools_used: string[];
    tagged_toolkits?: string[];
    project_url?: string;
    is_public: boolean;
    created_at: string;
    updated_at: string;
    auto_save_count: number;
}

interface DraftManagerProps {
    userId: string;
    currentDraft?: Partial<PortfolioDraft>;
    onDraftSelect: (draft: PortfolioDraft) => void;
    onDraftDelete: (draftId: string) => void;
    onDraftPublish: (draftId: string) => void;
    className?: string;
}

export function DraftManager({
    userId,
    currentDraft,
    onDraftSelect,
    onDraftDelete,
    onDraftPublish,
    className = ''
}: DraftManagerProps) {
    const [drafts, setDrafts] = useState<PortfolioDraft[]>([]);
    const [loading, setLoading] = useState(true);
    const [actionLoading, setActionLoading] = useState<string | null>(null);
    const { toast } = useToast();

    // Load drafts
    const loadDrafts = useCallback(async () => {
        try {
            setLoading(true);
            const userDrafts = await portfolioStorageService.getUserDrafts(userId);
            setDrafts(userDrafts);
        } catch (error) {
            console.error('Error loading drafts:', error);
            toast({
                title: 'Error',
                description: 'Failed to load drafts',
                variant: 'destructive'
            });
        } finally {
            setLoading(false);
        }
    }, [userId, toast]);

    useEffect(() => {
        loadDrafts();
    }, [loadDrafts]);

    // Auto-save current draft
    const autoSave = useCallback(async () => {
        if (!currentDraft || !currentDraft.title && !currentDraft.description && 
            !currentDraft.media_url && (!currentDraft.media_urls || currentDraft.media_urls.length === 0)) {
            return;
        }

        try {
            await portfolioStorageService.autoSaveDraft(userId, currentDraft);
            // Refresh drafts list
            await loadDrafts();
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    }, [userId, currentDraft, loadDrafts]);

    // Set up auto-save interval
    useEffect(() => {
        const interval = setInterval(autoSave, 30000); // Auto-save every 30 seconds
        return () => clearInterval(interval);
    }, [autoSave]);

    const handleDeleteDraft = async (draftId: string) => {
        try {
            setActionLoading(draftId);
            await portfolioStorageService.deleteDraft(draftId);
            await loadDrafts();
            onDraftDelete(draftId);
            toast({
                title: 'Draft deleted',
                description: 'Draft and associated media have been scheduled for cleanup'
            });
        } catch (error) {
            console.error('Error deleting draft:', error);
            toast({
                title: 'Error',
                description: 'Failed to delete draft',
                variant: 'destructive'
            });
        } finally {
            setActionLoading(null);
        }
    };

    const handlePublishDraft = async (draftId: string) => {
        try {
            setActionLoading(draftId);
            const portfolioId = await portfolioStorageService.publishDraft(draftId);
            await loadDrafts();
            onDraftPublish(portfolioId);
            toast({
                title: 'Draft published',
                description: 'Your portfolio item is now live!'
            });
        } catch (error) {
            console.error('Error publishing draft:', error);
            toast({
                title: 'Error',
                description: 'Failed to publish draft',
                variant: 'destructive'
            });
        } finally {
            setActionLoading(null);
        }
    };

    const getMediaIcon = (draft: PortfolioDraft) => {
        if (draft.media_type === 'video') return <Video className="w-4 h-4" />;
        if (draft.media_url || (draft.media_urls && draft.media_urls.length > 0)) {
            return <ImageIcon className="w-4 h-4" />;
        }
        return <FileText className="w-4 h-4" />;
    };

    const getContentTypeColor = (contentType: string) => {
        switch (contentType) {
            case 'reel': return 'bg-purple-100 text-purple-800';
            case 'both': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <Card className={`p-4 ${className}`}>
                <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="space-y-2">
                        {[1, 2, 3].map(i => (
                            <div key={i} className="h-16 bg-gray-200 rounded"></div>
                        ))}
                    </div>
                </div>
            </Card>
        );
    }

    return (
        <Card className={`p-4 ${className}`}>
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="font-semibold flex items-center gap-2">
                        <Save className="w-4 h-4" />
                        Drafts ({drafts.length})
                    </h3>
                    {drafts.length > 0 && (
                        <Button
                            onClick={loadDrafts}
                            variant="outline"
                            size="sm"
                        >
                            Refresh
                        </Button>
                    )}
                </div>

                {drafts.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                        <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No drafts yet</p>
                        <p className="text-sm">Your drafts will be auto-saved here</p>
                    </div>
                ) : (
                    <div className="space-y-3">
                        {drafts.map((draft) => (
                            <div
                                key={draft.id}
                                className="border rounded-lg p-3 hover:bg-gray-50 transition-colors"
                            >
                                <div className="flex items-start justify-between">
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center gap-2 mb-2">
                                            {getMediaIcon(draft)}
                                            <h4 className="font-medium truncate">
                                                {draft.title || 'Untitled Draft'}
                                            </h4>
                                            <Badge 
                                                variant="secondary" 
                                                className={getContentTypeColor(draft.content_type)}
                                            >
                                                {draft.content_type}
                                            </Badge>
                                        </div>
                                        
                                        {draft.description && (
                                            <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                                                {draft.description}
                                            </p>
                                        )}
                                        
                                        <div className="flex items-center gap-4 text-xs text-gray-500">
                                            <span className="flex items-center gap-1">
                                                <Clock className="w-3 h-3" />
                                                {formatDistanceToNow(new Date(draft.updated_at), { addSuffix: true })}
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <CheckCircle className="w-3 h-3" />
                                                {draft.auto_save_count} saves
                                            </span>
                                            {draft.tools_used.length > 0 && (
                                                <span>{draft.tools_used.length} tools</span>
                                            )}
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-1 ml-2">
                                        <Button
                                            onClick={() => onDraftSelect(draft)}
                                            variant="ghost"
                                            size="sm"
                                            title="Load draft"
                                        >
                                            <Eye className="w-4 h-4" />
                                        </Button>
                                        
                                        <Button
                                            onClick={() => handlePublishDraft(draft.id!)}
                                            variant="ghost"
                                            size="sm"
                                            disabled={actionLoading === draft.id}
                                            title="Publish draft"
                                            className="text-green-600 hover:text-green-700"
                                        >
                                            <CheckCircle className="w-4 h-4" />
                                        </Button>
                                        
                                        <Button
                                            onClick={() => handleDeleteDraft(draft.id!)}
                                            variant="ghost"
                                            size="sm"
                                            disabled={actionLoading === draft.id}
                                            title="Delete draft"
                                            className="text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                                
                                {draft.media_urls && draft.media_urls.length > 1 && (
                                    <div className="mt-2 text-xs text-blue-600">
                                        {draft.media_urls.length} media files
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}

                {drafts.length > 5 && (
                    <div className="text-center pt-4 border-t">
                        <div className="flex items-center justify-center gap-2 text-sm text-amber-600">
                            <AlertCircle className="w-4 h-4" />
                            <span>Drafts older than 7 days are automatically cleaned up</span>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    );
}

// Hook for managing draft state
export function useDraftManager(userId: string) {
    const [currentDraftId, setCurrentDraftId] = useState<string | null>(null);
    const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

    const saveDraft = useCallback(async (draftData: Partial<PortfolioDraft>) => {
        try {
            setAutoSaveStatus('saving');
            const draftId = await portfolioStorageService.saveDraft(userId, {
                ...draftData,
                id: currentDraftId || undefined
            });
            setCurrentDraftId(draftId);
            setAutoSaveStatus('saved');
            
            // Reset status after 2 seconds
            setTimeout(() => setAutoSaveStatus('idle'), 2000);
            
            return draftId;
        } catch (error) {
            setAutoSaveStatus('error');
            setTimeout(() => setAutoSaveStatus('idle'), 2000);
            throw error;
        }
    }, [userId, currentDraftId]);

    const clearCurrentDraft = useCallback(() => {
        setCurrentDraftId(null);
        setAutoSaveStatus('idle');
    }, []);

    return {
        currentDraftId,
        autoSaveStatus,
        saveDraft,
        clearCurrentDraft
    };
}


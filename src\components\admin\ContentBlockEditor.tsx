import { useState, useEffect } from 'react';
import { ContentBlock } from '@/types/academy';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PlusCircle, Trash } from 'lucide-react';
import { AspectRatio } from '@/components/ui/aspect-ratio';

interface ContentBlockEditorProps {
  block: ContentBlock;
  onChange: (block: ContentBlock) => void;
}

const ContentBlockEditor = ({ block, onChange }: ContentBlockEditorProps) => {
  // Create a deep copy to avoid mutating props directly
  const [editableBlock, setEditableBlock] = useState<ContentBlock>({ ...block });

  // Update parent component when block changes
  useEffect(() => {
    onChange(editableBlock);
  }, [editableBlock, onChange]);

  const handleChange = (field: string, value: any) => {
    setEditableBlock(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOptionChange = (index: number, value: string) => {
    if (!editableBlock.options) return;

    const updatedOptions = [...editableBlock.options];
    updatedOptions[index] = value;

    setEditableBlock(prev => ({
      ...prev,
      options: updatedOptions
    }));
  };

  const handleAddOption = () => {
    if (!editableBlock.options) {
      setEditableBlock(prev => ({
        ...prev,
        options: ['New option']
      }));
      return;
    }

    setEditableBlock(prev => ({
      ...prev,
      options: [...prev.options!, 'New option']
    }));
  };

  const handleRemoveOption = (index: number) => {
    if (!editableBlock.options) return;

    const updatedOptions = [...editableBlock.options];
    updatedOptions.splice(index, 1);

    // Adjust correctOption if needed
    let correctOption = editableBlock.correctOption;
    if (correctOption !== undefined) {
      if (index === correctOption) {
        correctOption = 0; // Default to first option if the correct one was removed
      } else if (index < correctOption) {
        correctOption--; // Shift down if an option before the correct one was removed
      }
    }

    setEditableBlock(prev => ({
      ...prev,
      options: updatedOptions,
      correctOption
    }));
  };

  // Render different editors based on content type
  const renderEditor = () => {
    switch (editableBlock.type) {
      case 'text':
        return (
          <div className="space-y-3">
            <Textarea
              value={editableBlock.content}
              onChange={(e) => handleChange('content', e.target.value)}
              placeholder="Enter your text content here..."
              rows={5}
              className="w-full resize-y min-h-[150px]"
            />
            <div className="space-y-2">
              <Label htmlFor="text-caption">Caption (optional)</Label>
              <Input
                id="text-caption"
                value={editableBlock.caption || ''}
                onChange={(e) => handleChange('caption', e.target.value)}
                placeholder="Add a caption for this text section"
              />
            </div>
          </div>
        );

      case 'image':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="image-url">Image URL</Label>
              <Input
                id="image-url"
                value={editableBlock.content}
                onChange={(e) => handleChange('content', e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>

            {editableBlock.content && (
              <div className="mt-2 border rounded-lg overflow-hidden">
                <AspectRatio ratio={16 / 9}>
                  <img
                    src={editableBlock.content}
                    alt="Preview"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/images/placeholder.png';
                    }}
                  />
                </AspectRatio>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="image-caption">Caption</Label>
              <Input
                id="image-caption"
                value={editableBlock.caption || ''}
                onChange={(e) => handleChange('caption', e.target.value)}
                placeholder="Add a descriptive caption for this image"
              />
            </div>
          </div>
        );

      case 'video':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="video-url">Video URL</Label>
              <Input
                id="video-url"
                value={editableBlock.content}
                onChange={(e) => handleChange('content', e.target.value)}
                placeholder="https://example.com/video.mp4 or YouTube URL"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="video-caption">Caption</Label>
              <Input
                id="video-caption"
                value={editableBlock.caption || ''}
                onChange={(e) => handleChange('caption', e.target.value)}
                placeholder="Add a descriptive caption for this video"
              />
            </div>
          </div>
        );

      case 'quiz':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="quiz-question">Question</Label>
              <Textarea
                id="quiz-question"
                value={editableBlock.content}
                onChange={(e) => handleChange('content', e.target.value)}
                placeholder="Enter your quiz question here..."
                rows={2}
              />
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Label>Options</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddOption}
                  className="h-8 text-xs bg-sortmy-blue/10 text-sortmy-blue hover:bg-sortmy-blue/20 border-sortmy-blue/20"
                >
                  <PlusCircle className="h-3.5 w-3.5 mr-1.5" />
                  Add Option
                </Button>
              </div>

              {editableBlock.options?.map((option, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <Input
                        value={option}
                        onChange={(e) => handleOptionChange(index, e.target.value)}
                        placeholder={`Option ${index + 1}`}
                        className={editableBlock.correctOption === index ? "border-green-500" : ""}
                      />
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      type="button"
                      variant={editableBlock.correctOption === index ? "default" : "outline"}
                      size="sm"
                      className={`h-8 text-xs ${editableBlock.correctOption === index ? "bg-green-600 hover:bg-green-700" : "text-gray-400 hover:text-gray-300"}`}
                      onClick={() => handleChange('correctOption', index)}
                    >
                      Correct
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => handleRemoveOption(index)}
                      className="h-8 w-8 text-red-500 hover:text-red-600"
                      disabled={editableBlock.options?.length === 1}
                    >
                      <Trash className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                </div>
              ))}

              {(!editableBlock.options || editableBlock.options.length === 0) && (
                <div className="flex justify-center py-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleAddOption}
                    className="bg-sortmy-blue/10 text-sortmy-blue hover:bg-sortmy-blue/20 border-sortmy-blue/20"
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    Add Your First Option
                  </Button>
                </div>
              )}
            </div>
          </div>
        );

      case 'code':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="code-language">Language</Label>
                <Select
                  value={editableBlock.codeLanguage || 'javascript'}
                  onValueChange={(value) => handleChange('codeLanguage', value)}
                >
                  <SelectTrigger id="code-language" className="w-[180px]">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="javascript">JavaScript</SelectItem>
                    <SelectItem value="typescript">TypeScript</SelectItem>
                    <SelectItem value="python">Python</SelectItem>
                    <SelectItem value="java">Java</SelectItem>
                    <SelectItem value="csharp">C#</SelectItem>
                    <SelectItem value="cpp">C++</SelectItem>
                    <SelectItem value="go">Go</SelectItem>
                    <SelectItem value="rust">Rust</SelectItem>
                    <SelectItem value="php">PHP</SelectItem>
                    <SelectItem value="ruby">Ruby</SelectItem>
                    <SelectItem value="swift">Swift</SelectItem>
                    <SelectItem value="kotlin">Kotlin</SelectItem>
                    <SelectItem value="html">HTML</SelectItem>
                    <SelectItem value="css">CSS</SelectItem>
                    <SelectItem value="bash">Bash</SelectItem>
                    <SelectItem value="sql">SQL</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="code-content">Code</Label>
              <Textarea
                id="code-content"
                value={editableBlock.content}
                onChange={(e) => handleChange('content', e.target.value)}
                placeholder="Enter your code snippet here..."
                rows={5}
                className="font-mono text-sm"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="code-caption">Caption</Label>
              <Input
                id="code-caption"
                value={editableBlock.caption || ''}
                onChange={(e) => handleChange('caption', e.target.value)}
                placeholder="Add a descriptive caption for this code snippet"
              />
            </div>
          </div>
        );

      case 'embed':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="embed-type">Embed Type</Label>
                <Select
                  value={editableBlock.embedType || 'youtube'}
                  onValueChange={(value) => handleChange('embedType', value)}
                >
                  <SelectTrigger id="embed-type" className="w-[180px]">
                    <SelectValue placeholder="Select embed type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="youtube">YouTube</SelectItem>
                    <SelectItem value="vimeo">Vimeo</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="embed-content">
                {editableBlock.embedType === 'youtube' ? 'YouTube Video ID' :
                  editableBlock.embedType === 'vimeo' ? 'Vimeo Video ID' :
                    'Embed URL or Code'}
              </Label>
              <Input
                id="embed-content"
                value={editableBlock.content}
                onChange={(e) => handleChange('content', e.target.value)}
                placeholder={editableBlock.embedType === 'youtube' ? 'e.g., dQw4w9WgXcQ' :
                  editableBlock.embedType === 'vimeo' ? 'e.g., 148751763' :
                    'https://example.com/embed or <iframe>...</iframe>'}
              />
            </div>

            {editableBlock.embedType === 'youtube' && editableBlock.content && (
              <div className="mt-2 border rounded-lg overflow-hidden">
                <AspectRatio ratio={16 / 9}>
                  <iframe
                    src={`https://www.youtube.com/embed/${editableBlock.content}`}
                    title="YouTube video"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    className="w-full h-full"
                  ></iframe>
                </AspectRatio>
              </div>
            )}

            {editableBlock.embedType === 'vimeo' && editableBlock.content && (
              <div className="mt-2 border rounded-lg overflow-hidden">
                <AspectRatio ratio={16 / 9}>
                  <iframe
                    src={`https://player.vimeo.com/video/${editableBlock.content}`}
                    title="Vimeo video"
                    allow="autoplay; fullscreen; picture-in-picture"
                    allowFullScreen
                    className="w-full h-full"
                  ></iframe>
                </AspectRatio>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="embed-caption">Caption</Label>
              <Input
                id="embed-caption"
                value={editableBlock.caption || ''}
                onChange={(e) => handleChange('caption', e.target.value)}
                placeholder="Add a descriptive caption for this embedded content"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="p-4 border border-gray-700 rounded-lg">
            <p className="text-red-400">Unknown content type: {editableBlock.type}</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      {renderEditor()}
    </div>
  );
};

export default ContentBlockEditor;
// Give the service worker access to Firebase Messaging.
importScripts('https://www.gstatic.com/firebasejs/10.5.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.5.0/firebase-messaging-compat.js');

// Default configuration that will be overridden
let firebaseConfig = {
  apiKey: "AIzaSyCSSBKFkrnBoK0b1Y3RmA97WdwcY9YLKcA",
  authDomain: "smai-og.firebaseapp.com",
  projectId: "smai-og",
  storageBucket: "smai-og.firebasestorage.app",
  messagingSenderId: "220186510992",
  appId: "1:220186510992:web:3d9e07c3df55d1f4ea7a15",
  measurementId: "G-4MR0WK595H"
};

let messaging = null;

// Listen for messages from the main thread
self.addEventListener('message', (event) => {
  if (event.data?.type === 'FIREBASE_CONFIG') {
    // Only update the config if we receive a valid one
    if (event.data.config) {
      firebaseConfig = event.data.config;
    }
    initializeFirebase();
  }
});

// Initialize Firebase
function initializeFirebase() {
  try {
    if (!firebase.apps.length) {
      firebase.initializeApp(firebaseConfig);
    }
    messaging = firebase.messaging();
    console.log('Firebase Messaging initialized in service worker');
    setupBackgroundListener();
  } catch (error) {
    console.error('Failed to initialize Firebase in service worker:', error);
  }
}

// Set up background message handler
function setupBackgroundListener() {
  if (!messaging) return;

  messaging.onBackgroundMessage(async (payload) => {
    console.log('[firebase-messaging-sw.js] Received background message:', payload);

    const notificationTitle = payload.notification?.title || 'New Message';
    const notificationOptions = {
      body: payload.notification?.body || '',
      icon: '/logo.png',
      badge: '/logo.png',
      tag: payload.data?.tag || 'default',
      data: payload.data || {},
      requireInteraction: true
    };

    try {
      await self.registration.showNotification(notificationTitle, notificationOptions);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  });
}

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification clicked:', event);

  event.notification.close();

  // This looks to see if the current is already open and focuses if it is
  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    })
      .then((clientList) => {
        // Get URL from notification data or use default
        const url = event.notification.data?.url || '/';

        // Try to focus an existing window first
        for (const client of clientList) {
          const clientUrl = new URL(client.url);
          const notificationUrl = new URL(url, self.location.origin);

          if (clientUrl.pathname === notificationUrl.pathname && 'focus' in client) {
            return client.focus();
          }
        }

        // If no window is open or matching URL not found, open a new one
        if (clients.openWindow) {
          // Ensure URL is absolute
          const absoluteUrl = new URL(url, self.location.origin).href;
          return clients.openWindow(absoluteUrl);
        }
      })
  );
});

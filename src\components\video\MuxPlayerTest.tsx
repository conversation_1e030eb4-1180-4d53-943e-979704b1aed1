import { useEffect, useState, useRef } from 'react';
import { MuxPlayer } from './MuxPlayer';
import { MuxUploader } from './MuxUploader';
import Hls from 'hls.js';

export function MuxPlayerTest() {
    const muxVideoRef = useRef<HTMLVideoElement>(null);
    const [muxHlsStatus, setMuxHlsStatus] = useState<string>('Initializing...');

    const muxTestUrl = "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8";

    // HLS.js setup for Mux test stream
    useEffect(() => {
        if (muxVideoRef.current) {
            const video = muxVideoRef.current;

            if (Hls.isSupported()) {
                setMuxHlsStatus('HLS.js supported, loading Mux test stream...');
                const hls = new Hls({
                    debug: true,
                    enableWorker: false,
                });

                hls.loadSource(muxTestUrl);
                hls.attachMedia(video);

                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    setMuxHlsStatus('✅ Mux test stream loaded successfully!');
                    console.log('Mux HLS manifest parsed, ready to play');
                });

                hls.on(Hls.Events.ERROR, (_, data) => {
                    setMuxHlsStatus(`❌ Mux HLS Error: ${data.type} - ${data.details}`);
                    console.error('Mux HLS error:', data);
                });

                return () => {
                    hls.destroy();
                };
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                setMuxHlsStatus('✅ Native HLS support detected for Mux');
                video.src = muxTestUrl;
            } else {
                setMuxHlsStatus('❌ HLS not supported in this browser');
            }
        }
    }, [muxTestUrl]);

    return (
        <div className="min-h-screen bg-gray-900 p-8">
            <div className="max-w-6xl mx-auto space-y-12">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-white mb-4">Mux Video Test Suite</h1>
                    <p className="text-gray-300">Testing Mux video streaming and upload functionality</p>
                </div>

                {/* Test 1: Mux Test Stream (HTML5 Video) */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-white">Test 1: Mux Test Stream (HTML5 Video)</h2>
                    <p className="text-sm text-gray-300">Using: https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8</p>
                    <div className="w-full max-w-2xl aspect-video">
                        <video
                            className="w-full h-full bg-black rounded-lg"
                            controls
                            preload="metadata"
                            crossOrigin="anonymous"
                        >
                            <source src="https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8" type="application/vnd.apple.mpegurl" />
                            <p className="text-white">Your browser doesn't support HLS video.</p>
                        </video>
                    </div>
                </div>

                {/* Test 2: Mux HLS.js Player */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-white">Test 2: Mux HLS.js Player</h2>
                    <p className="text-sm text-gray-300">Status: {muxHlsStatus}</p>
                    <div className="w-full max-w-2xl aspect-video">
                        <video
                            ref={muxVideoRef}
                            className="w-full h-full bg-black rounded-lg"
                            controls
                            preload="metadata"
                        />
                    </div>
                </div>

                {/* Test 3: MuxPlayer Component */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-white">Test 3: MuxPlayer Component</h2>
                    <p className="text-sm text-gray-300">Using custom MuxPlayer with playback ID: x36xhzz (placeholder)</p>
                    <div className="w-full max-w-2xl aspect-video">
                        <MuxPlayer
                            playbackId="x36xhzz"
                            title="Mux Test Video"
                            autoPlay={false}
                            muted={false}
                            controls={true}
                            className="w-full h-full"
                            onLoadedData={() => console.log('MuxPlayer: Video loaded')}
                            onPlay={() => console.log('MuxPlayer: Video playing')}
                            onPause={() => console.log('MuxPlayer: Video paused')}
                            onError={(error) => console.error('MuxPlayer: Error', error)}
                        />
                    </div>
                </div>

                {/* Test 5: Your Uploaded Video */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-white">Test 5: Your Uploaded Video</h2>
                    <p className="text-sm text-gray-300">Using your uploaded video playback ID: ZtAjumAUIl7C1mYRkhT6EgnhdeDckmJa3wDeDyovf3c</p>
                    <div className="w-full max-w-2xl aspect-video">
                        <MuxPlayer
                            playbackId="ZtAjumAUIl7C1mYRkhT6EgnhdeDckmJa3wDeDyovf3c"
                            title="Your Uploaded Video"
                            autoPlay={false}
                            muted={false}
                            controls={true}
                            className="w-full h-full"
                            onLoadedData={() => console.log('Your video loaded successfully!')}
                            onPlay={() => console.log('Your video is playing!')}
                            onPause={() => console.log('Your video paused')}
                            onError={(error) => console.error('Your video error:', error)}
                        />
                    </div>
                </div>

                {/* Test 4: Mux Uploader */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-white">Test 4: Mux Uploader</h2>
                    <p className="text-sm text-gray-300">Upload videos to Mux (requires API credentials)</p>
                    <div className="w-full max-w-2xl">
                        <MuxUploader
                            onUploadComplete={(result) => {
                                console.log('Upload complete:', result);
                                alert(`✅ Upload successful!\nAsset ID: ${result.assetId}\nPlayback ID: ${result.playbackId}`);
                            }}
                            onUploadError={(error) => {
                                console.error('Upload error:', error);
                                alert(`❌ Upload failed: ${error}`);
                            }}
                            onUploadProgress={(progress) => {
                                console.log('Upload progress:', progress + '%');
                            }}
                            maxSizeGB={2}
                            className="bg-gray-800 p-6 rounded-lg"
                        />
                    </div>
                </div>

                {/* Debug Info */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-white">Debug Information</h2>
                    <div className="bg-gray-800 p-4 rounded-lg text-sm text-gray-300 space-y-2">
                        <div><strong>Environment Variables:</strong></div>
                        <div>MUX_TOKEN_ID: {import.meta.env.VITE_MUX_TOKEN_ID ? '✅ Set' : '❌ Missing'}</div>
                        <div>MUX_TOKEN_SECRET: {import.meta.env.VITE_MUX_TOKEN_SECRET ? '✅ Set' : '❌ Missing'}</div>
                        <div><strong>HLS Support:</strong> {Hls.isSupported() ? '✅ HLS.js supported' : '❌ HLS.js not supported'}</div>
                        <div><strong>Native HLS:</strong> {document.createElement('video').canPlayType('application/vnd.apple.mpegurl') ? '✅ Native HLS supported' : '❌ Native HLS not supported'}</div>
                    </div>
                </div>

                {/* Instructions */}
                <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-white">Expected Results</h2>
                    <div className="bg-blue-900/20 border border-blue-500/30 p-4 rounded-lg text-sm text-blue-100 space-y-2">
                        <div><strong>Test 1 & 2:</strong> Should show Big Buck Bunny video and play smoothly</div>
                        <div><strong>Test 3:</strong> Should show the same video using our custom MuxPlayer component</div>
                        <div><strong>Test 4:</strong> Should show upload interface. Try dropping a small video file!</div>
                        <div><strong>All tests:</strong> Should work without any CORS or streaming errors</div>
                    </div>
                </div>
            </div>
        </div>
    );
}


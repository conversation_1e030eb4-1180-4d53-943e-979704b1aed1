import React from 'react';
import { cn } from '@/lib/utils';

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
    children: React.ReactNode;
    variant?: 'default' | 'bordered' | 'glowing';
    intensity?: 'low' | 'medium' | 'high';
}

const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
    (
        { children, className, variant = 'default', intensity = 'medium', ...props },
        ref
    ) => {
        const baseStyles =
            'backdrop-blur-md rounded-lg transition-all duration-300 ease-in-out';
        const bgIntensity = {
            low: 'bg-white/5 dark:bg-black/5',
            medium: 'bg-white/10 dark:bg-black/10',
            high: 'bg-white/20 dark:bg-black/20',
        };
        const borderStyles = {
            default: '',
            bordered: 'border border-white/20 dark:border-white/10',
            glowing: 'border-2 border-transparent',
        };
        const shadowStyles = {
            default: 'shadow-md',
            bordered: 'shadow-none',
            glowing: 'shadow-glow',
        };
        const hoverStyles = {
            default: 'hover:shadow-lg',
            bordered: 'hover:shadow-none',
            glowing: 'hover:shadow-glow',
        };

        return (
            <div
                ref={ref}
                className={cn(
                    baseStyles,
                    bgIntensity[intensity],
                    borderStyles[variant],
                    shadowStyles[variant],
                    hoverStyles[variant],
                    className
                )}
                {...props}
            >
                {children}
            </div>
        );
    }
);

GlassCard.displayName = 'GlassCard';

export { GlassCard, type GlassCardProps };

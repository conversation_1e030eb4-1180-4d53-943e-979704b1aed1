import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from './button';
import type { ButtonHTMLAttributes } from 'react';

type BaseButtonProps = Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'variant'>;

interface NeonButtonProps extends BaseButtonProps {
    variant?: 'magenta' | 'blue' | 'green' | 'yellow';
    className?: string;
}

export const NeonButton = React.forwardRef<HTMLButtonElement, NeonButtonProps>(
    ({ className, variant = 'blue', ...props }, ref) => {
        const variantClasses = {
            magenta: 'bg-magenta-500 hover:bg-magenta-600 text-white shadow-magenta-500/50',
            blue: 'bg-blue-500 hover:bg-blue-600 text-white shadow-blue-500/50',
            green: 'bg-green-500 hover:bg-green-600 text-white shadow-green-500/50',
            yellow: 'bg-yellow-500 hover:bg-yellow-600 text-white shadow-yellow-500/50',
        };

        return (
            <Button
                ref={ref}
                className={cn(
                    'relative transition-all duration-300 ease-out',
                    'shadow-lg hover:shadow-xl',
                    variantClasses[variant],
                    className
                )}
                {...props}
            />
        );
    }
);

NeonButton.displayName = 'NeonButton';


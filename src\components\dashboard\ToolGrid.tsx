import { Briefcase } from 'lucide-react';
import type { ToolGridProps, Toolkit } from '@/types/tools';
import { ToolkitPreview } from '@/components/toolkits/ToolkitPreview';
import { ToolIcon } from '@/components/tools/ToolIcon';

export const ToolGrid = ({
    tools,
    toolkits = [],
    isLoading,
    isLoadingTools, // Add support for both isLoading and isLoadingTools
    error,
    onDelete,
    onAdd,
    onEdit,
    onToolkitEdit,
    onToolkitDelete,
    isAdmin,
    savingToolIds = [],
    emptyMessage = "No tools found"
}: ToolGridProps) => {
    if (isLoading || isLoadingTools) {
        return (
            <div className="p-4 bg-sortmy-darker/40 border border-[#01AAE9]/20 rounded-xl">
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-6 justify-items-center">
                    {Array.from({ length: 12 }).map((_, index) => (
                        <div key={index} className="flex flex-col items-center">
                            <div className="w-16 h-16 rounded-2xl bg-[#01AAE9]/20 mb-2 animate-pulse"></div>
                            <div className="w-16 h-3 bg-[#01AAE9]/20 rounded mb-1 animate-pulse"></div>
                            <div className="w-12 h-3 bg-[#01AAE9]/10 rounded animate-pulse"></div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-sortmy-darker border border-red-500/20 rounded-lg p-6 text-center">
                <p className="text-red-400">Error loading tools. Please try again later.</p>
            </div>
        );
    }

    if (!tools.length) {
        return (
            <div className="bg-sortmy-darker border border-[#01AAE9]/20 rounded-lg p-6 text-center">
                <Briefcase className="mx-auto w-12 h-12 mb-3 opacity-30" />
                <p className="text-gray-400">{emptyMessage}</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Tools Grid - Add pb-20 for extra space at the bottom */}
            <div className="p-4 pb-20 bg-sortmy-darker/40 border border-[#01AAE9]/20 rounded-xl">
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-6 justify-items-center relative">
                    {tools.map((tool) => (
                        <div key={tool.id} className="tool-icon-container relative">
                            <ToolIcon
                                tool={{
                                    ...tool,
                                    // Prefer logo_url, fallback to logoUrl, logoLink, icon_url
                                    logoUrl: tool.logo_url || tool.logoUrl || tool.logoLink || tool.icon_url || '',
                                    createdBy: tool.user_id || '',
                                    createdAt: tool.created_at,
                                    updatedAt: tool.updated_at || tool.created_at
                                }}
                                isUserTool={'source' in tool && (tool.source === 'tools_collection' || tool.source === 'user_tooltracker')}
                                isLibraryTool={'source' in tool && tool.source === 'library'}
                                onDelete={onDelete ? () => onDelete(tool) : undefined}
                                onAdd={onAdd ? () => onAdd(tool) : undefined}
                                onEdit={onEdit ? () => onEdit(tool) : undefined}
                                isAdmin={isAdmin}
                                isSaving={savingToolIds.includes(tool.id)}
                            />
                        </div>
                    ))}
                </div>
            </div>

            {/* Toolkits Grid */}
            {toolkits && toolkits.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {toolkits.map((toolkit: Toolkit) => (
                        <ToolkitPreview
                            key={toolkit.id}
                            toolkit={toolkit}
                            tools={tools}
                            onEdit={onToolkitEdit ? () => onToolkitEdit(toolkit) : undefined}
                            onDelete={onToolkitDelete ? () => onToolkitDelete(toolkit) : undefined}
                            isOwner={toolkit.source === 'user_tooltracker' || toolkit.source === 'user'}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};


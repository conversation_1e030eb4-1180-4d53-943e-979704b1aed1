import {
  collection,
  addDoc,
  query,
  where,
  orderBy,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  onSnapshot,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Message, Conversation, MessagePreview } from '@/types/message';
import { User } from '@/types';
import { cleanFirestoreData } from '@/utils/firestoreUtils';
import { withNetworkRetry } from '@/utils/networkUtils';

// Helper to check permissions
const checkMessagePermissions = async (userId: string, conversationId: string): Promise<boolean> => {
  try {
    const conversationRef = doc(db, 'conversations', conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (!conversationDoc.exists()) {
      return false;
    }

    const data = conversationDoc.data();
    return data.participants.includes(userId) || data.isPublic === true;
  } catch (error) {
    console.error('Error checking message permissions:', error);
    return false;
  }
}

// Create a new conversation
export const createConversation = async (participants: string[], requesterId: string): Promise<string> => {
  try {
    const conversationRef = await addDoc(collection(db, 'conversations'), {
      participants,
      status: 'pending', // Start as a pending conversation
      requesterId, // Store who initiated the conversation
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    return conversationRef.id;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
};

// Get or create a conversation between two users
export const getOrCreateConversation = async (userId1: string, userId2: string): Promise<string> => {
  try {
    // Check if conversation already exists
    const q = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', userId1)
    );

    const querySnapshot = await getDocs(q);

    // Find conversation with both participants
    let conversationId: string | null = null;
    let conversationStatus: string | null = null;

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (data.participants.includes(userId2)) {
        conversationId = doc.id;
        conversationStatus = data.status;
      }
    });

    // If conversation exists and is not rejected, return its ID
    if (conversationId && conversationStatus !== 'rejected') {
      return conversationId;
    }

    // Otherwise, create a new conversation
    return await createConversation([userId1, userId2], userId1);
  } catch (error) {
    console.error('Error getting or creating conversation:', error);
    // Create a more descriptive error message
    if (error instanceof Error) {
      throw new Error(`Failed to get or create conversation: ${error.message}`);
    } else {
      throw new Error('Failed to get or create conversation due to an unknown error');
    }
  }
};

// Message interface to enforce type safety
interface MessageData {
  conversationId: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read';
  read?: boolean;  // Track if message has been read
  isAutomatedResponse?: boolean;
  buttons?: Array<{
    label: string;
    action: string;
  }>;
  attachmentUrl?: string;
  attachmentType?: 'image' | 'file' | 'audio' | 'video';
  metadata?: {
    originalName?: string;
    size?: number;
    mimeType?: string;
  };
}

// Send a message
export const sendMessage = async (
  conversationId: string,
  senderId: string,
  receiverId: string,
  content: string,
  isAutomatedResponse = false,
  buttons?: Array<{ label: string; action: string; }>,
  attachmentUrl?: string,
  attachmentType?: 'image' | 'file' | 'audio' | 'video',
  metadata?: {
    originalName?: string;
    size?: number;
    mimeType?: string;
  }
): Promise<string> => {
  return withNetworkRetry(async () => {
    // Check permissions first
    const hasPermission = await checkMessagePermissions(senderId, conversationId);
    if (!hasPermission) {
      throw new Error('Missing or insufficient permissions');
    }

    // Convert blob URLs to data URLs if needed
    let processedContent = content;
    if (content.includes('blob:')) {
      try {
        const response = await fetch(content);
        const blob = await response.blob();
        processedContent = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        });
      } catch (error) {
        console.error('Error converting blob URL:', error);
      }
    }

    // Create message data object
    const messageData: MessageData = {
      conversationId,
      senderId,
      receiverId,
      content: processedContent,
      timestamp: new Date().toISOString(),
      isAutomatedResponse,
      status: 'sent'
    };

    // Only add optional fields if they have values
    if (buttons?.length) {
      messageData.buttons = buttons;
    }
    if (attachmentUrl) {
      messageData.attachmentUrl = attachmentUrl;
      messageData.attachmentType = attachmentType;
      if (metadata) {
        messageData.metadata = metadata;
      }
    }

    // Clean the data to remove any undefined fields
    const cleanedMessageData = cleanFirestoreData(messageData);

    // Add the message to Firestore
    const messageRef = await addDoc(collection(db, 'conversations', conversationId, 'messages'), cleanedMessageData);

    // Update conversation metadata
    try {
      const conversationRef = doc(db, 'conversations', conversationId);
      await updateDoc(conversationRef, {
        lastMessageTimestamp: messageData.timestamp,
        lastMessageContent: content.substring(0, 100),
        lastMessageSenderId: senderId,
        unreadCount: increment(1)
      });
    } catch (updateError) {
      console.error('Error updating conversation metadata:', updateError);
    }

    // Try to send notification
    try {
      const userRef = doc(db, 'users', receiverId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists() && userDoc.data().fcmTokens?.length > 0) {
        await addDoc(collection(db, 'notifications'), {
          type: 'new_message',
          recipientId: receiverId,
          senderId,
          title: 'New Message',
          body: content.substring(0, 100),
          data: {
            conversationId
          },
          timestamp: messageData.timestamp
        });
      }
    } catch (error) {
      console.warn('Error sending notification:', error);
    }

    // Check for internship keyword and send automated response if needed
    // Only do this for non-automated messages to prevent infinite loops
    if (!isAutomatedResponse) {
      // Check if the message contains exactly the word "intern" (case-insensitive)
      const internRegex = /\bintern\b/i;

      // Check if the recipient is the specific user
      const isSpecificUser = receiverId === "x8PgDaTjq3Xb4WmAJzOQu0J9Htl2";

      // Log the check to help with debugging
      console.log('Checking for internship keyword:', {
        hasInternKeyword: internRegex.test(content),
        isSpecificUser,
        receiverId
      });

      if (internRegex.test(content) && isSpecificUser) {
        // Use setTimeout to ensure this runs after the current execution context
        // and after the UI has had time to update with the original message
        console.log('Will send automated internship response after delay...');

        // We'll use a longer delay to ensure the UI is stable
        setTimeout(async () => {
          try {
            console.log('Now sending automated internship response...');
            await checkAndSendInternshipResponse(conversationId, senderId, receiverId, content);
          } catch (autoResponseError) {
            console.error('Error in delayed automated response:', autoResponseError);
          }
        }, 2000); // Longer delay to ensure UI stability
      }
    }

    return messageRef.id;
  }, 'send message');
};

// Get messages for a conversation
export const getMessages = async (conversationId: string): Promise<Message[]> => {
  try {
    const q = query(
      collection(db, 'conversations', conversationId, 'messages'),
      orderBy('timestamp', 'asc')
    );

    const querySnapshot = await getDocs(q);

    const messages: Message[] = [];
    querySnapshot.forEach((doc) => {
      messages.push({
        id: doc.id,
        ...doc.data()
      } as Message);
    });

    return messages;
  } catch (error) {
    console.error('Error getting messages:', error);
    throw error;
  }
};

// Subscribe to messages for a conversation
export const subscribeToMessages = (
  conversationId: string,
  callback: (messages: Message[]) => void
) => {
  const q = query(
    collection(db, 'conversations', conversationId, 'messages'),
    orderBy('timestamp', 'asc')
  );

  return onSnapshot(q, (querySnapshot) => {
    const messages: Message[] = [];
    querySnapshot.forEach((doc) => {
      messages.push({
        id: doc.id,
        ...doc.data()
      } as Message);
    });

    callback(messages);
  });
};

// Get conversations for a user
export const getConversations = async (userId: string): Promise<Conversation[]> => {
  try {
    const q = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', userId),
      orderBy('updatedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);

    const conversations: Conversation[] = [];
    querySnapshot.forEach((doc) => {
      conversations.push({
        id: doc.id,
        ...doc.data()
      } as Conversation);
    });

    return conversations;
  } catch (error) {
    console.error('Error getting conversations:', error);
    throw error;
  }
};

// Subscribe to conversations for a user
export const subscribeToConversations = (
  userId: string,
  callback: (conversations: Conversation[]) => void
) => {
  const q = query(
    collection(db, 'conversations'),
    where('participants', 'array-contains', userId),
    orderBy('updatedAt', 'desc')
  );

  return onSnapshot(q, (querySnapshot) => {
    const conversations: Conversation[] = [];
    querySnapshot.forEach((doc) => {
      conversations.push({
        id: doc.id,
        ...doc.data()
      } as Conversation);
    });

    callback(conversations);
  });
};

// Mark message as read
export const markMessageAsRead = async (conversationId: string, messageId: string): Promise<void> => {
  try {
    await updateDoc(
      doc(db, 'conversations', conversationId, 'messages', messageId),
      { read: true }
    );
  } catch (error) {
    console.error('Error marking message as read:', error);
    throw error;
  }
};

// Get conversation previews with user details
export const getMessagePreviews = async (userId: string): Promise<MessagePreview[]> => {
  try {
    // Get all conversations for the user
    const conversations = await getConversations(userId);

    // Get user details for each conversation
    const previews: MessagePreview[] = await Promise.all(
      conversations.map(async (conversation) => {
        // Get the other participant's ID
        const participantId = conversation.participants.find(id => id !== userId) || '';

        // Get user details
        const userDoc = await getDoc(doc(db, 'users', participantId));
        const userData = userDoc.data() as User;

        // Count unread messages
        const messagesQuery = query(
          collection(db, 'conversations', conversation.id, 'messages'),
          where('receiverId', '==', userId),
          where('read', '==', false)
        );
        const unreadSnapshot = await getDocs(messagesQuery);

        // Get last message content
        let lastMessageContent = '';
        if (conversation.lastMessage?.content) {
          lastMessageContent = conversation.lastMessage.content;
        } else {
          // If no last message, check if it's a pending request
          if (conversation.status === 'pending') {
            lastMessageContent = conversation.requesterId === userId
              ? 'You sent a message request'
              : 'Sent you a message request';
          } else {
            lastMessageContent = 'No messages yet';
          }
        }

        return {
          conversationId: conversation.id,
          participantId,
          participantName: userData?.username || 'User',
          participantAvatar: userData?.avatar_url,
          lastMessage: lastMessageContent,
          timestamp: conversation.lastMessage?.timestamp || conversation.updatedAt,
          unreadCount: unreadSnapshot.size,
          status: conversation.status || 'accepted', // Default to accepted for backward compatibility
          isRequester: conversation.requesterId === userId
        };
      })
    );

    // Sort by timestamp (newest first)
    return previews.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  } catch (error) {
    console.error('Error getting message previews:', error);
    throw error;
  }
};

// Accept a conversation request
export const acceptConversationRequest = async (conversationId: string): Promise<void> => {
  try {
    const conversationRef = doc(db, 'conversations', conversationId);
    await updateDoc(conversationRef, {
      status: 'accepted',
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error accepting conversation request:', error);
    throw error;
  }
};

// Reject a conversation request
export const rejectConversationRequest = async (conversationId: string): Promise<void> => {
  try {
    const conversationRef = doc(db, 'conversations', conversationId);
    await updateDoc(conversationRef, {
      status: 'rejected',
      updatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error rejecting conversation request:', error);
    throw error;
  }
};

// Send automated internship response
export const checkAndSendInternshipResponse = async (
  conversationId: string,
  senderId: string,
  receiverId: string,
  _content: string // Unused but kept for consistent interface
): Promise<void> => {
  try {
    // Add a small delay to ensure the original message is processed first
    await new Promise(resolve => setTimeout(resolve, 500));

    const timestamp = new Date().toISOString();

    // Create the automated response with internship information
    const responseContent = `[AUTOMATED INTERNSHIP RESPONSE]

Thank you for your interest in our internship program!

We offer a comprehensive 3-month internship program for talented individuals looking to gain hands-on experience in the tech industry. Our program includes:

• Mentorship from experienced professionals
• Real-world project experience
• Networking opportunities
• Possibility of full-time employment after completion

Would you like to learn more about our internship opportunities?`;

    // Define the buttons for user interaction
    const buttons = [
      {
        label: "I'm interested",
        action: "internship_accept"
      },
      {
        label: "Not interested",
        action: "internship_decline"
      }
    ];

    // Important: We're sending the message as the current user (senderId)
    // but marking it as an automated response so it's displayed differently
    const messageData = {
      conversationId,
      senderId, // Current user is sending the message
      receiverId, // To the target user
      content: responseContent,
      timestamp,
      read: false,
      isAutomatedResponse: true, // Mark as automated
      isInternshipResponse: true, // Special flag for this type of response
      buttons,
      status: 'sent'
    };

    // Clean the data to remove any undefined fields
    const cleanedMessageData = cleanFirestoreData(messageData);

    console.log('Sending internship response with data:', cleanedMessageData);

    // Add the message to Firestore - using addDoc
    const messageRef = await addDoc(
      collection(db, 'conversations', conversationId, 'messages'),
      cleanedMessageData
    );

    // Update conversation metadata
    await updateDoc(doc(db, 'conversations', conversationId), {
      lastMessage: {
        content: "Internship Program Information",
        timestamp,
        senderId
      },
      updatedAt: timestamp
    });

    console.log('Automated internship response sent successfully with ID:', messageRef.id);
  } catch (error) {
    console.error('Error sending automated internship response:', error);
    // Don't throw the error to prevent disrupting the normal message flow
  }
};

// Handle automated message button actions
export const handleMessageAction = async (
  conversationId: string,
  senderId: string,
  receiverId: string,
  action: string
): Promise<string> => {
  try {
    const timestamp = new Date().toISOString();

    let responseContent = '';
    let isInternshipResponse = false;

    // Handle different action types
    if (action === 'proceed') {
      responseContent = 'You have chosen to proceed with the intern program. Our team will contact you shortly.';
    } else if (action === 'not_proceed') {
      responseContent = 'Thank you for your interest. Feel free to reach out if you change your mind.';
    } else if (action === 'internship_accept') {
      isInternshipResponse = true;
      responseContent = `[AUTOMATED INTERNSHIP RESPONSE]

Great! We're excited about your interest in our internship program.

Our team will reach out to you within 2 business days to schedule an initial interview. In the meantime, please prepare:

• Your updated resume
• Portfolio of relevant work (if applicable)
• Your availability for the next 2 weeks

If you have any questions before then, feel free to ask!`;
    } else if (action === 'internship_decline') {
      isInternshipResponse = true;
      responseContent = `[AUTOMATED INTERNSHIP RESPONSE]

Thank you for considering our internship program. We understand that timing and circumstances vary for everyone.

If you change your mind or would like to explore other opportunities with us in the future, please don't hesitate to reach out.

Best of luck with your career endeavors!`;
    }

    // Create message data - important: we're sending as the current user
    const messageData = {
      conversationId,
      senderId, // Current user sending the response
      receiverId, // To the target user
      content: responseContent,
      timestamp,
      read: false,
      isAutomatedResponse: true,
      isInternshipResponse,
      status: 'sent'
    };

    // Clean the data
    const cleanedMessageData = cleanFirestoreData(messageData);

    console.log('Sending action response with data:', cleanedMessageData);

    // Add the message to Firestore
    const messageRef = await addDoc(
      collection(db, 'conversations', conversationId, 'messages'),
      cleanedMessageData
    );

    // Update conversation metadata
    await updateDoc(doc(db, 'conversations', conversationId), {
      lastMessage: {
        content: isInternshipResponse ? "Internship Program Response" : responseContent.substring(0, 100),
        timestamp,
        senderId
      },
      updatedAt: timestamp
    });

    console.log('Automated action response sent successfully with ID:', messageRef.id);

    return messageRef.id;
  } catch (error) {
    console.error('Error handling message action:', error);
    if (error instanceof Error) {
      throw new Error(`Failed to handle message action: ${error.message}`);
    } else {
      throw new Error('Failed to handle message action due to an unknown error');
    }
  }
};
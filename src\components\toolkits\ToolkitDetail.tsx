import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Tool } from '@/types/tools';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
    Loader2,
    Share,
    // Upload, // Removed unused import
    // User as UserIcon, // Removed unused import
    ArrowLeft,
    Calendar,
    Tag,
    Zap,
    ExternalLink,
    // Star, // Removed unused import
    Eye,
    Heart,
    Sparkles,
    Layers,
    // Settings, // Removed unused import
    // Globe, // Removed unused import
    // Pin, // Removed unused import
    Edit
} from 'lucide-react';
import { useToolkit } from '@/hooks/useToolkit';
import { useToast } from '@/hooks/use-toast';
import { ToolkitShareModal } from './ToolkitShareModal';
import { ToolkitStatusBar } from './ToolkitStatusBar';
import { ToolkitProjects } from './ToolkitProjects';
import {
    getLikesAndViews,
    toggleLike,
    incrementViews,
    subscribeLikesAndViews,
    checkUserLiked,
    type LikeData
} from '@/services/likesService';
import NeuCard from '@/components/ui/NeuCard';
import GlassCard from '@/components/ui/GlassCard';
import NeonButton from '@/components/ui/NeonButton';

export default function ToolkitDetail() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { user: currentUser } = useAuth();
    const { toast } = useToast();
    const { toolkit, tools, creator, loading, error, fetchToolkit } = useToolkit();
    const [showShareModal, setShowShareModal] = useState(false);
    const [likes, setLikes] = useState(0);
    const [views, setViews] = useState(0);
    const [isLiked, setIsLiked] = useState(false);
    const [likingInProgress, setLikingInProgress] = useState(false);
    // Removed unused loadingLikes state

    useEffect(() => {
        if (id) {
            fetchToolkit(id);
            loadLikesAndViews();
        }
    }, [id, fetchToolkit]);

    // Load likes and views data
    const loadLikesAndViews = async () => {
        if (!id) return;

        // Loading likes and views
        try {
            // Get initial data
            const data = await getLikesAndViews(id, 'toolkit');
            setLikes(data.totalLikes);
            setViews(data.totalViews);

            // Check if current user has liked
            if (currentUser) {
                const userLiked = await checkUserLiked(id, currentUser.uid, 'toolkit');
                setIsLiked(userLiked);
            }

            // Increment view count
            const newViewCount = await incrementViews(id, 'toolkit');
            setViews(newViewCount);

            // Subscribe to real-time updates
            const unsubscribe = subscribeLikesAndViews(id, 'toolkit', (data: LikeData) => {
                setLikes(data.totalLikes);
                setViews(data.totalViews);
                if (currentUser) {
                    setIsLiked(data.likedBy.includes(currentUser.uid));
                }
            });

            return unsubscribe;
        } catch (error) {
            console.error('Error loading likes and views:', error);
        } finally {
            // Finished loading likes and views
        }
    };

    const handleShare = (e?: React.MouseEvent) => {
        e?.preventDefault();
        e?.stopPropagation();
        if (!toolkit || !currentUser) return;
        setShowShareModal(true);
    };

    const handleManageToolkit = (e?: React.MouseEvent) => {
        e?.preventDefault();
        e?.stopPropagation();
        if (!toolkit || !currentUser || !isOwner) return;
        navigate(`/dashboard/toolkits/edit/${toolkit.id}`);
    };

    const handleLike = async () => {
        if (!toolkit || !currentUser || likingInProgress) return;

        setLikingInProgress(true);
        try {
            const result = await toggleLike(toolkit.id, currentUser.uid, 'toolkit');
            setIsLiked(result.isLiked);
            setLikes(result.totalLikes);

            toast({
                title: result.isLiked ? "Added to favorites" : "Removed from favorites",
                description: result.isLiked
                    ? "Toolkit added to your favorites"
                    : "Toolkit removed from your favorites",
            });
        } catch (error) {
            console.error('Error updating like:', error);
            toast({
                title: "Error",
                description: "Failed to update favorite status",
                variant: "destructive"
            });
        } finally {
            setLikingInProgress(false);
        }
    };

    const handleOpenAllTools = () => {
        if (!tools || tools.length === 0) {
            toast({
                title: "No tools to open",
                description: "This toolkit doesn't have any tools yet.",
                variant: "destructive"
            });
            return;
        }

        // Count tools with valid URLs
        let openedCount = 0;

        // Open all tool websites in new tabs
        tools.forEach((tool: Tool) => {
            // Try multiple possible URL fields
            const websiteUrl = tool.website || tool.websiteLink || tool.link;
            if (websiteUrl) {
                // Ensure URL has protocol
                const url = websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}`;
                window.open(url, '_blank', 'noopener,noreferrer');
                openedCount++;
            }
        });

        if (openedCount > 0) {
            toast({
                title: "Opening tools",
                description: `Opening ${openedCount} tool${openedCount !== 1 ? 's' : ''} in new tabs`,
            });
        } else {
            toast({
                title: "No tool websites found",
                description: "None of the tools in this toolkit have website URLs.",
                variant: "destructive"
            });
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center p-8">
                <Loader2 className="w-8 h-8 animate-spin" />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center p-8">
                <p>Error: {error.message}</p>
            </div>
        );
    }

    if (!toolkit) {
        return (
            <div className="text-center p-8">
                <p>Toolkit not found</p>
            </div>
        );
    }

    const isOwner = currentUser?.uid === toolkit.created_by;

    const formatDate = (dateString: string) => {
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch {
            return 'Unknown';
        }
    };

    return (
        <div className="flex h-screen text-white overflow-hidden">
            <div className="flex-1 p-4 overflow-y-auto">
                {/* Header */}
                <div className="bg-gray-900/50 backdrop-blur-sm border-b border-sortmy-blue/20 sticky top-0 z-10">
                    <div className="container mx-auto px-6 py-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <NeonButton
                                    variant="outline"
                                    onClick={() => navigate(-1)}
                                    className="text-gray-300 hover:text-white"
                                >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back
                                </NeonButton>
                                <div className="flex items-center gap-2">
                                    <Layers className="w-5 h-5 text-blue-400" />
                                </div>
                            </div>

                            {/* Pinned Action Button */}
                            <div className="relative group">
                                <NeonButton
                                    variant="gradient"
                                    onClick={handleOpenAllTools}
                                    className="shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
                                >
                                    <Zap className="w-4 h-4 mr-2" />
                                    Quick Launch
                                </NeonButton>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="space-y-8 max-w-7xl mx-auto">
                    {/* Hero Section */}
                    <GlassCard
                        variant="bordered"
                        intensity="low"
                        className="border-sortmy-blue/20"
                    >
                        <div className="relative">
                            {/* Subtle gradient background */}
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-xl blur-xl opacity-50"></div>

                            <Card className="relative bg-sortmy-darker border-sortmy-blue/20 overflow-hidden backdrop-blur-sm">
                                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-3xl pointer-events-none"></div>

                                <CardContent className="p-8">
                                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
                                        <div className="flex-1">
                                            {/* Toolkit Icon & Title */}
                                            <div className="flex items-center gap-4 mb-4">
                                                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                                                    <Sparkles className="w-8 h-8 text-white" />
                                                </div>
                                                <div>
                                                    <h1 className="text-4xl font-bold text-white mb-2">{toolkit.name}</h1>
                                                    <div className="flex items-center gap-4 text-sm text-gray-400">
                                                        <div className="flex items-center gap-1">
                                                            <Calendar className="w-4 h-4" />
                                                            <span>Created {formatDate(toolkit.created_at)}</span>
                                                        </div>
                                                        <div className="flex items-center gap-1">
                                                            <Zap className="w-4 h-4" />
                                                            <span>{tools.length} tools</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Description */}
                                            <p className="text-xl text-gray-300 mb-6 leading-relaxed">
                                                {toolkit.description}
                                            </p>

                                            {/* Creator Info */}
                                            {creator && (
                                                <div className="flex items-center gap-3 mb-6">
                                                    <Avatar className="w-10 h-10 border-2 border-gray-700">
                                                        <AvatarImage src={creator.avatar_url} />
                                                        <AvatarFallback className="bg-gray-800 text-gray-200">
                                                            {creator.username?.charAt(0)?.toUpperCase() || 'U'}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div>
                                                        <p className="text-gray-200 font-medium">Created by {creator.username}</p>
                                                        <p className="text-gray-400 text-sm">Toolkit Creator</p>
                                                    </div>
                                                </div>
                                            )}

                                            {/* Tags */}
                                            {toolkit.tags && toolkit.tags.length > 0 && (
                                                <div className="flex flex-wrap gap-2 mb-6">
                                                    {toolkit.tags.map((tag: string) => (
                                                        <Badge
                                                            key={`${toolkit.id}-${tag}`}
                                                            className="bg-gray-800 text-gray-200 hover:bg-gray-700 border-gray-700"
                                                        >
                                                            <Tag className="w-3 h-3 mr-1" />
                                                            {tag}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            )}
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="flex flex-col gap-3 lg:min-w-[200px] relative z-10">
                                            <NeonButton
                                                variant="gradient"
                                                onClick={handleShare}
                                                className="shadow-lg hover:shadow-xl"
                                            >
                                                <Share className="w-4 h-4 mr-2" />
                                                Share Toolkit
                                            </NeonButton>

                                            {isOwner && (
                                                <NeonButton
                                                    variant="gradient"
                                                    onClick={handleManageToolkit}
                                                    className="shadow-lg hover:shadow-xl"
                                                >
                                                    <Edit className="w-4 h-4 mr-2" />
                                                    Manage Toolkit
                                                </NeonButton>
                                            )}

                                            <div className="flex gap-2">
                                                <NeonButton
                                                    variant={isLiked ? 'magenta' : 'outline'}
                                                    onClick={handleLike}
                                                    disabled={likingInProgress}
                                                    className="flex-1"
                                                >
                                                    <Heart className={`w-4 h-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                                                    {likes}
                                                </NeonButton>
                                                <NeonButton variant="outline" className="flex-1">
                                                    <Eye className="w-4 h-4 mr-1" />
                                                    {views}
                                                </NeonButton>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </GlassCard>

                    {/* Status Bar */}
                    <ToolkitStatusBar
                        toolkit={toolkit}
                        onUpdate={() => fetchToolkit(id!)}
                    />

                    {/* Tools Section */}
                    <NeuCard
                        variant="elevated"
                        color="dark"
                        className="border border-sortmy-blue/20"
                    >
                        <CardHeader className="pb-4">
                            <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                    <Zap className="w-5 h-5 text-white" />
                                </div>
                                Tools in this Toolkit
                                <Badge className="bg-green-600/20 text-green-400 border-green-600/30">
                                    {tools.length} tools
                                </Badge>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6">
                            {tools.length > 0 ? (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {tools.map((tool: Tool) => (
                                        <Card
                                            key={tool.id}
                                            className="bg-gray-800/50 border-gray-700 hover:bg-gray-800/80 transition-all duration-300 hover:scale-105 cursor-pointer group"
                                            onClick={() => navigate(`/dashboard/tools/${tool.id}`)}
                                        >
                                            <CardContent className="p-4">
                                                <div className="flex items-start gap-3">
                                                    {tool.logo_url ? (
                                                        <img
                                                            src={tool.logo_url}
                                                            alt={tool.name}
                                                            className="w-12 h-12 rounded-xl object-cover flex-shrink-0 border border-gray-600"
                                                        />
                                                    ) : (
                                                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                                                            <span className="text-white font-bold text-lg">
                                                                {tool.name.charAt(0).toUpperCase()}
                                                            </span>
                                                        </div>
                                                    )}
                                                    <div className="flex-1 min-w-0">
                                                        <h3 className="font-semibold text-white group-hover:text-blue-400 transition-colors truncate">
                                                            {tool.name}
                                                        </h3>
                                                        <p className="text-sm text-gray-400 line-clamp-2 mt-1">
                                                            {tool.description}
                                                        </p>
                                                        {tool.category && (
                                                            <Badge className="mt-2 bg-gray-700 text-gray-300 text-xs">
                                                                {tool.category}
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    <ExternalLink className="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors flex-shrink-0" />
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <div className="w-16 h-16 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <Zap className="w-8 h-8 text-gray-500" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-300 mb-2">No tools yet</h3>
                                    <p className="text-gray-500">This toolkit doesn't have any tools added yet.</p>
                                </div>
                            )}
                        </CardContent>
                    </NeuCard>

                    {/* Projects Using This Toolkit */}
                    <ToolkitProjects toolkitId={toolkit.id} />

                    {/* Share Modal */}
                    {toolkit && (
                        <ToolkitShareModal
                            isOpen={showShareModal}
                            onClose={() => setShowShareModal(false)}
                            toolkit={toolkit}
                        />
                    )}
                </div>
            </div>
        </div>
    );
}
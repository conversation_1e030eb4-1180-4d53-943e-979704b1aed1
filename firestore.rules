rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }    // Helper function to check if user owns the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }    // Helper function to check if user is an admin
    function isAdmin() {
      let user = get(/databases/$(database)/documents/users/$(request.auth.uid));
      return isAuthenticated() &&
        user != null &&
        user.data.role != null &&
        user.data.role == 'admin';
    }

    // Users collection rules
    match /users/{userId} {
      allow read: if true;  // Allow public read access to user profiles

      // Allow updates to following, following_count, followers_count, and XP-related fields by any authenticated user
      allow update: if isAuthenticated() && (
        isOwner(userId) ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['following', 'following_count', 'followers_count']) ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['xp', 'level', 'streak_days', 'last_login', 'last_activity'])
      );

      // Allow other writes only by the owner
      allow create, delete: if isOwner(userId);
      allow list: if true;

      // Nested collections under users
      match /{allChildren=**} {
        allow read: if isAuthenticated();
        allow write: if isOwner(userId);
      }
    }

    // System collections
    match /_connection_test/{docId} {
      allow read, write: if true;  // Allow all operations for connection testing
    }

    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        resource.data.isPublic == true
      );
      // Allow creation by any authenticated user for any userId (for toolkit sharing, etc)
      allow create: if isAuthenticated() && (
        request.resource.data.userId is string &&
        request.resource.data.userId != null &&
        request.resource.data.userId.size() > 0
      );
      // Allow update by recipient for status/readAt, or by system
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'readAt'])
      );
      // Allow delete by recipient
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // Messages collection rules
    match /messages/{messageId} {
      function isParticipant(conversationId) {
        let conversation = get(/databases/$(database)/documents/conversations/$(conversationId));
        return isAuthenticated() &&
          conversation != null &&
          (request.auth.uid in conversation.data.participants ||
           conversation.data.isPublic == true ||
           request.auth.uid == conversation.data.ownerId);
      }      function isValidMessage() {
        let msgData = request.resource.data;
        return msgData.size() > 0 &&
               msgData.conversationId is string &&
               msgData.senderId is string &&
               msgData.senderId == request.auth.uid &&
               msgData.receiverId is string &&
               msgData.content is string &&
               msgData.timestamp is string &&
               msgData.status in ['sent', 'delivered', 'read'] &&
               (!('attachmentType' in msgData) || msgData.attachmentType in ['image', 'file', 'audio', 'video']) &&
               (!('isAutomatedResponse' in msgData) || msgData.isAutomatedResponse is bool) &&
               (!('buttons' in msgData) || (msgData.buttons is list && msgData.buttons.size() <= 10)) &&
               (!('metadata' in msgData) || msgData.metadata is map);
      }

      allow read: if isAuthenticated() &&
        isParticipant(resource.data.conversationId);
      allow create: if isAuthenticated() &&
        isParticipant(request.resource.data.conversationId) &&
        isValidMessage();
      allow update: if isAuthenticated() &&
        isParticipant(resource.data.conversationId) &&
        (request.auth.uid == resource.data.senderId ||
         request.auth.uid == resource.data.receiverId);
      allow delete: if isAuthenticated() &&
        request.auth.uid == resource.data.senderId;
    }

    // Conversations collection rules
    match /conversations/{conversationId} {
      function isConversationParticipant() {
        return isAuthenticated() &&
          (request.auth.uid in resource.data.participants ||
           resource.data.isPublic == true);
      }

      allow read: if isAuthenticated() && isConversationParticipant();
      allow create: if isAuthenticated() &&
        request.auth.uid in request.resource.data.participants;
      allow update: if isAuthenticated() && isConversationParticipant();
      allow delete: if false; // Never allow conversation deletion

      // Messages subcollection
      match /messages/{messageId} {
        allow read: if isAuthenticated() &&
          request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
        allow create: if isAuthenticated() &&
          request.auth.uid == request.resource.data.senderId &&
          request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
        allow update: if isAuthenticated() &&
          request.auth.uid == resource.data.receiverId &&
          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
      }

      // Nested collections under users (if any)
      match /{allChildren=**} {
        allow read: if true;
        allow write: if isOwner(userId);
      }
    }

    // Portfolio items collection
    match /portfolio/{itemId} {
      allow read: if true;
      allow create: if isAuthenticated();
      // Allow any authenticated user to update interaction-related fields
      allow update: if isAuthenticated() && (
        resource.data.user_id == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['likes_count', 'views_count', 'comments_count', 'likes', 'views', 'comments'])
      );
      allow delete: if isAuthenticated() && resource.data.user_id == request.auth.uid;

      match /interactions/{type} {
        allow read: if true;
        allow write: if isAuthenticated();
      }

      match /likes/{likeId} {
        allow read: if true;
        allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
        allow delete: if isAuthenticated();
      }
    }

    // Tools collection
    match /tools/{toolId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.resource.data.user_id == request.auth.uid;
      allow update, delete: if isAuthenticated() && (
        resource.data.user_id == request.auth.uid ||
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'
      );
    }

    // AI Tools collection
    match /ai-tools/{toolId} {
      allow read: if true;
      allow write: if isAuthenticated() &&
        (request.auth.token.admin == true ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }

    // For backward compatibility
    match /aiTools/{toolId} {
      allow read: if true;
      allow write: if isAuthenticated() &&
        (request.auth.token.admin == true ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }

    // Likes collection (global)
    match /likes/{likeId} {
      allow read: if true;
      allow create: if isAuthenticated()
        && request.resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated();
    }

    // Video likes collection (if you use a separate collection)
    match /videoLikes/{likeId} {
      allow read: if true;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated();
    }

    // Likes as a subcollection under videos
    match /videos/{videoId}/likes/{likeId} {
      allow read: if true;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated();
    }

    // Comments collection
    match /comments/{commentId} {
      allow read: if true;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() &&
        get(/databases/$(database)/documents/comments/$(commentId)).data.userId == request.auth.uid &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['content', 'updatedAt']);
      allow delete: if isAuthenticated() &&
        get(/databases/$(database)/documents/comments/$(commentId)).data.userId == request.auth.uid;
    }

    // Analytics views collection
    match /analytics_views/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
    }

    // Analytics interactions collection
    match /analytics_interactions/{interactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
    }

    // Analytics summary for portfolio items
    match /analytics_summary_portfolio/{itemId} {
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/portfolio/$(itemId)) &&
        get(/databases/$(database)/documents/portfolio/$(itemId)).data.user_id == request.auth.uid;
      allow write: if isAuthenticated();
    }

    // Analytics summary for profiles
    match /analytics_summary_profile/{userId} {
      allow read: if isAuthenticated() && request.auth.uid == userId;
      allow write: if isAuthenticated();
    }    // System collections for testing and notifications
    match /_connection_test/{docId} {
      allow read, write: if true;
    }

    // Toolkits collection (merged rules)
    match /toolkits/{toolkitId} {
      // Allow create if the user is the creator
      allow create: if request.auth != null && request.resource.data.created_by == request.auth.uid;
      // Allow update if:
      // 1. The user created the toolkit
      // 2. The user is being added to shared_with and not changing other fields
      allow update: if request.auth != null && (
        resource.data.created_by == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['shared_with', 'updated_at'])
      );
      // Allow delete if the user is the creator
      allow delete: if request.auth != null && resource.data.created_by == request.auth.uid;
      // Allow read if:
      // - Public library toolkit
      // - User is creator
      // - User is in shared_with
      allow get, list, read: if (
        (resource.data.is_published == true && resource.data.source == 'library') ||
        (request.auth != null && (
          resource.data.created_by == request.auth.uid ||
          (resource.data.shared_with != null && resource.data.shared_with.hasAny([request.auth.uid]))
        ))
      );
    }

    // Portfolio collection rules with toolkit support
    match /portfolio/{portfolioId} {
      allow read: if true;  // Public read access
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid
      );
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['likes', 'views'])
      );
      allow delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid
      );
    }

    // Portfolio drafts collection rules
    match /portfolio_drafts/{draftId} {
      allow read, write, delete: if isAuthenticated() && (
        (resource == null && request.resource.data.userId == request.auth.uid) ||
        (resource != null && resource.data.userId == request.auth.uid)
      );
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid &&
        request.resource.data.keys().hasAll(['userId', 'title', 'description', 'content_type', 'tools_used', 'is_public', 'created_at', 'updated_at', 'auto_save_count']) &&
        request.resource.data.userId is string &&
        request.resource.data.title is string &&
        request.resource.data.description is string &&
        request.resource.data.content_type in ['post', 'reel', 'both'] &&
        request.resource.data.tools_used is list &&
        request.resource.data.is_public is bool &&
        request.resource.data.auto_save_count is number
      );
    }

    // Portfolio archives collection rules
    match /portfolio_archives/{archiveId} {
      allow read, write, delete: if isAuthenticated() && (
        (resource == null && request.resource.data.userId == request.auth.uid) ||
        (resource != null && resource.data.userId == request.auth.uid)
      );
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid &&
        request.resource.data.keys().hasAll(['userId', 'title', 'description', 'archived_at'])
      );
    }

    // Storage cleanup queue rules (admin only)
    match /storage_cleanup_queue/{queueId} {
      allow read, write, delete: if isAuthenticated() && (
        request.auth.token.admin == true
      );
      allow create: if isAuthenticated() && (
        request.auth.token.admin == true &&
        request.resource.data.keys().hasAll(['filePath', 'fileUrl', 'userId', 'reason', 'scheduledFor', 'createdAt', 'attempts'])
      );
    }    // Academy rules
    match /courses/{courseId} {
      allow read: if true;
      // Allow write/update/delete if admin or course creator
      allow write, update, delete: if isAdmin() || (isAuthenticated() && (
        (request.resource.data.createdBy == request.auth.uid) ||
        (resource.data.createdBy == request.auth.uid)
      ));

      // Subcollection for modules within a course
      match /modules/{moduleId} {
        allow read: if true;
        allow write, update, delete: if isAdmin() || (isAuthenticated() && (
          (request.resource.data.createdBy == request.auth.uid) ||
          (resource.data.createdBy == request.auth.uid)
        ));

        // Subcollection for lessons within a module
        match /lessons/{lessonId} {
          allow read: if true;
          allow write, update, delete: if isAdmin() || (isAuthenticated() && (
            (request.resource.data.createdBy == request.auth.uid) ||
            (resource.data.createdBy == request.auth.uid)
          ));
        }
      }
    }

    // Rules for user progress in academy
    // Allow authenticated users to read/write their own progress docs in both user_progress and userProgress collections
    match /user_progress/{progressId} {
      allow read, write: if isAuthenticated() && progressId.startsWith(request.auth.uid + '_');
    }
    match /userProgress/{progressId} {
      // Allow create if userId matches auth and doc ID is correct
      allow create: if isAuthenticated()
        && request.resource.data.userId == request.auth.uid
        && progressId == (request.auth.uid + '_' + request.resource.data.courseId);
      // Allow read, update, delete if userId matches auth and doc ID is correct
      allow read, update, delete: if isAuthenticated()
        && resource.data.userId == request.auth.uid
        && progressId == (request.auth.uid + '_' + resource.data.courseId);
    }

    // Toolkit Likes collection
    match /toolkitLikes/{toolkitId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated(); // Allow liking
      allow update: if isAuthenticated(); // Allow updating like doc (e.g. increment views)
      allow delete: if isAuthenticated(); // Allow unliking
    }

    // Tool Likes collection
    match /toolLikes/{toolId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }

    // XP Activities collection - for logging XP earning activities
    match /xp_activities/{activityId} {
      // Allow users to read their own XP activities
      allow read, get: if isAuthenticated() && resource.data.userId == request.auth.uid;

      // Allow users to list their own XP activities
      allow list: if isAuthenticated()
        && request.auth.uid != null
        && (
          !('where' in request.query) ||
          request.query.where.size() == 0 ||
          (request.query.where.size() > 0 &&
           request.query.where[0][0] == 'userId' &&
           request.query.where[0][2] == request.auth.uid)
        );

      // Allow system to create XP activity logs with proper validation
      allow create: if isAuthenticated()
        && request.resource.data.userId == request.auth.uid
        && request.resource.data.keys().hasAll(['userId', 'activityType', 'xpEarned', 'description', 'timestamp'])
        && request.resource.data.userId is string
        && request.resource.data.activityType is string
        && request.resource.data.xpEarned is number
        && request.resource.data.description is string;

      // No updates or deletes allowed for audit trail
      allow update, delete: if false;
    }

    // User Mission Progress collection - for tracking mission completion
    match /user_mission_progress/{progressId} {
      // TEMPORARY DEBUG: Allow all authenticated users full access
      allow read, write: if request.auth != null;
    }

    // Test collection to verify rules deployment
    match /test_permissions/{docId} {
      allow read, write: if true;
    }

    // User Tools collection - for personal tool library
    match /userTools/{userToolId} {
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() && resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    // TEMP: Debugging rule, DISABLE IN PROD
    // match /{document=**} {
    //   allow read: if true;
    // }
  }
}

import { <PERSON><PERSON><PERSON>, Palette, User, VideoIcon, Users } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

const PortfolioFeatureSection = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleCreatePortfolio = () => {
    if (user) {
      // If user is logged in, navigate to their profile
      navigate(`/dashboard/portfolio`);
    } else {
      // If user is not logged in, navigate to signup page
      navigate('/signup');
    }
  };

  return (
    <section className="py-16 md:py-24 px-4 bg-sortmy-darker">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Turn Your <span className="text-sortmy-blue">AI Work</span> Into a Powerful Digital <span className="text-sortmy-blue">Portfolio</span></h2>
          <p className="text-xl text-white max-w-2xl mx-auto flex items-center justify-center gap-2">
            <span>Get discovered.</span>
            <span>Get hired.</span>
            <span>Get paid.</span>
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          <CreatorProfileCard
            username="aivisioneer"
            fullName="Emma Chen"
            tags={["AI Art", "Voice Clone", "Brand Reels"]}
            imageUrl="https://images.unsplash.com/photo-1740153204804-200310378f2f?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          />

          <CreatorProfileCard
            username="futurecoder"
            fullName="James Wilson"
            tags={["ChatGPT Pro", "Code Generation", "Automation"]}
            imageUrl="https://plus.unsplash.com/premium_photo-1689562473471-6e736b8afe15?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          />

          <CreatorProfileCard
            username="designfuturist"
            fullName="Alex Rivera"
            tags={["Midjourney", "3D Models", "UX Prototypes"]}
            imageUrl="https://images.unsplash.com/photo-1641893049587-a7f0f399ecec?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
          />
        </div>

        <div className="text-center mt-12">
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              className="bg-sortmy-blue hover:bg-sortmy-blue/90 text-white"
              onClick={handleCreatePortfolio}
            >
              Create Your AI Portfolio
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Link to="/explore">
              <Button variant="outline" className="border-sortmy-blue text-white hover:bg-sortmy-blue transition-colors">
                <Users className="mr-2 h-5 w-5" />
                Explore AI Creators
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

interface CreatorProfileCardProps {
  username: string;
  fullName: string;
  tags: string[];
  imageUrl: string;
}

const CreatorProfileCard = ({ username, fullName, tags, imageUrl }: CreatorProfileCardProps) => {
  return (
    <Card className="overflow-hidden border-sortmy-gray/30 bg-sortmy-gray/10 hover:bg-sortmy-gray/20 transition-colors card-glow">
      <div className="relative">
        <AspectRatio ratio={1 / 1}>
          <img
            src={imageUrl}
            alt={fullName}
            className="w-full h-full object-cover"
          />
        </AspectRatio>
        <div className="absolute top-3 right-3 bg-black/60 backdrop-blur-sm rounded-full p-1.5">
          <VideoIcon className="w-4 h-4 text-sortmy-blue" />
        </div>
      </div>

      <div className="p-4">
        <div className="flex items-center mb-2">
          <div className="w-8 h-8 rounded-full bg-sortmy-blue/20 flex items-center justify-center mr-2">
            <User className="w-4 h-4 text-sortmy-blue" />
          </div>
          <div>
            <h3 className="font-medium">{fullName}</h3>
            <p className="text-sm text-gray-400">@{username}</p>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mt-3 mb-4">
          {tags.map((tag, index) => (
            <div key={index} className="text-xs px-2 py-1 rounded-full bg-sortmy-gray/30 text-gray-300 flex items-center">
              <Palette className="w-3 h-3 mr-1 text-sortmy-blue" />
              {tag}
            </div>
          ))}
        </div>

        <Link to={`/creator/${username}`}>
          <Button variant="outline" size="sm" className="w-full mt-2 border-sortmy-gray/50">
            View Portfolio
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default PortfolioFeatureSection;


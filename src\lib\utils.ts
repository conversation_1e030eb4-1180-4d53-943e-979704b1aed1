import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import type { ImageSizeType } from "@/types/tools";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatWebsiteUrl = (url: string): string => {
  if (!url) return "";
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }
  return url;
};

export const validateImageSize = (size: string): ImageSizeType => {
  const validSizes: ImageSizeType[] = ['contain', 'cover', '85%', '75%', '50%'];
  return validSizes.includes(size as ImageSizeType) ? size as ImageSizeType : 'contain';
};

export const getSecureStorageUrl = (url: string | undefined): string => {
  if (!url) return '';
  return url.startsWith('https://') ? url : url.replace('http://', 'https://');
};
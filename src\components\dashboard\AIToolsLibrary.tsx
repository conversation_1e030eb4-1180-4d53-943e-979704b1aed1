import { useState, useEffect } from 'react';
import { collection, getDocs, doc, updateDoc, arrayUnion } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Search, Filter } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AITool } from '@/types';
import { ToolIcon } from '@/components/tools/ToolIcon';
import { MissionTracker } from '@/services/missionService';

// Helper functions
const processTags = (tags: string | string[] | undefined): string[] => {
  if (!tags) return [];
  if (Array.isArray(tags)) return tags;
  return tags.split(',').map((tag: string) => tag.trim()).filter(Boolean);
};

const getFullURL = (url: string | undefined): string => {
  if (!url) return '';
  return url.startsWith('http') ? url : `https://${url}`;
};

export const AIToolsLibrary: React.FC = () => {
  const [tools, setTools] = useState<AITool[]>([]);
  const [filteredTools, setFilteredTools] = useState<AITool[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [allTags, setAllTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [savingToolIds, setSavingToolIds] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const [filtersExpanded, setFiltersExpanded] = useState(true);

  const auth = useAuth();
  const toastContext = useToast();

  useEffect(() => {
    const fetchTools = async () => {
      try {
        setLoading(true);
        const toolsCollection = collection(db, 'aiTools');
        const toolsSnapshot = await getDocs(toolsCollection);

        const toolsList: AITool[] = [];
        const tagsSet = new Set<string>();

        toolsSnapshot.forEach((docSnapshot) => {
          const toolData = docSnapshot.data() as Omit<AITool, 'id'>;
          const processedTags = processTags(toolData.tags);
          const tool: AITool = {
            ...toolData,
            id: docSnapshot.id,
            tags: processedTags
          };

          processedTags.forEach((tag: string) => tagsSet.add(tag));
          toolsList.push(tool);
        });

        setTools(toolsList);
        setFilteredTools(toolsList);
        setAllTags(Array.from(tagsSet));
      } catch (error) {
        console.error('Error fetching AI tools:', error);
        toastContext.toast({
          title: 'Error',
          description: 'Failed to load AI tools. Please try again later.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    void fetchTools();
  }, [toastContext]);

  useEffect(() => {
    let filtered = [...tools];

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((tool: AITool) =>
        tool.name?.toLowerCase().includes(query) ||
        tool.description?.toLowerCase().includes(query) ||
        tool.useCase?.toLowerCase().includes(query)
      );
    }

    if (selectedTags.length > 0) {
      filtered = filtered.filter((tool: AITool) => {
        const toolTags = processTags(tool.tags);
        return selectedTags.every((tag: string) => toolTags.includes(tag));
      });
    }

    if (activeTab === 'free') {
      filtered = filtered.filter((tool: AITool) =>
        tool.pricing === 'Free'
      );
    } else if (activeTab === 'freemium') {
      filtered = filtered.filter((tool: AITool) =>
        tool.pricing === 'Freemium'
      );
    } else if (activeTab === 'paid') {
      filtered = filtered.filter((tool: AITool) =>
        tool.pricing === 'Paid' || tool.pricing === 'Subscription'
      );
    }

    setFilteredTools(filtered);
  }, [searchQuery, selectedTags, tools, activeTab]);

  const addToolToTracker = async (tool: AITool) => {
    if (!auth.user) {
      toastContext.toast({
        title: 'Authentication Required',
        description: 'Please sign in to add tools to your tracker.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setSavingToolIds((prev) => [...prev, tool.id]);

      const processedTags = processTags(tool.tags);
      const userRef = doc(db, 'users', auth.user.uid);

      await updateDoc(userRef, {
        toolTracker: arrayUnion({
          id: tool.id,
          name: tool.name,
          useCase: tool.useCase,
          description: tool.description,
          logoUrl: tool.logoUrl || tool.logoLink,
          website: getFullURL(tool.website || tool.websiteLink),
          tags: processedTags,
          pricing: tool.pricing,
          excelsAt: tool.excelsAt,
          imageSettings: tool.imageSettings,
          addedAt: new Date().toISOString(),
        })
      });

      // Track mission progress for adding tool to library
      try {
        await MissionTracker.addToolToLibrary(auth.user.uid);
      } catch (missionError) {
        console.warn('Error tracking tool addition mission:', missionError);
        // Don't fail the tool addition if mission tracking fails
      }

      toastContext.toast({
        title: 'Success',
        description: `${tool.name} has been added to your tool tracker.`,
        variant: 'default',
      });
    } catch (error) {
      console.error('Error adding tool to tracker:', error);
      toastContext.toast({
        title: 'Error',
        description: 'Failed to add tool to your tracker. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSavingToolIds((prev) => prev.filter((id) => id !== tool.id));
    }
  };

  const toggleTag = (tag: string) => {
    setSelectedTags((prev) =>
      prev.includes(tag)
        ? prev.filter((t) => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSelectedTags([]);
    setSearchQuery('');
    setActiveTab('all');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">AI Tools Library</h2>
        <div className="flex items-center space-x-4">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search tools..."
            className="w-64 bg-sortmy-darker text-white placeholder:text-sortmy-gray"
          />
          <Button
            variant="outline"
            onClick={() => setFiltersExpanded(!filtersExpanded)}
            className="text-white hover:text-sortmy-blue"
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>
        </div>
      </div>

      {filtersExpanded && (
        <div className="space-y-4 bg-sortmy-darker p-4 rounded-lg">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full bg-sortmy-dark">
              <TabsTrigger value="all" className="flex-1">All</TabsTrigger>
              <TabsTrigger value="free" className="flex-1">Free</TabsTrigger>
              <TabsTrigger value="freemium" className="flex-1">Freemium</TabsTrigger>
              <TabsTrigger value="paid" className="flex-1">Paid</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex flex-wrap gap-2">
            {allTags.map((tag: string) => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? 'default' : 'outline'}
                className="cursor-pointer hover:bg-sortmy-blue/20"
                onClick={() => toggleTag(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>

          {(selectedTags.length > 0 || searchQuery) && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="text-white hover:text-sortmy-blue"
            >
              Clear Filters
            </Button>
          )}
        </div>
      )}

      <div className="w-full">
        {loading ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="w-full aspect-square bg-sortmy-darker rounded-lg mb-2" />
                <div className="h-4 bg-sortmy-darker rounded w-3/4 mb-2" />
                <div className="h-3 bg-sortmy-darker rounded w-1/2" />
              </div>
            ))}
          </div>
        ) : filteredTools.length === 0 ? (
          <div className="text-center py-12">
            <Search className="mx-auto h-12 w-12 text-sortmy-gray mb-4" />
            <h3 className="text-xl font-medium mb-2 text-white">No tools found</h3>
            <p className="text-sortmy-gray text-center max-w-md mx-auto">
              We couldn't find any tools matching your criteria. Try adjusting your filters or search query.
            </p>
            {(selectedTags.length > 0 || searchQuery) && (
              <Button
                variant="outline"
                className="mt-4"
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
            {filteredTools.map((tool) => (
              <div key={tool.id}>
                <ToolIcon
                  tool={tool}
                  onAdd={() => addToolToTracker(tool)}
                  isSaving={savingToolIds.includes(tool.id)}
                />
                <div className="mt-1 flex justify-center">
                  <Badge
                    variant="outline"
                    className={`text-xs ${tool.pricing === 'Free'
                      ? 'bg-green-500/20 text-green-400 border-green-500/30'
                      : tool.pricing === 'Freemium'
                        ? 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                        : 'bg-purple-500/20 text-purple-400 border-purple-500/30'
                      }`}
                  >
                    {tool.pricing}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AIToolsLibrary;


import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { MuxPlayer } from '@/components/video/MuxPlayer';
import { isMuxUrl, extractMuxPlaybackId } from '@/lib/mux';

interface PortfolioVideoProps {
    mediaUrl: string;
    thumbnailUrl?: string;
    title: string;
    isReel?: boolean;
    onPlayChange?: (isPlaying: boolean) => void;
    debug?: boolean;
    autoPlay?: boolean; // Added for Instagram-like feed
    muted?: boolean; // NEW: allow parent to control mute
}

const DEFAULT_THUMBNAIL = '/images/video-placeholder.jpg';

export function PortfolioVideo({
    mediaUrl,
    thumbnailUrl,
    title,
    isReel = false,
    onPlayChange,
    debug = false,
    autoPlay = false,
    muted = true, // NEW: default to true
}: PortfolioVideoProps) {
    const [isPlaying, setIsPlaying] = useState(false);
    const [showControls, setShowControls] = useState(false);
    const [videoError, setVideoError] = useState<string | null>(null);
    const videoRef = useRef<HTMLVideoElement>(null);

    // Show controls on hover/focus, hide on mouse leave/blur
    const handleMouseEnter = () => {
        setShowControls(true);
        if (videoRef.current && videoRef.current.paused) {
            videoRef.current.muted = true;
            videoRef.current.play().catch(() => { });
            setIsPlaying(true);
        }
    };

    const handleMouseLeave = () => {
        setShowControls(false);
        if (videoRef.current && !videoRef.current.paused) {
            videoRef.current.pause();
            setIsPlaying(false);
        }
    };

    const handleVideoClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (videoRef.current) {
            if (videoRef.current.paused) {
                videoRef.current.play().catch(() => { });
                setIsPlaying(true);
            } else {
                videoRef.current.pause();
                setIsPlaying(false);
            }
        }
    };

    const getValidThumbnailUrl = (url?: string): string => {
        if (!url || url.includes('.m3u8')) {
            return DEFAULT_THUMBNAIL;
        }
        return url;
    };

    const handlePlay = () => {
        setIsPlaying(true);
        if (onPlayChange) onPlayChange(true);
    };

    const handlePause = () => {
        setIsPlaying(false);
        if (onPlayChange) onPlayChange(false);
    };

    const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
        const msg = e.currentTarget.error?.message || 'Unknown error';
        setVideoError(`Video failed to load: ${msg}`);
        if (debug) console.error('Video error', msg, mediaUrl);
    };

    // Only show debug overlay if both debug prop is true and in development mode
    const showDebugOverlay = debug && process.env.NODE_ENV === 'development';

    const isVideoUrl =
        isMuxUrl(mediaUrl) ||
        mediaUrl.endsWith('.m3u8') ||
        mediaUrl.includes('livepeercdn.com') ||
        mediaUrl.includes('lp-playback.studio');

    const resolvedThumbnail = isVideoUrl
        ? thumbnailUrl || DEFAULT_THUMBNAIL
        : getValidThumbnailUrl(thumbnailUrl);

    const playbackId = isMuxUrl(mediaUrl) ? extractMuxPlaybackId(mediaUrl) : undefined;
    const hasValidMuxPlayback = Boolean(playbackId && playbackId.length > 5);

    // Debug logging
    console.log('PortfolioVideo Debug', { mediaUrl, isVideoUrl, hasValidMuxPlayback, autoPlay });

    // Auto-play video when autoPlay becomes true and videoRef is available
    useEffect(() => {
        if (autoPlay && videoRef.current) {
            videoRef.current.muted = true;
            videoRef.current.play().catch(() => { });
        } else if (!autoPlay && videoRef.current) {
            videoRef.current.pause();
        }
    }, [autoPlay]);

    // Only handle Mux, HLS, and fallback <video>
    if (isVideoUrl && hasValidMuxPlayback) {
        return (
            <div
                className="relative w-full h-full"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
                onClick={handleVideoClick}
            >
                <MuxPlayer
                    src={mediaUrl}
                    playbackId={playbackId as string | undefined}
                    poster={resolvedThumbnail}
                    title={title}
                    muted={muted}
                    autoPlay={autoPlay}
                    loop={true}
                    controls={debug ? true : showControls}
                    onPlay={handlePlay}
                    onPause={handlePause}
                    className={isReel ? 'aspect-[9/16]' : ''}
                />

                {showDebugOverlay && videoError && (
                    <div className="absolute top-0 left-0 z-50 bg-red-900/90 text-xs text-red-200 p-2 rounded shadow max-w-xs break-words">
                        <b>Video Error:</b> {videoError}
                    </div>
                )}
            </div>
        );
    }

    // Fallback for non-Mux, non-GDrive videos
    return (
        <div className="relative w-full h-full"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            onClick={handleVideoClick}
            tabIndex={0}
            onFocus={() => setShowControls(true)}
            onBlur={() => setShowControls(false)}
        >
            {showDebugOverlay && videoError && (
                <div className="absolute top-0 left-0 z-50 bg-red-900/90 text-xs text-red-200 p-2 rounded shadow max-w-xs break-words">
                    <b>Video Error:</b> {videoError}
                </div>
            )}
            <video
                ref={videoRef}
                src={mediaUrl}
                className="w-full h-full object-cover"
                muted={muted}
                loop
                playsInline
                controls
                controlsList="nodownload nofullscreen"
                poster={thumbnailUrl || DEFAULT_THUMBNAIL}
                onPlay={handlePlay}
                onPause={handlePause}
                onClick={handleVideoClick}
                onError={handleVideoError}
                autoPlay={autoPlay}
            />
            {(showControls || isPlaying) && (
                <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between bg-black/50 rounded-md px-2 py-1 z-10">
                    <button onClick={handleVideoClick} className="p-1 hover:bg-white/10 rounded-full" tabIndex={-1}>
                        {isPlaying ? <Pause className="w-4 h-4 text-white" /> : <Play className="w-4 h-4 text-white" />}
                    </button>
                    {/* Mute/unmute button is now controlled by parent, so just show icon */}
                    <span className="p-1 rounded-full">
                        {muted ? <VolumeX className="w-4 h-4 text-white" /> : <Volume2 className="w-4 h-4 text-white" />}
                    </span>
                </div>
            )}
        </div>
    );
}

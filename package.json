{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --mode development", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup": "node scripts/setup.js", "typecheck": "tsc --noEmit", "firebase": "firebase", "deploy": "npm run build && firebase deploy", "clear-cache": "node clear-cache.js", "fresh-start": "npm run clear-cache && npm install && npm run dev"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capacitor/keyboard": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@firebase/auth": "^1.10.0", "@hookform/resolvers": "^3.3.4", "@livepeer/react": "^4.3.5", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.24.1", "@types/uuid": "^10.0.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.1.1", "date-fns": "^3.3.1", "embla-carousel-react": "^8.6.0", "firebase": "^11.6.0", "firebase-admin": "^12.0.0", "framer-motion": "^12.6.3", "hls.js": "^1.6.5", "input-otp": "^1.4.2", "livepeer": "^3.5.0", "lovable-tagger": "^1.1.8", "lucide-react": "^0.341.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.6.5", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.4.2", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.5.2", "react-image-crop": "^11.0.10", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.22.2", "react-toastify": "^11.0.5", "recharts": "^2.15.2", "sonner": "^1.4.3", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "viem": "^2.31.0", "wagmi": "^2.15.6", "zod": "^3.22.4"}, "devDependencies": {"@firebase/app-types": "^0.9.3", "@firebase/auth-types": "^0.13.0", "@firebase/database-types": "^1.0.10", "@firebase/firestore-types": "^3.0.3", "@firebase/performance-types": "^0.2.3", "@firebase/storage-types": "^0.8.3", "@tailwindcss/typography": "^0.5.10", "@types/firebase": "^3.2.3", "@types/gapi": "^0.0.47", "@types/gapi.client.drive": "^3.0.15", "@types/google.accounts": "^0.0.15", "@types/node": "^20.11.24", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "firebase-tools": "^13.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-pwa": "^1.0.0"}}
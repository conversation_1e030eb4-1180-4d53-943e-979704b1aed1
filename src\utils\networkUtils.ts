import { enableNetwork, disableNetwork } from 'firebase/firestore';
import { FirebaseError } from 'firebase/app';
import { db } from '@/lib/firebase';

export interface NetworkManager {
    getNetworkStatus: () => boolean;
    executeWithRetry: <T>(operation: () => Promise<T>, context: string) => Promise<T>;
}

interface NetworkError {
    message?: string;
    code?: string;
    name?: string;
}

class DefaultNetworkManager implements NetworkManager {
    private isOnline: boolean;
    private maxRetries: number = 3;
    private baseDelay: number = 1000;

    constructor() {
        this.isOnline = navigator.onLine;
        this.setupListeners();
    }

    private setupListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.enableFirestore();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.disableFirestore();
        });
    }

    private async enableFirestore() {
        try {
            await enableNetwork(db);
            console.log('Firestore network enabled');
        } catch (error) {
            console.error('Error enabling Firestore network:', error);
        }
    }

    private async disableFirestore() {
        try {
            await disableNetwork(db);
            console.log('Firestore network disabled');
        } catch (error) {
            console.error('Error disabling Firestore network:', error);
        }
    }

    getNetworkStatus(): boolean {
        return this.isOnline;
    }

    private isWebChannelError(error: unknown): boolean {
        if (error instanceof FirebaseError) {
            return error.code?.includes('unavailable') || false;
        }

        const networkError = error as NetworkError;
        return !!(
            networkError.message?.includes('WebChannel') ||
            networkError.code?.includes('unavailable') ||
            networkError.name === 'FirebaseError'
        );
    }

    async executeWithRetry<T>(operation: () => Promise<T>, context: string): Promise<T> {
        const executeWithBackoff = async (attempt: number): Promise<T> => {
            try {
                if (!this.isOnline) {
                    throw new Error('Network is offline');
                }

                return await operation();
            } catch (error) {
                if (this.isWebChannelError(error) && attempt < this.maxRetries) {
                    // Calculate delay with exponential backoff
                    const delay = Math.min(this.baseDelay * Math.pow(2, attempt), 10000);
                    console.log(`${context}: Attempt ${attempt + 1}/${this.maxRetries} failed, retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return executeWithBackoff(attempt + 1);
                }

                // If we've exhausted retries or it's not a WebChannel error, rethrow
                throw error;
            }
        };

        return executeWithBackoff(0);
    }
}

// Export a singleton instance
export const networkManager = new DefaultNetworkManager();

// Utility function for wrapping Firestore operations
export const withNetworkRetry = async <T>(
    operation: () => Promise<T>,
    context: string
): Promise<T> => {
    return networkManager.executeWithRetry(operation, context);
};

{"version": 3, "file": "web-qcnismWP.js", "sources": ["../../node_modules/@capacitor/preferences/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class PreferencesWeb extends WebPlugin {\n    constructor() {\n        super(...arguments);\n        this.group = 'CapacitorStorage';\n    }\n    async configure({ group }) {\n        if (typeof group === 'string') {\n            this.group = group;\n        }\n    }\n    async get(options) {\n        const value = this.impl.getItem(this.applyPrefix(options.key));\n        return { value };\n    }\n    async set(options) {\n        this.impl.setItem(this.applyPrefix(options.key), options.value);\n    }\n    async remove(options) {\n        this.impl.removeItem(this.applyPrefix(options.key));\n    }\n    async keys() {\n        const keys = this.rawKeys().map(k => k.substring(this.prefix.length));\n        return { keys };\n    }\n    async clear() {\n        for (const key of this.rawKeys()) {\n            this.impl.removeItem(key);\n        }\n    }\n    async migrate() {\n        var _a;\n        const migrated = [];\n        const existing = [];\n        const oldprefix = '_cap_';\n        const keys = Object.keys(this.impl).filter(k => k.indexOf(oldprefix) === 0);\n        for (const oldkey of keys) {\n            const key = oldkey.substring(oldprefix.length);\n            const value = (_a = this.impl.getItem(oldkey)) !== null && _a !== void 0 ? _a : '';\n            const { value: currentValue } = await this.get({ key });\n            if (typeof currentValue === 'string') {\n                existing.push(key);\n            }\n            else {\n                await this.set({ key, value });\n                migrated.push(key);\n            }\n        }\n        return { migrated, existing };\n    }\n    async removeOld() {\n        const oldprefix = '_cap_';\n        const keys = Object.keys(this.impl).filter(k => k.indexOf(oldprefix) === 0);\n        for (const oldkey of keys) {\n            this.impl.removeItem(oldkey);\n        }\n    }\n    get impl() {\n        return window.localStorage;\n    }\n    get prefix() {\n        return this.group === 'NativeStorage' ? '' : `${this.group}.`;\n    }\n    rawKeys() {\n        return Object.keys(this.impl).filter(k => k.indexOf(this.prefix) === 0);\n    }\n    applyPrefix(key) {\n        return this.prefix + key;\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["PreferencesWeb", "WebPlugin", "group", "options", "k", "key", "_a", "migrated", "existing", "oldprefix", "keys", "oldkey", "value", "currentValue"], "mappings": "uCACO,MAAMA,UAAuBC,CAAU,CAC1C,aAAc,CACV,MAAM,GAAG,SAAS,EAClB,KAAK,MAAQ,kBACrB,CACI,MAAM,UAAU,CAAE,MAAAC,GAAS,CACnB,OAAOA,GAAU,WACjB,KAAK,MAAQA,EAEzB,CACI,MAAM,IAAIC,EAAS,CAEf,MAAO,CAAE,MADK,KAAK,KAAK,QAAQ,KAAK,YAAYA,EAAQ,GAAG,CAAC,CAC7C,CACxB,CACI,MAAM,IAAIA,EAAS,CACf,KAAK,KAAK,QAAQ,KAAK,YAAYA,EAAQ,GAAG,EAAGA,EAAQ,KAAK,CACtE,CACI,MAAM,OAAOA,EAAS,CAClB,KAAK,KAAK,WAAW,KAAK,YAAYA,EAAQ,GAAG,CAAC,CAC1D,CACI,MAAM,MAAO,CAET,MAAO,CAAE,KADI,KAAK,QAAO,EAAG,IAAIC,GAAKA,EAAE,UAAU,KAAK,OAAO,MAAM,CAAC,CACrD,CACvB,CACI,MAAM,OAAQ,CACV,UAAWC,KAAO,KAAK,UACnB,KAAK,KAAK,WAAWA,CAAG,CAEpC,CACI,MAAM,SAAU,CACZ,IAAIC,EACJ,MAAMC,EAAW,CAAE,EACbC,EAAW,CAAE,EACbC,EAAY,QACZC,EAAO,OAAO,KAAK,KAAK,IAAI,EAAE,OAAON,GAAKA,EAAE,QAAQK,CAAS,IAAM,CAAC,EAC1E,UAAWE,KAAUD,EAAM,CACvB,MAAML,EAAMM,EAAO,UAAUF,EAAU,MAAM,EACvCG,GAASN,EAAK,KAAK,KAAK,QAAQK,CAAM,KAAO,MAAQL,IAAO,OAASA,EAAK,GAC1E,CAAE,MAAOO,CAAc,EAAG,MAAM,KAAK,IAAI,CAAE,IAAAR,EAAK,EAClD,OAAOQ,GAAiB,SACxBL,EAAS,KAAKH,CAAG,GAGjB,MAAM,KAAK,IAAI,CAAE,IAAAA,EAAK,MAAAO,CAAK,CAAE,EAC7BL,EAAS,KAAKF,CAAG,EAEjC,CACQ,MAAO,CAAE,SAAAE,EAAU,SAAAC,CAAU,CACrC,CACI,MAAM,WAAY,CACd,MAAMC,EAAY,QACZC,EAAO,OAAO,KAAK,KAAK,IAAI,EAAE,OAAON,GAAKA,EAAE,QAAQK,CAAS,IAAM,CAAC,EAC1E,UAAWE,KAAUD,EACjB,KAAK,KAAK,WAAWC,CAAM,CAEvC,CACI,IAAI,MAAO,CACP,OAAO,OAAO,YACtB,CACI,IAAI,QAAS,CACT,OAAO,KAAK,QAAU,gBAAkB,GAAK,GAAG,KAAK,KAAK,GAClE,CACI,SAAU,CACN,OAAO,OAAO,KAAK,KAAK,IAAI,EAAE,OAAOP,GAAKA,EAAE,QAAQ,KAAK,MAAM,IAAM,CAAC,CAC9E,CACI,YAAYC,EAAK,CACb,OAAO,KAAK,OAASA,CAC7B,CACA", "x_google_ignoreList": [0]}
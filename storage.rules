rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Avatar uploads
    match /avatars/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    // Tool logos uploaded by users
    match /tool_logos/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    // Portfolio media files
    match /portfolio/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.uid == userId &&
        request.resource.size < 50 * 1024 * 1024 && // 50MB limit
        (request.resource.contentType.matches('image/.*') ||
         request.resource.contentType.matches('video/.*'));
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    // Portfolio draft files
    match /portfolio-drafts/{userId}/{allPaths=**} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null &&
        request.auth.uid == userId &&
        request.resource.size < 50 * 1024 * 1024 && // 50MB limit
        (request.resource.contentType.matches('image/.*') ||
         request.resource.contentType.matches('video/.*'));
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    // Portfolio archived files
    match /portfolio-archives/{userId}/{allPaths=**} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null &&
        request.auth.uid == userId &&
        request.resource.size < 50 * 1024 * 1024; // 50MB limit
      allow delete: if request.auth != null && request.auth.uid == userId;
    }    // Toolkit logos
    match /toolkit-logos/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.auth.uid == userId &&
        request.resource.size < 5 * 1024 * 1024 && // 5MB limit
        request.resource.contentType.matches('image/.*');
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    // Admin tool logos
    match /tool-logos/{toolId}/{fileName} {
      allow read: if true;
      allow write: if request.auth != null &&
        (request.auth.token.admin == true ||
         (request.resource.size < 5 * 1024 * 1024 &&
          request.resource.contentType.matches('image/.*')));
      allow delete: if request.auth != null && request.auth.token.admin == true;
    }

    // Temporary upload paths for processing
    match /temp/{userId}/{allPaths=**} {
      allow read, write, delete: if request.auth != null && request.auth.uid == userId;
    }

    // Course media files (thumbnails, lesson images, etc)
    match /course-media/{courseId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.resource.size < 50 * 1024 * 1024 && // 50MB limit
        (request.resource.contentType.matches('image/.*') ||
         request.resource.contentType.matches('video/.*'));
      allow delete: if request.auth != null;
    }

    // Course lesson media files
    match /course-lessons/{courseId}/{lessonId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.resource.size < 100 * 1024 * 1024 && // 100MB limit for lesson videos
        (request.resource.contentType.matches('image/.*') ||
         request.resource.contentType.matches('video/.*'));
      allow delete: if request.auth != null;
    }

    // AI tool library logos (admin only)
    match /ai-tool-logos/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
      allow delete: if request.auth != null && request.auth.token.admin == true;
    }

    // Cleanup service rules - only admins can delete orphaned files
    match /{allPaths=**} {
      allow delete: if request.auth != null &&
        (request.auth.token.admin == true ||
         request.auth.token.cleanup == true);
    }
  }
}